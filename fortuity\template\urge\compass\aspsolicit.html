{extend name="/base"/}
{block name="main"}
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;}.portlet-title{display:flex;justify-content:space-between;align-items:center;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid #f0f0f0;}.caption{font-size:16px;color:#333;font-weight:500;}.actions .action-btn + .action-btn,.actions a + .action-btn{margin-left:10px;}.am-form-label{text-align:right;color:#333;font-weight:normal;}.tpl-form-input{display:block;width:100%;height:36px;padding:8px 12px;font-size:14px;line-height:1.5;color:#495057;background-color:#fff;background-clip:padding-box;border:1px solid #ced4da;border-radius:4px;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;}.tpl-form-input:focus{color:#495057;background-color:#fff;border-color:#80bdff;outline:0;box-shadow:0 0 0 0.2rem rgba(35,183,229,.25);}.tpl-form-input[disabled],.tpl-form-input[readonly]{background-color:#e9ecef;opacity:1;cursor:not-allowed;}textarea.tpl-form-input{height:auto;min-height:90px;resize:vertical;}.action-btn{display:inline-block;padding:5px 12px;font-size:13px;font-weight:normal;line-height:1.4;text-align:center;cursor:pointer;border:1px solid #ced4da;border-radius:4px;transition:all .3s;background-color:#fff;}.action-btn:hover{background-color:#f8f9fa;}.action-btn.btn-approve{color:#fff;background-color:#28a745;border-color:#28a745;}.action-btn.btn-approve:hover{background-color:#218838;}.action-btn.btn-reject{color:#fff;background-color:#dc3545;border-color:#dc3545;}.action-btn.btn-reject:hover{background-color:#c82333;}.action-btn.btn-return{color:#212529;background-color:#f8f9fa;border-color:#ced4da;}.action-btn.btn-return:hover{background-color:#e2e6ea;}.am-badge{display:inline-block;padding:.3em .6em;font-size:85%;font-weight:700;line-height:1;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25rem;}.am-badge-success{color:#fff;background-color:#28a745;}.am-badge-warning{color:#fff;background-color:#ffc107;}.am-badge-danger{color:#fff;background-color:#dc3545;}.form-img-preview{width:100px;height:100px;border-radius:4px;border:1px solid #ced4da;object-fit:cover;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-circle-o"></span> 圈子审核详情
        </div>
        <div class="actions">
            {if $list.realm_status==0}
            <button type="button" class="action-btn btn-approve" onclick="satypical('1');">通过审核</button>
            <button type="button" class="action-btn btn-reject" onclick="satypical('3');">拒绝通过</button>
            {/if}
            <a href="{:url('solicit')}">
                <button type="button" class="action-btn btn-return">返回</button>
            </a>
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label for="rename" class="am-u-sm-3 am-form-label">圈子名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="rename" class="tpl-form-input" value="{$list.realm_name}" disabled>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label for="shion" class="am-u-sm-3 am-form-label">圈子图标</label>
                        <div class="am-u-sm-9">
                            <img src="{$list.realm_icon}" id="shion" class="form-img-preview" onerror="this.src='static/disappear/default.png'"/>
                            <input type="hidden" value="{$list.realm_icon}" name="sngimg">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label for="name" class="am-u-sm-3 am-form-label">圈子类型</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" class="tpl-form-input" value="{$list.name}" disabled>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label for="synopsis" class="am-u-sm-3 am-form-label">圈子简介</label>
                        <div class="am-u-sm-9">
                            <textarea rows="5" id="synopsis" class="tpl-form-input" disabled placeholder="请输入圈子简介">{$list.realm_synopsis}</textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label for="nick_name" class="am-u-sm-3 am-form-label">圈子申请用户</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="nick_name" class="tpl-form-input" disabled value="{$list.user_nick_name|emoji_decode|filter_emoji}">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label for="openid" class="am-u-sm-3 am-form-label">用户openid</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="openid" class="tpl-form-input" disabled value="{$list.user_wechat_open_id}">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label for="ustatus" class="am-u-sm-3 am-form-label">用户状态</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="ustatus" class="tpl-form-input" disabled value="{if $list.ustatus==1}正常{else}封禁{/if}">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label for="is_gnaw" class="am-u-sm-3 am-form-label">是否申请圈主</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="is_gnaw" class="tpl-form-input" disabled value="{if $list.is_gnaw_qulord==1}是{else}否{/if}">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label for="attention" class="am-u-sm-3 am-form-label">是否开启权限</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="attention" class="tpl-form-input" disabled value="{if $list.attention==1}是{else}否{/if}">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label for="solicit_origin" class="am-u-sm-3 am-form-label">申请原因</label>
                        <div class="am-u-sm-9">
                            <textarea rows="5" id="solicit_origin" class="tpl-form-input" disabled placeholder="请输入圈子简介">{$list.solicit_origin}</textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label for="ustatus" class="am-u-sm-3 am-form-label">圈子提交申请次数</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="solicit_rate" class="tpl-form-input" disabled value="{$list.solicit_rate}">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子状态</label>
                        <div class="am-u-sm-9">
                            {if $list.realm_status == 0}
                            <span class="am-badge am-badge-warning">待审核</span>
                            {elseif $list.realm_status == 1}
                            <span class="am-badge am-badge-success">已通过</span>
                            {elseif $list.realm_status == 3}
                            <span class="am-badge am-badge-danger">已拒绝</span>
                            {elseif $list.realm_status == 4}
                            <span class="am-badge am-badge-danger">数据重复</span>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    function satypical(pical) {
        var uata = {};
        uata['uplid'] = '{$list.id}';
        uata['pical'] = pical;
        if (pical == 1) {
            layer.confirm("您要<span style='color: green;'>同意</span>该圈子通过审核吗？", {
                btn: ['同意', '取消'], 'title': '操作提示'
            }, function () {
                equitable(uata);
            }, function () {
                layer.closeAll();
            });
        } else {
            layer.confirm("您要<span style='color: red;'>拒绝</span>该圈子通过审核吗？", {
                btn: ['确定', '取消'], 'title': '操作提示'
            }, function () {
                equitable(uata);
            }, function () {
                layer.closeAll();
            });
        }
    }


    function equitable(uata) {
        $.ajax({
            type: "post",
            url: "{:url('compass/aspsolicit')}",
            data: uata,
            dataType: 'json',
            traditional: true,
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1000}, function () {
                        location.reload();
                    });
                }
            }
        });
    }


</script>
{/block}