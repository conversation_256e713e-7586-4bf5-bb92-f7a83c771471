{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-code {margin-right: 5px;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;vertical-align: middle;color: #666;line-height: 1.6;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .user-info {display: flex;align-items: center;justify-content: flex-start; text-align: left; }
    .user-info img {width: 40px;height: 40px;border-radius: 50%;margin-right: 10px;}
    .search-bar {display: flex; flex-wrap: wrap; align-items: center; margin-bottom: 15px; gap: 15px;}
    .search-item {display: flex; align-items: center;}
    .search-item .search-label {margin-right: 8px; font-size: 13px; color: #555;}
    .search-item .search-input {height: 32px; width: 150px; padding: 0 10px; border: 1px solid #e8e8e8; border-radius: 4px; background: #fafafa; transition: all 0.3s;}
    .search-item .search-input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .search-btn {height: 32px; padding: 0 15px; background: #23b7e5; color: #fff; border: none; border-radius: 4px; cursor: pointer; transition: all 0.3s;}
    .search-btn:hover {background: #1e9ebc;}
    .add-btn {height: 32px; padding: 0 15px; background: #5cb85c; color: #fff; border: none; border-radius: 4px; cursor: pointer; transition: all 0.3s;}
    .add-btn:hover {background: #4cae4c;}
</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-code"></span> 私信列表
        </div>
        <button class="add-btn" @click="dialogVisible = true">
            <span class="am-icon-plus"></span> 新增私信
        </button>
    </div>
    <div class="tpl-block">
        <div id="search-list" class="search-bar">
            <div class="search-item">
                <label class="search-label">发送用户UID</label>
                <input type="text" name="sendUid" value="{$sendUid}" class="search-input">
            </div>
            <div class="search-item">
                <label class="search-label">接收用户UID</label>
                <input type="text" name="receiveUid" value="{$receiveUid}" class="search-input">
            </div>
            <div class="search-item">
                <label class="search-label">私信内容</label>
                <input type="text" name="content" value="{$content}" class="search-input">
            </div>
            <button class="search-btn" onclick="turtle();">
                <i class="am-icon-search"></i> 搜索
            </button>
        </div>

        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="25%">发送用户</th>
                            <th width="30%">私信内容</th>
                            <th width="25%">接收用户</th>
                            <th width="20%">私信时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                <div class="user-info">
                                    <img src="{$vo.se_user.user_head_sculpture}" alt="avatar">
                                    {if $vo.se_user.uvirtual == 0}
                                    <a href="{:url('user/index')}&openid={$vo.se_user.user_wechat_open_id}&page=1"
                                       title="{$vo.se_user.user_nick_name|emoji_decode}" target="_blank">
                                        {$vo.se_user.user_nick_name|emoji_decode|subtext=8}
                                    </a>
                                    {else}
                                    <a href="{:url('user/theoretic')}&hazy_name={$vo.se_user.user_nick_name|filter_emoji}&page=1"
                                       target="_blank" title="{$vo.se_user.user_nick_name|emoji_decode}">
                                        {$vo.se_user.user_nick_name|emoji_decode|subtext=8}
                                    </a>
                                    {/if}
                                </div>
                            </td>
                            <td style="text-align: left; vertical-align: middle;">
                                <a href="{:url('journal/letterRecord')}&rid={$vo.se_user_id}&uid={$vo.re_user_id}" target="_self" title="点击查看私信详情">
                                    {$vo.content|emoji_decode|subtext=18|$expressionHtml}
                                </a>
                            </td>
                            <td>
                                <div class="user-info">
                                    <img src="{$vo.re_user.user_head_sculpture}" alt="avatar">
                                    {if $vo.re_user.uvirtual == 0}
                                    <a href="{:url('user/index')}&openid={$vo.re_user.user_wechat_open_id}&page=1"
                                       title="{$vo.re_user.user_nick_name|emoji_decode}" target="_blank">
                                        {$vo.re_user.user_nick_name|emoji_decode|subtext=8}
                                    </a>
                                    {else}
                                    <a href="{:url('user/theoretic')}&hazy_name={$vo.re_user.user_nick_name|filter_emoji}&page=1"
                                       target="_blank" title="{$vo.re_user.user_nick_name|emoji_decode}">
                                        {$vo.re_user.user_nick_name|emoji_decode|subtext=8}
                                    </a>
                                    {/if}
                                </div>
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.le_time)}</td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
    <el-dialog title="新增私信" :visible.sync="dialogVisible" width="30%">
        <div class="am-modal-bd am-form am-form-horizontal" style="border: 0;">
            <div class="am-form-group">
                <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">发送用户UID</label>
                <div class="am-u-sm-8">
                    <input type="number" v-model="sendUid" class="tpl-form-input" placeholder="请输入发送用户UID">
                </div>
            </div>
            <div class="am-form-group">
                <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">接收用户UID</label>
                <div class="am-u-sm-8">
                    <input type="number" v-model="receiveUid" class="tpl-form-input" placeholder="请输入接收用户UID">
                </div>
            </div>
            <div class="am-form-group" style="position: relative;">
                <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">私信内容</label>
                <div class="am-u-sm-8">
                    <div style="position: absolute;top: 0;padding-left: 3px;width: 91%;background: #dddddd;height: 30px;opacity: 0.75;">
                        <ul style="display: flex;height: 30px;">
                            <li style="width: 30px;display: flex;justify-content: center;align-items: center;color: #1055ab;">
                                <span style="cursor: pointer;" title="点击添加表情"
                                      onclick="complicatedFunc(0);">
                                    <span class="am-icon-meh-o"></span>
                                </span>
                            </li>
                        </ul>
                    </div>
                    <textarea v-model="content" style="padding-top: 35px;resize: none;height: 180px;"></textarea>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="holdSave">确 定</el-button>
                <el-button @click="dialogVisible = false">取 消</el-button>
            </span>
        </div>
    </el-dialog>
</div>
{/block}
{block name="script"}
<script>

    var vm = new Vue({
        el: '#app',
        data() {
            return {
                dialogVisible: false,
                sendUid: '',
                receiveUid: '',
                content: '',
                onLock: false
            }
        },
        methods: {
            holdSave() {
                var _this = this;
                var setData = {};
                setData['sendUid'] = this.sendUid;
                if ($.trim(setData['sendUid']) === '') {
                    layer.msg('请输入发送用户UID');
                    return;
                }
                setData['receiveUid'] = this.receiveUid;
                if ($.trim(setData['receiveUid']) === '') {
                    layer.msg('请输入接收用户UID');
                    return;
                }
                if (setData['sendUid'] === setData['receiveUid']) {
                    layer.msg('发送用户UID和接收用户UID不能相同');
                    return;
                }
                setData['content'] = this.content;
                if ($.trim(setData['content']) === '') {
                    layer.msg('请输入私信内容');
                    return;
                }
                if (!this.onLock) {
                    this.onLock = true;
                    $.ajax({
                        type: "post",
                        url: "{:url('tissue/send_private_messages')}",
                        data: setData,
                        success: function (data) {
                            if (data.code > 0) {
                                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                    location.reload();
                                });
                            } else {
                                layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                    _this.onLock = false;
                                });
                            }
                        }
                    });
                }
            }
        }
    });

    var complicatedFunc = function (type) {
        switch (type) {
            case 0:
                layer.open({
                    type: 2,
                    anim: 2,
                    title: false,
                    area: ['350px', '360px'],
                    scrollbar: true,
                    closeBtn: false,
                    shadeClose: true,
                    content: ["{:url('tedious/emoji')}&type=" + type, 'no'],
                });
                break;
        }
    }

    var complicatedCallFunc = function (type, data) {
        switch (type) {
            case 0:
                vm.content += data['content'];
                break;
        }
    }

    function turtle() {
        var searchInput = $('#search-list .search-input');
        var n = 0;
        for (var i = 0; i < searchInput.length; i++) {
            if (searchInput[i].value.trim() !== '') {
                n++;
            }
        }
        if (n > 0) {
            location.href = `{:url('tissue/privateLetter')}&${searchInput.serialize()}&page={$page}`;
        } else {
            location.href = "{:url('tissue/privateLetter')}&page={$page}";
        }
    }

</script>
{/block}