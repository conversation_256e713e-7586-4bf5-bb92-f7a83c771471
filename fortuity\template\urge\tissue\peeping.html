{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;text-align:center;}.am-table > thead:first-child > tr:first-child > th{text-align:center;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 截屏记录
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="20%">用户信息</th>
                            <th width="10%">截屏场景</th>
                            <th width="50%">截屏路径</th>
                            <th width="10%">截屏来源（IP）</th>
                            <th width="10%">截屏时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle am-text-nowrap" style="display: flex;justify-content: center;">
                                <div style="width: 200px;text-align: left;">
                                    <span style="margin-right: 10px;">
                                        {if $vo.user.user_head_sculpture && $vo.user.user_head_sculpture !='/yl_welore/style/icon/default.png'}
                                        <img src="{$vo.user.user_head_sculpture}" style="width: 50px;height: 50px;border-radius: 50%;">
                                        {else}
                                        <img src="{:urlBridging('static/disappear/tourist.png')}" style="width: 50px;height: 50px;border-radius: 50%;">
                                        {/if}
                                    </span>
                                    {if $vo.user.uvirtual == 0}
                                    <a href="{:url('user/index')}&openid={$vo.user.user_wechat_open_id}&page=1" title="{$vo.user.user_nick_name|emoji_decode}" target="_blank">
                                        {$vo.user.user_nick_name|emoji_decode}
                                    </a>
                                    {else}
                                    <a href="{:url('user/theoretic')}&hazy_name={$vo.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user.user_nick_name|emoji_decode}">
                                        {$vo.user.user_nick_name|emoji_decode}
                                    </a>
                                    {/if}
                                </div>
                            </td>
                            <td class="am-text-middle am-text-nowrap am-text-nowrap">{$vo.scene_name}</td>
                            <td class="am-text-middle am-text-nowrap">{$vo.scene_path}</td>
                            <td class="am-text-middle am-text-nowrap">{$vo.location_ip}</td>
                            <td class="am-text-middle am-text-nowrap">{:date('Y-m-d H:i:s',$vo.add_time)}</td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>
    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('tissue/privateLetter')}&unid={$unid}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('tissue/privateLetter')}&unid={$unid}&page={$page}";
        }
    }

</script>
{/block}