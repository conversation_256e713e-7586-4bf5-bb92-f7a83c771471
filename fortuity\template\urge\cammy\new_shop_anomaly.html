{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 新增商品卡密
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-7 am-u-sm-push-2">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商品信息</label>
                        <div class="am-u-sm-9">
                            <input type="hidden" id="sid">
                            <div id="shopSelector" onclick="selectShop();"
                                 style="width:100%; background: #eee; color: #555; font-size:1.2rem; line-height: 1.2; border: 1px solid #ccc; padding: 1rem; cursor: pointer; display: inline-block;">
                                点击选择商品信息
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">卡密信息</label>
                        <div class="am-u-sm-9">
                            <textarea id="cardCode" style="height: 350px; resize: none;"></textarea>
                            <small style="color: red;">批量添加多张卡密请用回车（换行符）进行分割</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">卡密面值</label>
                        <div class="am-u-sm-9 am-u-end">
                            <input id="faceValue" type="text" oninput="grender(this,2)">
                            <small style="color: brown;">例如卡密可以 兑换多少天 或 价值多少钱</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">卡密状态</label>
                        <div class="am-u-sm-9">
                            <select id="status">
                                <option value="-1">请选择</option>
                                <option value="0">禁用</option>
                                <option value="1">正常</option>
                            </select>
                            <small style="color: brown;">商品自动发货仅发送卡密状态为正常的数据</small>
                        </div>
                    </div>
                    <div class="am-form-group am-u-sm-push-1"
                         style="display: flex; justify-content: center;margin: 60px 0 40px 0;">
                        <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">添加数据</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var selectShop = function () {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['900px', '580px'],
            scrollbar: false,
            closeBtn: true,
            shadeClose: true,
            content: ["{:url('cammy/bindShop')}", 'no'],
        });
    }

    var cardRefrain = function (sid, shopName) {
        $('#sid').val(sid);
        $('#shopName').val(shopName);
    }

    var grender = function (obj, limit) {
        if (limit === 2) {
            obj.value = obj.value.replace(/[^\d.]/g, "");
            obj.value = obj.value.replace(/^\./g, "");
            obj.value = obj.value.replace(/\.{2,}/g, ".");
            obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        } else {
            obj.value = Number((obj.value.match(/^\d+(?:\.\d{0})?/)));
        }
    }


    var onlock = false;
    var holdSave = function () {
        var setData = {};
        setData['sid'] = Number($('#sid').val());
        if (setData['sid'] === 0) {
            layer.msg('请选择商品信息');
            return;
        }
        setData['cardCode'] = $.trim($('#cardCode').val());
        if (setData['cardCode'] === '') {
            layer.msg('请输入卡密信息');
            return;
        }
        setData['faceValue'] = $.trim($('#faceValue').val());
        if (setData['faceValue'] === '') {
            layer.msg('请输入卡密面值');
            return;
        }
        setData['status'] = Number($('#status').val());
        if (setData['status'] === -1) {
            layer.msg('请选择卡密状态');
            return;
        }
        if (!onlock) {
            onlock = true;
            $.post("{:url('cammy/newShopAnomaly')}", {'setData': setData}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.href = "{:url('cammy/shopAnomaly')}";
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200}, function () {
                        onlock = false;
                    });
                }
            }, 'json');
        }
    }
</script>
{/block}