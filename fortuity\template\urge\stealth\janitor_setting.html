{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-cog {margin-right: 5px;}
    
    .am-form-horizontal .am-form-group { margin-bottom: 20px; }
    .am-form-horizontal .am-form-label { text-align: right; font-weight: 500; padding-top: .5em; }
    .am-form-horizontal .am-form-group .am-u-sm-9 { padding-left: 15px; }
    .am-form-horizontal input[type='text'], .am-form-horizontal input[type='number'] {
        border-radius: 3px;
        border: 1px solid #e8e8e8;
        padding: 6px 10px;
        transition: all 0.3s;
        width: 100%;
        max-width: 400px;
        height: 38px;
    }
    .am-form-horizontal input[type='text']:focus, .am-form-horizontal input[type='number']:focus {
        border-color: #23b7e5;
        box-shadow: 0 0 0 2px rgba(35,183,229,0.1);
    }
    .am-form-horizontal .am-form-group small {color: #999; margin-top: 5px; display: block;}
    .am-form-horizontal .am-form-group .am-text-danger {color: #dd514c;}
    .am-radio-inline { margin-right: 20px; }
    .am-btn-primary { background-color: #23b7e5; border-color: #23b7e5; border-radius: 3px; }
    .am-btn-primary:hover { background-color: #49c5ec; border-color: #49c5ec; }

</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-cog"></span> 小秘密设置
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-8 am-u-md-offset-2">
                <div class="am-form am-form-horizontal">

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">小秘密自定义名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="title" value="{if $list.custom_hiss_title}{$list.custom_hiss_title}{else}树洞{/if}">
                            <small>自定义模块标题名称</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">发布自动审核</label>
                        <div class="am-u-sm-9">
                            <label class="am-radio-inline">
                                <input type="radio" name="hiss" value="1" {if $list.auto_hiss==1}checked{/if}> 开启
                            </label>
                            <label class="am-radio-inline">
                                <input type="radio" name="hiss" value="0" {if $list.auto_hiss==0}checked{/if}> 关闭
                            </label>
                            <small class="am-text-danger">开启后您无需再手动审核 系统将自动通过所有用户发布的小秘密</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">发布次数限制</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="hissLimit" value="{$list.hiss_limit}" oninput="grender(this);">
                            <small>每日单个帖子用户最多发布小秘密次数，0 为不限制</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">回复自动审核</label>
                        <div class="am-u-sm-9">
                            <label class="am-radio-inline">
                                <input type="radio" name="replyHiss" value="1" {if $list.reply_auto_hiss==1}checked{/if}> 开启
                            </label>
                            <label class="am-radio-inline">
                                <input type="radio" name="replyHiss" value="0" {if $list.reply_auto_hiss==0}checked{/if}> 关闭
                            </label>
                            <small class="am-text-danger">开启后您无需再手动审核 系统将自动通过所有用户回复的小秘密</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">回复次数限制</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="replyHissLimit" value="{$list.reply_hiss_limit}" oninput="grender(this);">
                            <small>每日单个帖子用户最多回复小秘密次数，0 为不限制</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-offset-3">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    var grender = function (obj) {
        obj.value = Number(obj.value.match(/^\d+(?:\.\d{0,0})?/));
    }

    var holdSave = function () {

        var setData = {};

        setData['uplid'] = '{$list.id}';

        setData['title'] = $.trim($("#title").val());

        setData['hiss'] = $.trim($("[name='hiss']:checked").val());
        setData['hissLimit'] = $('#hissLimit').val();

        setData['replyHiss'] = $.trim($("[name='replyHiss']:checked").val());
        setData['replyHissLimit'] = $('#replyHissLimit').val();

        $.post("{:url('stealth/janitor_setting')}", setData, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000});
            }
        }, 'json');
    }
</script>
{/block}