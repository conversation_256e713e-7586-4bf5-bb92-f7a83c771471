{extend name="/base"/}
{block name="main"}
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;}.tpl-toolbar{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;}.am-input-group-sm .am-form-field,.am-input-group-sm .am-input-group-btn .am-btn{height:32px;}.action-btn{display:inline-block;padding:5px 10px;font-size:13px;font-weight:normal;line-height:1.4;text-align:center;white-space:nowrap;vertical-align:middle;cursor:pointer;border:1px solid #ccc;border-radius:4px;transition:all .3s;}.action-btn:hover{background-color:#f5f5f5;}.action-btn .am-icon-trash-o,.action-btn .am-icon-copy,.action-btn .am-icon-repeat,.action-btn .am-icon-adn{margin-right:4px;}.action-btn.btn-add{color:#fff;background-color:#23b7e5;border-color:#23b7e5;}.action-btn.btn-add:hover{background-color:#1a9fd4;}.action-btn.btn-delete{color:#dd514c;background-color:white;}.action-btn.btn-delete:hover{color:#fff;background-color:#dd514c;}.action-btn.btn-approve{color:#fff;background-color:#5eb95e;border-color:#5eb95e;}.action-btn.btn-approve:hover{background-color:#449d44;}.action-btn.btn-reject{color:#fff;background-color:#dd514c;border-color:#dd514c;}.action-btn.btn-reject:hover{background-color:#c9302c;}.action-btn.btn-return{color:#333;background-color:#fff;border-color:#ccc;}.action-btn.btn-return:hover{background-color:#e6e6e6;}.confirm-btn{color:#fff;background-color:#23b7e5;border-color:#23b7e5;padding:6px 15px;font-size:13px;border-radius:3px;cursor:pointer;}.confirm-btn:hover{background-color:#1a9fd4;}.filter-btn-group .am-btn{border-radius:3px;}.filter-btn-group .am-btn.active{background-color:#23b7e5;color:white;border-color:#23b7e5;}.am-table{border:1px solid #f0f0f0;border-radius:4px;font-size:13px;}.am-table > thead:first-child > tr:first-child > th{background-color:#f9f9f9;text-align:center;padding:10px 8px;font-weight:500;}.am-table > tbody > tr > td{text-align:center;vertical-align:middle;}.tpl-form-input{display:block;width:100%;height:32px;padding:6px 12px;font-size:13px;border:1px solid #ccc;border-radius:4px;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;}.tpl-form-input:focus{border-color:#23b7e5;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(35,183,229,.6);}textarea.tpl-form-input{height:auto;}.am-badge{display:inline-block;padding:.25em .6em;font-size:75%;font-weight:700;line-height:1;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25rem;}.am-badge-success{color:#fff;background-color:#5eb95e;}.am-badge-warning{color:#fff;background-color:#f39c12;}.am-badge-danger{color:#fff;background-color:#dd514c;}</style>
<div class="tpl-portlet-components">
    <div class="tpl-block">
        <div class="am-g tpl-toolbar">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <button type="button" class="action-btn btn-add" onclick="clearText();" data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 400, height: 185}">
                            <span class="am-icon-adn"></span> 新增关注用户
                        </button>
                    </div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-3">
                <div class="am-input-group am-input-group-sm">
                    <input type="text" id="fz_name" value="{$hazy_name}" class="am-form-field" placeholder="搜索用户...">
                    <span class="am-input-group-btn">
                        <button class="am-btn am-btn-default am-btn-success tpl-am-btn-success am-icon-search" onclick="fuzzy();" type="button"></button>
                    </span>
                </div>
            </div>
        </div>

        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr>
                            <th width="20%">用户头像</th>
                            <th width="25%">用户昵称</th>
                            <th width="25%">openid</th>
                            <th width="20%">关注时间</th>
                            <th width="10%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                {if $vo.user_head_sculpture && $vo.user_head_sculpture !='/yl_welore/style/icon/default.png'}
                                <img src="{$vo.user_head_sculpture}" style="width: 50px;height: 50px;border-radius: 50%;">
                                {else}
                                <img src="{:urlBridging('static/disappear/tourist.png')}" style="width: 50px;height: 50px;border-radius: 50%;">
                                {/if}
                                </a>
                            </td>
                            <td class="am-text-middle">
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1"
                                   title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                            </td>
                            <td class="am-text-middle">{$vo.user_wechat_open_id}</td>
                            <td class="am-text-middle">{:date('Y-m-d H:i:s',$vo.ling_time)}</td>
                            <td class="am-text-middle">
                                <button class="action-btn btn-delete" onclick="cancelStrike('{$vo.id}');">移除关注</button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
        <div class="am-modal-dialog" style="background:#fefffe;">
            <div class="am-modal-hd">
                <span style="font-size: 14px;position: absolute;left:12px;top:7px;">关注用户</span>
                <a id="euModalClose" href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd am-form tpl-form-line-form">
                <div class="am-form-group" style="margin-top:25px;">
                    <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">用户信息</label>
                    <div class="am-u-sm-8">
                        <input type="text" id="openid" oninput="extolled(this);" class="tpl-form-input" style="margin:3px 0 0 -20px;" placeholder="请输入用户openid">
                        <span id="sehred" style="position: absolute;left: 0px; color: blue;font-size: 12px;">　</span>
                    </div>
                </div>
                <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top:20px;">
                    <button type="button" class="confirm-btn" onclick="sendGifts();">确定</button>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    var clearText = function () {
        $('#openid').val('');
        $('#sehred').text('');
    }

    var extolled = function (obj) {
        obj.value = $.trim(obj.value);
        if (obj.value != '') {
            $.getJSON("{:url('compass/getopenid')}", {"openid": obj.value}, function (data) {
                if (data.name != '') {
                    $('#sehred').css('color', 'blue');
                    $('#sehred').text(data.name);
                } else {
                    $('#sehred').css('color', 'red');
                    $('#sehred').text('\u7528\u6237\u006f\u0070\u0065\u006e\u0069\u0064\u8f93\u5165\u9519\u8bef');
                }
            });
        }
    }

    var sendGifts = function () {
        var openid = $.trim($('#openid').val());
        if (openid == '') {
            layer.msg('用户openid不能为空');
            return;
        }
        $.ajaxSettings.async = false;
        $.post("{:url('compass/caveat')}", {'openid': openid, 'tyid': '{$tyid}', 'type': 0}, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1600}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                    location.reload();
                });
            }
        }, 'json');
        $.ajaxSettings.async = true;
    }

    var cancelStrike = function (hid) {
        layer.confirm('您确定要移除此用户关注当前圈子吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("{:url('compass/caveat')}", {'hid': hid, type: 1}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1600}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('compass/caveat')}&tyid={$tyid}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('compass/caveat')}&tyid={$tyid}&page={$page}";
        }
    }

</script>
{/block}