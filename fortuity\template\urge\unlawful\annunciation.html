{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-trophy {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .add-ranking-btn {background: linear-gradient(135deg, #28a745 0%, #20c997 100%);border: none;color: white;padding: 8px 16px;border-radius: 6px;font-size: 13px;cursor: pointer;transition: all 0.3s ease;box-shadow: 0 2px 4px rgba(40,167,69,0.2);display: inline-flex;align-items: center;gap: 6px;}
    .add-ranking-btn:hover {background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);box-shadow: 0 4px 8px rgba(40,167,69,0.3);transform: translateY(-1px);}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 12px 8px;}
    .am-table > tbody > tr > td {padding: 15px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .sort-input {width: 180px;max-width: 180px;min-width: 180px;height: 32px;padding: 4px 6px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;font-size: 13px;text-align: center;flex-shrink: 0;}
    .sort-input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .status-badge {display: inline-block;padding: 4px 8px;border-radius: 12px;font-size: 11px;font-weight: 500;}
    .status-hidden {background-color: #f8d7da;color: #721c24;border: 1px solid #f5c6cb;}
    .status-visible {background-color: #d4edda;color: #155724;border: 1px solid #c3e6cb;}
    .action-btn {padding: 6px 12px;border-radius: 4px;font-size: 12px;cursor: pointer;transition: all 0.3s;border: 1px solid;margin: 0 2px;}
    .edit-btn {background: #fff;color: #17a2b8;border-color: #17a2b8;}
    .edit-btn:hover {background: #17a2b8;color: #fff;}
    .delete-btn {background: #fff;color: #dc3545;border-color: #dc3545;}
    .delete-btn:hover {background: #dc3545;color: #fff;}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-trophy"></span> 榜单排行
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="turtle('all');"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索榜单名称...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-6 am-u-md-7">
                <div class="am-btn-toolbar" style="margin-bottom: 15px;">
                    <button class="add-ranking-btn" onclick="saloof();">
                        <span class="am-icon-plus"></span> 新增榜单
                    </button>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form" style="overflow-x:auto;">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="25%">排序</th>
                            <th width="25%">榜单名称</th>
                            <th width="25%">状态</th>
                            <th width="25%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                <div style="display: flex;justify-content: center;">
                                    <input type="text" class="sort-input" id="sfixed-{$vo.id}" value="{$vo.scores}" data-score="{$vo.scores}" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                                </div>
                            </td>
                            <td>
                                <span title="{$vo.ranking_name}">
                                    {$vo.ranking_name}
                                </span>
                            </td>
                            <td>
                                {if $vo.status == 0}
                                <span class="status-badge status-hidden">隐藏</span>
                                {elseif $vo.status == 1}
                                <span class="status-badge status-visible">显示</span>
                                {/if}
                            </td>
                            <td>
                                <button type="button" class="action-btn edit-btn" onclick="unute('{$vo.id}');">
                                    <span class="am-icon-edit"></span> 编辑
                                </button>
                                <button type="button" class="action-btn delete-btn" onclick="annunciationDel('{$vo.id}','{$vo.ranking_name}');">
                                    <span class="am-icon-trash"></span> 删除
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                </div>
                <div class="am-u-sm-12 no-wrap" style="text-align:center;">
                    {$list->render()}
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>


    function exalter(asyId, dalue) {
        var straw = {};
        $.ajax({
            type: "post",
            url: "{:url('unlawful/annunciationSort')}",
            data: {
                asyId: asyId,
                dalue: dalue
            },
            async: false,
            success: function (data) {
                straw = data;
            }
        });
        return straw;
    }

    function supre(asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            var repair = exalter(asyId, dalue);
            if (repair.code > 0) {
                layer.msg(repair.msg, {icon: 1, time: 800});
                $(domId).attr('data-score', dalue);
            } else {
                layer.msg(repair.msg, {icon: 5, time: 1600});
            }
        }
    }

    function saloof() {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('unlawful/ruannunciation')}");
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    function unute(usid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('unlawful/upannunciation')}&usid=" + usid);
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    var lock = false;
    function annunciationDel(mid, vname) {
        if (!lock) {
            lock = true;
            var shint = '您确定要 <span style="color: red">删除</span> <span style="color: blue;">' + vname + '</span> 吗？';
            layer.confirm(shint, {
                btn: ['确定', '取消'], 'title': '删除提示 (<span style="color: #0066ee;">数据将不可恢复</span>)'
            }, function () {
                $.post("{:url('unlawful/delAnnunciation')}", {'ecid': mid},
                    function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                lock = false;
                            });
                        }
                    }, 'json');
            }, function () {
                lock = false;
            });
        }
    }



    function turtle() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name)
            location.href = "{:url('unlawful/annunciation')}&hazy_name=" + fz_name + "&page={$page}";
        else
            location.href = "{:url('unlawful/annunciation')}&page={$page}";
    }

</script>
{/block}