<?php
error_reporting(0);
//接收信息
$notify_data = file_get_contents("php://input");
//file_put_contents("test2.txt", $notify_data, FILE_APPEND);
//xml to arr
$values = json_decode(json_encode(simplexml_load_string($notify_data, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
//获取域名
$domain = explode(':', $_SERVER['HTTP_HOST']);
$absAddress = explode("payReact.php", $_SERVER['SCRIPT_NAME']);
$absRessReplace = "https://{$domain[0]}{$absAddress[0]}index.php?s=/api/notify/get_notify";
$absRess = str_replace('\\', '/', $absRessReplace);
try {
    return requestPost($absRess, $values, true);
} catch (Exception $e) {
    return false;
}

//进行post请求
function requestPost($url, $data, $ssl = true) {
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);//URL
    $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:38.0) Gecko/20100101 Firefox/38.0 FirePHP/0.7.4';
    curl_setopt($curl, CURLOPT_USERAGENT, $user_agent);
    curl_setopt($curl, CURLOPT_AUTOREFERER, true);
    curl_setopt($curl, CURLOPT_TIMEOUT, 15 * 60);
    if ($ssl) {
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 2);
    }
    curl_setopt($curl, CURLOPT_POST, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
    curl_setopt($curl, CURLOPT_HEADER, false);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($curl);
    if (false === $response) {
        return false;
    }
    curl_close($curl);
    return $response;
}
