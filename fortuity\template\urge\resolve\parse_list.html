{extend name="/base"/}
{block name="main"}
<style>.customize-span,.customize-span:hover{padding:3px 10px;color:black !important;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 解析列表
        </div>
        <div style="text-align: right;">
            <div class="am-btn-toolbar" style="padding-top: 5px;">
                <a href="{:url('resolve/new_parse_list')}" class="customize-span">
                    <span class="am-icon-adn"></span> 新增解析信息
                </a>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div id="search-list" style="display: flex;flex-wrap: wrap;width: 100%;min-height: 38px;">
            <div class="search-wide-pitch search-inline">
                <label class="search-label">解析名称</label>
                <div class="search-input-inline">
                    <input type="text" name="name" value="{$name}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">解析地址</label>
                <div class="search-input-inline">
                    <input type="text" name="url" value="{$url}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">默认解析地址</label>
                <div class="search-input-inline">
                    <select name="isDefault" class="search-input">
                        <option value="-1">请选择</option>
                        <option value="1" {if $isDefault==1}selected{/if}>是</option>
                        <option value="0" {if $isDefault==0}selected{/if}>否</option>
                    </select>
                </div>
            </div>
            <button class="search-wide-pitch search-btn" onclick="turtle();">
                <i class="am-icon-search"></i> 搜 索
            </button>
        </div>
        <div class="am-g" style="margin-top: 15px;">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th class="text-center" width="16.66%">解析名称</th>
                            <th class="text-center" width="16.66%">解析地址</th>
                            <th class="text-center" width="16.66%">请求方法</th>
                            <th class="text-center" width="16.66%">默认解析地址</th>
                            <th class="text-center" width="16.66%">创建时间</th>
                            <th class="text-center" width="16.66%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td style="width: 16.66%">
                                <div style="display: flex;justify-content: center;align-items: center;height: 42px;">
                                    {$vo.parse_name}
                                </div>
                            </td>
                            <td style="width: 16.66%;">
                                <div style="display: flex;justify-content: center;align-items: center;height: 42px;padding: 0 20px;" title="{$vo.parse_url}">
                                    {$vo.parse_url|$customSubstr}
                                </div>
                            </td>
                            <td style="width: 16.66%">
                                <div style="display: flex;justify-content: center;align-items: center;height: 42px;">
                                    {if $vo.req_method==0}GET{else}POST{/if}
                                </div>
                            </td>
                            <td style="width: 16.66%">
                                <div style="display: flex;justify-content: center;align-items: center;height: 42px;">
                                    {if $vo.is_default==1}
                                    <span style="color: green;">是</span>
                                    {else}
                                    <span style="color: red;">否</span>
                                    {/if}
                                </div>
                            </td>
                            <td style="width: 16.66%">
                                <div style="display: flex;justify-content: center;align-items: center;height: 42px;">
                                    {:date('Y-m-d H:i:s',$vo.create_time)}
                                </div>
                            </td>
                            <td style="width: 16.66%">
                                <div style="display: flex;justify-content: center;align-items: center;height: 42px;">
                                    <button type="button" class="am-btn am-btn-default am-btn-xs am-text-secondary" onclick="editInfo('{$vo.id}');">
                                        <span class="am-icon-pencil-square-o"></span> 编辑
                                    </button>
                                    <button type="button" class="am-btn am-btn-default am-btn-xs am-text-danger am-hide-sm-only" onclick="deleteInfo('{$vo.id}')">
                                        <span class="am-icon-trash-o"></span>
                                        删除
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    function turtle() {
        var searchInput = $('#search-list .search-input');
        var n = 0;
        for (var i = 0; i < searchInput.length; i++) {
            if (searchInput[i].value.trim() !== '') {
                n++;
            }
        }
        if (n > 0) {
            location.href = `{:url('resolve/parse_list')}&${searchInput.serialize()}&page={$page}`;
        } else {
            location.href = "{:url('resolve/parse_list')}&page={$page}";
        }
    }

    function editInfo(fid) {
        location.href = "{:url('resolve/edit_parse_list')}&fid=" + fid;
    }

    function deleteInfo(fid) {
        layer.confirm('您确定要删除当前地址吗', {
            btn: ['确定', '取消']
        }, function () {
            $.post("{:url('resolve/del_parse_list')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

</script>
{/block}