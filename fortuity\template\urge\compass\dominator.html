{extend name="/base"/}
{block name="main"}
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;position:relative;}.tpl-portlet-components:after{content:"";display:table;clear:both;}.am-form{position:relative;overflow:visible;}.portlet-title{display:flex;justify-content:space-between;align-items:center;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid #f0f0f0;}.caption{font-size:16px;color:#23b7e5;font-weight:500;}.caption .am-icon-google-wallet{margin-right:5px;color:#23b7e5;}.tpl-portlet-input{position:relative;}.tpl-portlet-input input{height:32px;width:200px;padding:0 30px 0 10px;border:1px solid #e8e8e8;border-radius:4px;background:#fafafa;transition:all 0.3s;}.tpl-portlet-input input:focus{border-color:#23b7e5;background:#fff;box-shadow:0 0 0 2px rgba(35,183,229,0.1);}.tpl-portlet-input .am-icon-search{position:absolute;right:10px;top:50%;transform:translateY(-50%);color:#999;cursor:pointer;}.action-btn{display:inline-block;padding:4px 8px;background:#fff;border:1px solid #ddd;color:#23b7e5;border-radius:3px;font-size:12px;cursor:pointer;transition:all 0.3s;margin:2px;}.action-btn:hover{border-color:#23b7e5;color:#23b7e5;background-color:#f5fafd;}.am-table{border:1px solid #f0f0f0;border-radius:4px;font-size:13px;margin-bottom:15px;}.am-table>thead:first-child>tr:first-child>th{background-color:#f9f9f9;border-bottom:1px solid #eee;color:#333;font-weight:500;font-size:13px;text-align:center;padding:10px 8px;}.am-table>tbody>tr>td{padding:10px 8px;border-top:1px solid #f3f3f3;text-align:center;vertical-align:middle;color:#666;line-height:1.6;position:relative;}.am-table-striped>tbody>tr:nth-child(odd)>td{background-color:#fafafa;}.am-table>tbody>tr:hover>td{background-color:#f5fafd;}.am-modal-dialog{border-radius:4px;overflow:hidden;box-shadow:0 5px 15px rgba(0,0,0,0.2);}.am-modal-hd{background:#f9f9f9;padding:10px 15px;border-bottom:1px solid #eee;}.am-modal-bd{padding:15px;text-align:center;}.am-form-group{margin-bottom:15px;}.am-form-label{font-size:13px;color:#666;font-weight:normal;line-height:32px;text-align:right;padding:0;}.tpl-form-input{height:32px;padding:6px 10px;font-size:13px;border:1px solid #e8e8e8;border-radius:3px;transition:all 0.3s;width:100%;}.tpl-form-input:focus{border-color:#23b7e5;box-shadow:0 0 0 2px rgba(35,183,229,0.1);}.confirm-btn{background:#23b7e5;color:white;border:none;border-radius:3px;padding:6px 15px;font-size:13px;cursor:pointer;transition:all 0.3s;}.confirm-btn:hover{background:#1a9fd4;box-shadow:0 2px 5px rgba(26,159,212,0.2);}select.tpl-form-input{padding-top:0;padding-bottom:0;line-height:32px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-google-wallet"></span> {$toryInfo.realm_name|emoji_decode} 管理团队列表
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <a href="javascript:void(0);" class="am-btn am-btn-success am-btn-sm" onclick="clearText();"
                        data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 400, height: 295}">
                        <span class="am-icon-plus"></span> 任命用户职位
                    </a>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                            <tr>
                                <th width="20%">用户</th>
                                <th width="20%">昵称</th>
                                <th width="20%">openid</th>
                                <th width="20%">职位</th>
                                <th width="20%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {volist name="tylBulordInfo" id="vo"}
                            <tr>
                                <td>
                                    {if $vo.user_head_sculpture && $vo.user_head_sculpture
                                    !='/yl_welore/style/icon/default.png'}
                                    <img src="{$vo.user_head_sculpture}"
                                        style="width: 40px;height: 40px;border-radius: 50%;">
                                    {else}
                                    <img src="{:urlBridging('static/disappear/tourist.png')}"
                                        style="width: 40px;height: 40px;border-radius: 50%;">
                                    {/if}
                                </td>
                                <td>{$vo.user_nick_name|emoji_decode}</td>
                                <td>{$vo.user_wechat_open_id}</td>
                                <td>
                                    <span class="am-badge am-badge-primary">圈主</span>
                                </td>
                                <td>
                                    <button type="button" class="action-btn am-btn-secondary"
                                        onclick="sendGifts('{$toryInfo.id}',1,0,'{$vo.user_wechat_open_id}');">
                                        取消任命
                                    </button>
                                </td>
                            </tr>
                            {/volist}
                            {volist name="tylSulordInfo" id="vo"}
                            <tr>
                                <td>
                                    <img src="{$vo.user_head_sculpture}"
                                        style="width:40px;height:40px;border-radius:50%;">
                                </td>
                                <td>{$vo.user_nick_name|emoji_decode}</td>
                                <td>{$vo.user_wechat_open_id}</td>
                                <td>
                                    <span class="am-badge am-badge-secondary">管理员</span>
                                </td>
                                <td>
                                    <button type="button" class="action-btn am-btn-secondary"
                                        onclick="sendGifts('{$toryInfo.id}',1,1,'{$vo.user_wechat_open_id}');">
                                        取消任命
                                    </button>
                                </td>
                            </tr>
                            {/volist}
                            {volist name="tylSnviteBulordInfo" id="vo" key="cursor"}
                            <tr>
                                <td>
                                    <img src="{$vo.user_head_sculpture}"
                                        style="width:40px;height:40px;border-radius:50%;">
                                </td>
                                <td>{$vo.user_nick_name|emoji_decode}</td>
                                <td>{$vo.user_wechat_open_id}</td>
                                <td>
                                    <span class="am-badge am-badge-warning" title="点击查看申请理由" style="cursor: pointer;"
                                        onclick="reasonForApplication('{$tylSnviteBulord[$cursor - 1][\'upshot\']}','{:date(\'Y-m-d H:i:s\',$tylSnviteBulord[$cursor - 1][\'time\'])}');">
                                        圈主 (待审)
                                    </span>
                                </td>
                                <td>
                                    <button type="button" class="action-btn am-btn-success"
                                        onclick="certificationAuthority('{$toryInfo.id}',0,0,'{$tylSnviteBulord[$cursor - 1][\'openid\']}');">
                                        通过
                                    </button>
                                    <button type="button" class="action-btn am-btn-danger"
                                        onclick="certificationAuthority('{$toryInfo.id}',1,0,'{$tylSnviteBulord[$cursor - 1][\'openid\']}');">
                                        拒绝
                                    </button>
                                </td>
                            </tr>
                            {/volist}
                            {volist name="tylEnviteSulordInfo" id="vo" key="cursor"}
                            <tr>
                                <td>
                                    <img src="{$vo.user_head_sculpture}"
                                        style="width:40px;height:40px;border-radius:50%;">
                                </td>
                                <td>{$vo.user_nick_name|emoji_decode}</td>
                                <td>{$vo.user_wechat_open_id}</td>
                                <td>
                                    <span class="am-badge am-badge-warning" title="点击查看申请理由" style="cursor: pointer;"
                                        onclick="reasonForApplication('{$tylEnviteSulord[$cursor - 1][\'upshot\']}','{:date(\'Y-m-d H:i:s\',$tylEnviteSulord[$cursor - 1][\'time\'])}');">
                                        管理员 (待审)
                                    </span>
                                </td>
                                <td>
                                    <button type="button" class="action-btn am-btn-success"
                                        onclick="certificationAuthority('{$toryInfo.id}',0,1,'{$tylEnviteSulord[$cursor - 1][\'openid\']}');">
                                        通过
                                    </button>
                                    <button type="button" class="action-btn am-btn-danger"
                                        onclick="certificationAuthority('{$toryInfo.id}',1,1,'{$tylEnviteSulord[$cursor - 1][\'openid\']}');">
                                        拒绝
                                    </button>
                                </td>
                            </tr>
                            {/volist}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
        <div class="am-modal-dialog">
            <div class="am-modal-hd">
                <span>任命职位</span>
                <a id="euModalClose" href="javascript: void(0);" class="am-close am-close-spin"
                    data-am-modal-close>&times;</a>
            </div>
            <input id="virtual-user" type="hidden" value="0" />
            <div class="am-modal-bd am-form">
                <div class="am-form-group am-g">
                    <label class="am-u-sm-3 am-form-label">用户信息</label>
                    <div class="am-u-sm-9">
                        <input type="text" id="openid" oninput="extolled(this);" class="tpl-form-input"
                            placeholder="请输入用户openid">
                        <span id="sehred"
                            style="display:block;position:static;margin-top:4px;color:blue;font-size:12px;">　</span>
                    </div>
                </div>
                <div class="am-form-group am-g">
                    <label class="am-u-sm-3 am-form-label">圈子职位</label>
                    <div class="am-u-sm-9">
                        <select id="job" class="tpl-form-input">
                            <option value="0">圈主</option>
                            <option value="1">管理员</option>
                        </select>
                    </div>
                </div>
                <div class="am-u-sm-12" style="margin-top:15px;text-align: center;">
                    <button type="button" class="confirm-btn" onclick="sendGifts('{$toryInfo.id}',0,null,null);">
                        确定任命
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    var clearText = function () {
        $('#openid').val('');
        $('#sehred').text('');
    }

    var extolled = function (obj) {
        obj.value = $.trim(obj.value);
        if (obj.value != '') {
            $.getJSON("{:url('compass/getopenid')}&virtual=1", { "openid": obj.value }, function (data) {
                if (data.name != '') {
                    $('#sehred').css('color', 'blue');
                    $('#sehred').text(data.name);
                } else {
                    $('#sehred').css('color', 'red');
                    $('#sehred').text('\u7528\u6237\u006f\u0070\u0065\u006e\u0069\u0064\u8f93\u5165\u9519\u8bef');
                }
            });
        }
    }

    var sendGifts = function (tyid, type, job, openid) {
        switch (type) {
            case 0:
                openid = $.trim($('#openid').val());
                if (openid == '') {
                    layer.msg('用户openid不能为空');
                    return;
                }
                job = $('#job').val();
                $.post("{:url('compass/nominate')}", { tyid, type, job, openid }, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, { icon: 5, time: 2000 });
                    }
                }, 'json');
                break;
            case 1:
                layer.prompt({ title: '请输入取消任命原因 : ' }, function (reason, index) {
                    if ($.trim(reason) == '') {
                        return;
                    }
                    $.post("{:url('compass/nominate')}", { tyid, type, job, openid, reason }, function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, { icon: 5, time: 2000 });
                        }
                    }, 'json');
                });
                break;
        }
    }


    var reasonForApplication = function (justification, time) {
        if ($.trim(justification) == '') {
            justification = '申请理由：该用户没有输入申请理由！';
        } else {
            justification = '申请理由：' + justification;
        }
        layer.alert(justification, { title: '职位申请时间：' + time });
    }

    var certificationAuthority = function (tyid, genre, type, openid) {
        switch (genre) {
            case 0:
                layer.confirm('您确定要同意此用户的申请请求吗？', {
                    btn: ['确定', '取消'], title: '提示'
                }, function () {
                    $.post("{:url('compass/turnover')}", { tyid, genre, type, openid }, function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, { icon: 5, time: 2000 });
                        }
                    }, 'json');
                }, function (index) {
                    layer.close(index);
                });
                break;
            case 1:
                layer.prompt({ title: '请输入拒绝原因 : ' }, function (reason, index) {
                    if ($.trim(reason) == '') {
                        return;
                    }
                    $.post("{:url('compass/turnover')}", { tyid, genre, type, openid, reason }, function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, { icon: 5, time: 2000 });
                        }
                    }, 'json');
                });
                break;
        }
    }
</script>
{/block}