{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-users"></span> 邀请明细
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索邀请码...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="22%">邀请用户信息</th>
                            <th width="22%">被邀请用户信息</th>
                            <th width="8%">邀请码</th>
                            <th width="14%" style="text-align:right;">邀请用户获得{$defaultNavigate.confer}</th>
                            <th width="14%" style="text-align:right;">被邀请用户获得{$defaultNavigate.confer}</th>
                            <th width="20%" style="text-align:right;">邀请时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                {if $vo.in_user_head_img && $vo.in_user_head_img !='/yl_welore/style/icon/default.png'}
                                <img src="{$vo.in_user_head_img}" style="width: 50px;height: 50px;border-radius: 50%;">
                                {else}
                                <img src="{:urlBridging('static/disappear/tourist.png')}" style="width: 50px;height: 50px;border-radius: 50%;">
                                {/if}
                                <a href="{:url('user/index')}&openid={$vo.in_user_openid}&page=1" target="_blank" title="{$vo.in_user_nickname}">
                                    {$vo.in_user_nickname}
                                </a>
                            </td>
                            <td class="am-text-middle">
                                {if $vo.user_head_sculpture && $vo.user_head_sculpture !='/yl_welore/style/icon/default.png'}
                                <img src="{$vo.user_head_sculpture}" style="width: 50px;height: 50px;border-radius: 50%;">
                                {else}
                                <img src="{:urlBridging('static/disappear/tourist.png')}" style="width: 50px;height: 50px;border-radius: 50%;">
                                {/if}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                            </td>
                            <td class="am-text-middle">{$vo.re_code}</td>
                            <td class="am-text-middle" style="text-align:right;">
                                {$vo.in_us_reward}
                            </td>
                            <td class="am-text-middle" style="text-align:right;">
                                {$vo.re_us_reward}
                            </td>
                            <td class="am-text-middle" style="text-align:right;">
                                {:date('Y-m-d H:i:s',$vo.re_time)}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>
    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('unlawful/salute')}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('unlawful/salute')}&page={$page}";
        }
    }
</script>
{/block}