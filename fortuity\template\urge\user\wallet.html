{extend name="/base"/}
{block name="main"}
<style>.span-ranking{background:#477a98;border-radius:3px;padding:6px 10px;font-size:12px;cursor:pointer;color:#f1f4f5;margin-left:5px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption bold" style="margin-left:14px;">
            <span style="color: black;margin: 0px 3px;">{$userInfo.user_nick_name|emoji_decode}</span>的钱包
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="searchDetail" value="{$searchDetail}" placeholder="搜索说明...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom:20px;">
            <div class="am-u-sm-6 am-u-md-6">
                <span style="font-size:14px;font-weight:bold;">
                    {$defaultNavigate.currency}余额 :
                    <span style="color:green;">{$userInfo.conch}</span>
                </span>
                <span style="font-size:14px;font-weight:bold;margin-left:20px;">
                    {$defaultNavigate.confer}余额 :
                    <span style="color:green;">{$userInfo.fraction}</span>
                </span>
            </div>
            <div class="am-u-sm-6 am-u-md-6" style="text-align:right;">
                <span class="span-ranking" data-am-modal="{target: '#rechargeConchA', closeViaDimmer: 0, width: 550, height: 335}">
                    充值{$defaultNavigate.currency}
                </span>
                <span class="span-ranking" data-am-modal="{target: '#rechargeConchB', closeViaDimmer: 0, width: 550, height: 335}">
                    扣除{$defaultNavigate.currency}
                </span>
                <span class="span-ranking" data-am-modal="{target: '#rechargeFractionA', closeViaDimmer: 0, width: 550, height: 335}">
                    充值{$defaultNavigate.confer}
                </span>
                <span class="span-ranking" data-am-modal="{target: '#rechargeFractionB', closeViaDimmer: 0, width: 550, height: 335}">
                    扣除{$defaultNavigate.confer}
                </span>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr>
                            <th width="25%" style="text-align:right;">操作金额</th>
                            <th width="50%" style="text-align:center;">操作说明</th>
                            <th width="25%" style="text-align:left;">操作时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle" style="text-align:right;">
                                {if $vo.finance >= 0}
                                <span style="color:green;">
                                    +{$vo.finance}
                                    ( {if $vo.evaluate==0}{$defaultNavigate.currency}{elseif $vo.evaluate==1}{$defaultNavigate.confer}{elseif $vo.evaluate==2}微信支付{/if} )
                                </span>
                                {else}
                                <span style="color:red;">
                                    {$vo.finance}
                                    ( {if $vo.evaluate==0}{$defaultNavigate.currency}{elseif $vo.evaluate==1}{$defaultNavigate.confer}{elseif $vo.evaluate==2}微信支付{/if} )
                                </span>
                                {/if}
                            </td>
                            <td class="am-text-middle" style="text-align:center;">
                                {$vo.solution}
                            </td>
                            <td class="am-text-middle" style="text-align:left;">
                                {:date('Y-m-d H:i:s',$vo.ruins_time)}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="rechargeConchA">
        <div class="am-modal-dialog">
            <div class="am-modal-hd" style="text-align: left;">
                <a href="javascript: void(0)" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd" style="margin-top: 30px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">充值{$defaultNavigate.currency}</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <input type="number" id="modifyConchA" value="0" placeholder="请输入要充值的{$defaultNavigate.currency}数量" data-conch="0">
                            <input type="hidden" id="uconchA" value="{$userInfo.conch}">
                            <small id="cEcipherA"><span style="color: red;font-size: 14px;"> {$userInfo.conch} + 0 = {$userInfo.conch} </span></small><br>
                            <small><strong>计算方式：{$defaultNavigate.currency}余额 + 充值{$defaultNavigate.currency}数量 = 即将要保存的{$defaultNavigate.currency}</strong></small><br>
                            <small><span style="color: #2D93CA;">计算结果将会自动保留两位小数</span></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">备注</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <textarea id="solutionA" rows="3" style="resize: none;">系统赠送{$defaultNavigate.currency}</textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <button type="button" class="am-btn am-btn-default am-btn-sm" onclick="holdSave('0');">
                            确认充值
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="rechargeConchB">
        <div class="am-modal-dialog">
            <div class="am-modal-hd" style="text-align: left;">
                <a href="javascript: void(0)" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd" style="margin-top: 30px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">扣除{$defaultNavigate.currency}</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <input type="number" id="modifyConchB" value="0" placeholder="请输入要扣除的{$defaultNavigate.currency}数量" data-conch="0">
                            <input type="hidden" id="uconchB" value="{$userInfo.conch}">
                            <small id="cEcipherB"><span style="color: red;font-size: 14px;"> {$userInfo.conch} - 0 = {$userInfo.conch} </span></small><br>
                            <small><strong>计算方式：{$defaultNavigate.currency}余额 - 扣除{$defaultNavigate.currency}数量 = 即将要保存的{$defaultNavigate.currency}</strong></small><br>
                            <small><span style="color: #2D93CA;">计算结果将会自动保留两位小数</span></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">备注</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <textarea id="solutionB" rows="3" style="resize: none;">系统扣除{$defaultNavigate.currency}</textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <button type="button" class="am-btn am-btn-default am-btn-sm" onclick="holdSave('1');">
                            确认扣除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="rechargeFractionA">
        <div class="am-modal-dialog">
            <div class="am-modal-hd" style="text-align: left;">
                <a href="javascript: void(0)" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd" style="margin-top: 30px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">充值{$defaultNavigate.confer}</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <input type="number" id="modifyFractionA" value="0" placeholder="请输入要充值的{$defaultNavigate.confer}数量" data-fraction="0">
                            <input type="hidden" id="uFractionA" value="{$userInfo.fraction}">
                            <small id="fEcipherA"><span style="color: red;font-size: 14px;"> {$userInfo.fraction} + 0 = {$userInfo.fraction} </span></small><br>
                            <small><strong>计算方式：{$defaultNavigate.confer}余额 + 充值{$defaultNavigate.confer}数量 = 即将要保存的{$defaultNavigate.confer}</strong></small><br>
                            <small><span style="color: #2D93CA;">计算结果将会自动保留两位小数</span></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">备注</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <textarea id="solutionC" rows="3" style="resize: none;">系统赠送{$defaultNavigate.confer}</textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <button type="button" class="am-btn am-btn-default am-btn-sm" onclick="holdSave('2');">
                            确认充值
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="rechargeFractionB">
        <div class="am-modal-dialog">
            <div class="am-modal-hd" style="text-align: left;">
                <a href="javascript: void(0)" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd" style="margin-top: 30px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">扣除{$defaultNavigate.confer}</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <input type="number" id="modifyFractionB" value="0" placeholder="请输入要扣除的{$defaultNavigate.confer}数量" data-fraction="0">
                            <input type="hidden" id="uFractionB" value="{$userInfo.fraction}">
                            <small id="fEcipherB"><span style="color: red;font-size: 14px;"> {$userInfo.fraction} - 0 = {$userInfo.fraction} </span></small><br>
                            <small><strong>计算方式：{$defaultNavigate.confer}余额 - 扣除{$defaultNavigate.confer}数量 = 即将要保存的{$defaultNavigate.confer}</strong></small><br>
                            <small><span style="color: #2D93CA;">计算结果将会自动保留两位小数</span></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">备注</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <textarea id="solutionD" rows="3" style="resize: none;">系统扣除{$defaultNavigate.confer}</textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <button type="button" class="am-btn am-btn-default am-btn-sm" onclick="holdSave('3');">
                            确认扣除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    $(function () {
        $('#modifyConchA').keyup(function () {
            var getUconch = Number($('#uconchA').val().match(/^\d+(?:\.\d{0,2})?/));
            var getConch = Number($(this).val().match(/^\d+(?:\.\d{0,2})?/));
            if (isNaN(getConch)) {
                $(this).val(getConch = 0);
            }
            var epayoff = Number((getUconch + getConch).toString().match(/^\d+(?:\.\d{0,2})?/));
            if (epayoff >= 9999999999999999) {
                var dataConch = Number($(this).attr('data-conch'));
                $(this).val(dataConch);
                return false;
            }
            $('#cEcipherA').html('<span style="color: red;font-size: 14px;"> ' + getUconch + ' + ' + getConch + ' = ' + epayoff + ' </span>');
            $(this).attr('data-conch', getConch.toString());
        });
        $('#modifyConchB').keyup(function () {
            var getUconch = Number($('#uconchB').val().match(/^\d+(?:\.\d{0,2})?/));
            var getConch = Number($(this).val().match(/^\d+(?:\.\d{0,2})?/));
            if (isNaN(getConch)) {
                $(this).val(getConch = 0);
            }
            var epayoff = Number((getUconch - getConch).toString().match(/^\d+(?:\.\d{0,2})?/));
            if (epayoff < 0) {
                var dataConch = Number($(this).attr('data-conch'));
                $(this).val(dataConch);
                return false;
            }
            $('#cEcipherB').html('<span style="color: red;font-size: 14px;"> ' + getUconch + ' - ' + getConch + ' = ' + epayoff + ' </span>');
            $(this).attr('data-conch', getConch.toString());
        });
        $('#modifyFractionA').keyup(function () {
            var getUfraction = Number($('#uFractionA').val().match(/^\d+(?:\.\d{0,2})?/));
            var getFraction = Number($(this).val().match(/^\d+(?:\.\d{0,2})?/));
            if (isNaN(getFraction)) {
                $(this).val(getFraction = 0);
            }
            var epayoff = Number((getUfraction + getFraction).toString().match(/^\d+(?:\.\d{0,2})?/));
            if (epayoff >= 9999999999999999) {
                var dataFraction = Number($(this).attr('data-fraction'));
                $(this).val(dataFraction);
                return false;
            }
            $('#fEcipherA').html('<span style="color: red;font-size: 14px;"> ' + getUfraction + ' + ' + getFraction + ' = ' + epayoff + ' </span>');
            $(this).attr('data-fraction', getFraction.toString());
        });
        $('#modifyFractionB').keyup(function () {
            var getUfraction = Number($('#uFractionB').val().match(/^\d+(?:\.\d{0,2})?/));
            var getFraction = Number($(this).val().match(/^\d+(?:\.\d{0,2})?/));
            if (isNaN(getFraction)) {
                $(this).val(getFraction = 0);
            }
            var epayoff = Number((getUfraction - getFraction).toString().match(/^\d+(?:\.\d{0,2})?/));
            if (epayoff < 0) {
                var dataFraction = Number($(this).attr('data-fraction'));
                $(this).val(dataFraction);
                return false;
            }
            $('#fEcipherB').html('<span style="color: red;font-size: 14px;"> ' + getUfraction + ' - ' + getFraction + ' = ' + epayoff + ' </span>');
            $(this).attr('data-fraction', getFraction.toString());
        });
    });

    var islock = false
    var holdSave = function (genus) {
        if (!islock) {
            islock = true;
            var cipher = 0;
            var solution = '';
            switch (parseInt(genus)) {
                case 0:
                    cipher = Number($('#modifyConchA').val().toString().match(/^\d+(?:\.\d{0,2})?/));
                    solution = $.trim($('#solutionA').val());
                    break;
                case 1:
                    cipher = Number($('#modifyConchB').val().toString().match(/^\d+(?:\.\d{0,2})?/));
                    solution = $.trim($('#solutionB').val());
                    break;
                case 2:
                    cipher = Number($('#modifyFractionA').val().toString().match(/^\d+(?:\.\d{0,2})?/));
                    solution = $.trim($('#solutionC').val());
                    break;
                case 3:
                    cipher = Number($('#modifyFractionB').val().toString().match(/^\d+(?:\.\d{0,2})?/));
                    solution = $.trim($('#solutionD').val());
                    break;
            }
            $.post("{:url('user/alterunt')}", {
                'usid': '{$userInfo.id}',
                'genus': genus,
                'cipher': cipher,
                'solution': solution
            }, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                    islock = false;
                }
            }, 'json');
        }
    }


    var fuzzy = function () {
        var searchDetail = $.trim($('#searchDetail').val());
        if (searchDetail) {
            location.href = "{:url('user/wallet')}&usid={$userInfo.id}&searchDetail=" + searchDetail + "&page={$page}";
        } else {
            location.href = "{:url('user/wallet')}&usid={$userInfo.id}&page={$page}";
        }
    }
</script>
{/block}