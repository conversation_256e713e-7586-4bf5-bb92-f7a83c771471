{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 新增像框
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">像框名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" placeholder="请输入像框名称">
                            <small>建议像框名称不超过四个字</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">像框图标</label>
                        <div class="am-u-sm-9">
                            <img src="" id="shion" onerror="this.src='static/disappear/default.png'" onclick="cuonice();" style="width: 150px;height: 150px;cursor: pointer;"/>
                            <button type="button" style="margin-left:10px;font-size: 12px;" onclick="cuonice();">
                                选择图片
                            </button>
                            <small>建议选择图片大小尺寸为：128*128px 以内</small>
                            <input type="hidden" name="sngimg">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">所需积分</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="outlay" placeholder="请输入解锁像框所需要的积分" oninput="digitalCheck(this);">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">状态</label>
                        <div class="am-u-sm-9">
                            <select id="status">
                                <option value="0">隐藏</option>
                                <option value="1">显示</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="scores" value="0" placeholder="请输入排序数字" oninput="digitalCheck(this);">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-push-3">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        $('#shion').attr('src', eurl);
        $("[name='sngimg']").val(eurl);
        layer.closeAll();
    }

    var digitalCheck = function (obj) {
        obj.value = obj.value.replace(/[^\d.]/g, "");  //清除“数字”和“.”以外的字符
        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
        if (obj.value.indexOf(".") < 0 && obj.value !== "") {   //以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
            obj.value = parseFloat(obj.value);
        }
        return obj.value;
    }

    var onlock = false;
    var holdSave = function () {
        var setData = {};
        setData['adorn_name'] = $.trim($('#name').val());
        if (setData['adorn_name'] == '') {
            layer.msg('请输入像框名称');
            return;
        }
        setData['adorn_icon'] = $.trim($('[name=\'sngimg\']').val());
        if (setData['adorn_icon'] == '') {
            layer.msg('请选择像框图片');
            return;
        }
        setData['unlock_fraction'] = digitalCheck($('#outlay')[0]);
        setData['status'] = $('#status').val();
        setData['scores'] = digitalCheck($('#scores')[0]);
        if (!onlock) {
            onlock = true;
            $.post("{:url('manual/newAdhesion')}", setData, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.href = "{:url('manual/adhesion')}";
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1000}, function () {
                        onlock = false;
                    });
                }
            }, 'json');
        }
    }
</script>
{/block}