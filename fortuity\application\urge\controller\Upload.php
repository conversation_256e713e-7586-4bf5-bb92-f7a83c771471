<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\common\Assembly;
use app\common\ImageReduce;
use app\common\Remotely;
use app\urge\middleware\LoginCheck;
use think\Cache;
use think\Db;
use think\File;

#上传
class Upload
{

    //  多用户标识
    public $much_id = null;

    /**
     * @param $muchId | 多用户标识
     */
    public function __construct($muchId = null)
    {
        // 检查当前对象的$much_id属性是否为空。
        if (empty($this->much_id)) {
            // 如果为空，则将传入的$muchId赋值给它。
            $this->much_id = $muchId;
        }
        // 检查$much_id属性去除首尾空格后是否为空字符串。
        if (trim($this->much_id) == '') {
            // 通过 LoginCheck::run() 获取会话数据，该方法会在验证失败时自动处理重定向
            $sessionData = LoginCheck::run();
            // 确保从会话中成功获取了 uniacid
            if (isset($sessionData['uniacid'])) {
                $this->much_id = $sessionData['uniacid'];
            } else {
                // 如果 LoginCheck::run() 成功返回但没有 uniacid，这是一个异常情况
                // 抛出403禁止访问错误，因为没有有效的用户标识
                abort(403, '无效的会话');
            }
        }
    }

    /**
     * 系统上传配置
     * @param $type
     * @param $muchId
     * @return mixed
     */
    public static function systemOutlying($type, $muchId)
    {
        // 从缓存中获取指定类型和用户ID的外部存储配置
        $systemOutlying = Cache::get("outlying_{$type}_$muchId");
        // 如果缓存中没有找到配置
        if (!$systemOutlying) {
            // 根据类型获取配置
            switch ($type) {
                // 本地存储
                case 0:
                    // 从outlying表中获取配置
                    $systemOutlying = Db::name('outlying')->where('much_id', $muchId)->find();
                    break;
                // 网盘存储
                case 1:
                    // 从netdisc_storage表中获取配置
                    $systemOutlying = Db::name('netdisc_storage')->where('much_id', $muchId)->find();
                    break;
            }
            //  数据库没有数据
            if (!$systemOutlying) {
                // 设置用户ID
                $systemOutlying['much_id'] = $muchId;
                // 开启事务
                Db::startTrans();
                try {
                    // 根据类型插入配置
                    switch ($type) {
                        // 本地存储
                        case 0:
                            // 如果配置为空
                            if (!$systemOutlying) {
                                // 设置默认配置
                                $systemOutlying = ['quicken_type' => 0];
                            }
                            // 插入配置
                            $systemOutlying['id'] = Db::name('outlying')->insertGetId($systemOutlying);
                            break;
                        // 网盘存储
                        case 1:
                            // 如果配置为空
                            if (!$systemOutlying) {
                                // 设置默认配置
                                $systemOutlying = ['quicken_type' => -1];
                            }
                            // 插入配置
                            $systemOutlying['id'] = Db::name('netdisc_storage')->insertGetId($systemOutlying);
                            break;
                    }
                    // 提交事务
                    Db::commit();
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
            }
            // 定义需要解密的云存储配置项
            $followKeys = [
                'oss_follow',
                'qiniu_follow',
                'cos_follow',
                'upyun_follow',
                'ftp_follow',
                'aws_follow',
                'pan123_follow'
            ];

            // 循环处理每个配置项
            foreach ($followKeys as $followKey) {
                // 检查配置项是否存在且不为空
                if (!empty($systemOutlying[$followKey]) && is_string($systemOutlying[$followKey])) {
                    // 将JSON字符串格式的配置解码为关联数组
                    $decodedConfig = json_decode($systemOutlying[$followKey], true);
                    // 确保解码成功且结果是数组
                    if (is_array($decodedConfig)) {
                        // 遍历解码后的配置数组
                        foreach ($decodedConfig as $key => $value) {
                            // 对每个配置值使用'authcode'函数进行解密
                            $decodedConfig[$key] = authcode((string)$value, 'DECODE', 'YuluoNetwork');
                        }
                        // 将解密后的配置重新赋值回去
                        $systemOutlying[$followKey] = $decodedConfig;
                    }
                }
            }
            // 将处理后的配置数据存入缓存，有效期为7200秒（2小时）。
            Cache::set("outlying_{$type}_{$muchId}", $systemOutlying, 7200);
        }

        // 定义插件覆盖逻辑的配置映射
        $pluginOverrides = [
            // Amazon S3
            6 => ['property' => '6YCa55So5a2Y5YKo', 'config' => 'aws_follow'],
            // 123 Pan
            7 => ['property' => 'MTIz5LqR55uY', 'config' => 'pan123_follow'],
        ];
        // 循环检查并应用插件覆盖
        if ($type === 0) {
            // 遍历插件覆盖逻辑的配置映射
            foreach ($pluginOverrides as $typeId => $details) {
                // 检查是否开启插件 并且 配置不为空
                if (Remotely::isEnableProperty(base64_decode($details['property']), $muchId) && !empty($systemOutlying[$details['config']])) {
                    // 将存储类型设置为插件类型
                    $systemOutlying['quicken_type'] = $typeId;
                    // 跳出循环
                    break;
                }
            }
        }
        // 返回最终获取或创建的配置信息。
        return $systemOutlying;
    }

    /**
     * 上传文件
     * @return array|string[]|void|null
     */
    public function operate()
    {
        // 从当前HTTP请求中获取名为'sngpic'的上传文件对象。
        $file = request()->file('sngpic');
        // 从当前HTTP请求中获取名为'sngpic'的上传文件对象。
        // 从请求参数中获取'picture'标志，默认为空字符串。
        $picture = request()->param('picture', '');
        // 检查是否成功获取到文件对象。
        if ($file) {
            //  获取系统配置
            $systemOutlying = self::systemOutlying(0, $this->much_id);
            //  获取文件信息
            $fileInfo = $file->getInfo();
            //  获取文件后缀
            $fileExt = explode('/', $fileInfo['type']);
            //  上传是否是图片
            $uploadTypesOf = false;
            //  文件存储路径
            $agreement = date('Ymd');
            //  判断是否是图片类型
            if (in_array($fileExt[1], ['gif', 'jpg', 'jpeg', 'bmp', 'png'])) {
                $info = $file->move(ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'uploads' . DS . $agreement, md5(Assembly::uuid()) . '.' . strtolower($fileExt[1]));
                //  图片大于256kb并不等于gif类型进行压缩
                if ($info->getSize() > 262144 && $fileExt[1] != 'gif') {
                    //根据文件大小设置合适的裁剪比例
                    if ($info->getSize() > 10 * 1024 * 1024) {
                        // 如果文件大小大于10MB，则设置压缩幅度为0.1
                        $amplitude = 0.1;
                    } elseif ($info->getSize() > 8 * 1024 * 1024) {
                        // 如果文件大小大于8MB，则设置压缩幅度为0.2
                        $amplitude = 0.2;
                    } elseif ($info->getSize() > 6 * 1024 * 1024) {
                        // 如果文件大小大于6MB，则设置压缩幅度为0.3
                        $amplitude = 0.3;
                    } elseif ($info->getSize() > 3.5 * 1024 * 1024) {
                        // 如果文件大小大于3.5MB，则设置压缩幅度为0.4
                        $amplitude = 0.4;
                    } elseif ($info->getSize() > 1.5 * 1024 * 1024) {
                        // 如果文件大小大于1.5MB，则设置压缩幅度为0.5
                        $amplitude = 0.5;
                    } else {
                        // 如果文件大小小于等于1.5MB，则设置压缩幅度为0.7
                        $amplitude = 0.7;
                    }
                    //  图片压缩
                    $imageReduce = new ImageReduce(ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'uploads' . DS . $agreement . DS . $info->getSaveName(), $amplitude); // 实例化图片压缩类，传入图片路径和压缩幅度。
                    //  重新生成图片并覆盖
                    $imageReduce->compressImg(ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'uploads' . DS . $agreement . DS . $info->getSaveName()); // 调用压缩方法，将压缩后的图片覆盖原文件。
                    //  清除文件状态缓存
                    clearstatcache(); // 清除PHP的文件状态缓存，以确保后续获取的文件信息是更新后的。
                }
                $uploadTypesOf = true; // 将图片标志设置为true。
            } else { // 如果不是指定的图片类型。
                $info = $file->validate(['ext' => 'mp4,mp3,mpeg,wav,ogg'])->move(ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'uploads'); // 验证文件扩展名是否为指定的音视频格式，然后移动到uploads根目录。
            }
            if ($info) { // 检查文件移动是否成功。
                if ($uploadTypesOf) { // 如果上传的是图片。
                    $localFilePath = ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'uploads' . DS . $agreement . DS . $info->getSaveName(); // 构建包含日期目录的本地文件完整路径。
                } else { // 如果上传的不是图片。
                    $localFilePath = ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'uploads' . DS . $info->getSaveName(); // 构建不包含日期目录的本地文件完整路径。
                }
                return $this->commonThirdPartyUploads($systemOutlying, $picture, $agreement, $info, $uploadTypesOf, $localFilePath); // 调用内部方法处理后续的上传（本地或第三方）。
            } else { // 如果文件移动失败。
                return ['code' => 0, 'status' => 'file move error !']; // 返回一个包含错误信息的数组。
            }
        }
    }

    /**
     * 远程图像上传到第三方存储
     * @param $localFilePath | 本地文件路径
     * @return array|string[]|null
     */
    public function remoteImageUploadToThirdPartyStorage($localFilePath, $fileName) // 定义一个公共方法，用于将远程图片（已下载到本地）上传到第三方存储。
    {
        //  获取系统配置 调用静态方法获取类型为0的上传配置。
        $systemOutlying = self::systemOutlying(0, $this->much_id);
        // 使用本地文件路径创建一个新的File对象，以获取文件信息。
        $info = new File($localFilePath);
        // 设置文件的保存名称。
        $info->setSaveName($fileName);
        // 根据当前日期（年月日）生成一个目录名或标识。
        $agreement = date('Ymd');
        // 调用通用的第三方上传方法，并传递相应参数。
        return $this->commonThirdPartyUploads($systemOutlying, false, $agreement, $info, true, $localFilePath);
    }

    /**
     * 公共第三方上传文件
     * @param $systemOutlying
     * @param $picture
     * @param $agreement
     * @param $info
     * @param $uploadTypesOf
     * @param $localFilePath
     * @return array|string[]|void|null
     */
    private function commonThirdPartyUploads($systemOutlying, $picture, $agreement, $info, $uploadTypesOf, $localFilePath) // 定义一个私有方法，作为处理文件上传到不同存储位置的通用逻辑核心。
    {
        // 获取并转换存储类型为整数
        $quicken_type = intval($systemOutlying['quicken_type']);
        // 根据存储类型进行判断
        switch ($quicken_type) {
            // case 0: 代表本地存储
            case 0: // 如果存储类型为0，表示使用本地服务器存储。
                // 从$_SERVER['HTTP_HOST']（例如'www.example.com:80'）中分离出域名部分。
                $domain = explode(':', $_SERVER['HTTP_HOST']);
                // 获取当前脚本的绝对路径，用于拼接URL
                $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
                // 拼接成完整的、可通过公网访问的URL前缀
                $absRess = "https://{$domain[0]}{$absAddress[0]}";
                // 检查$picture标志是否为true，以确定是否需要将图片信息存入图库数据库。
                if ($picture) {
                    // 启动数据库事务，以保证数据操作的一致性。
                    Db::startTrans();
                    // 尝试执行数据库操作
                    try {
                        // 拼接完整的图片URL地址
                        $imgURLReplace = "{$absRess}static/uploads/{$agreement}/{$info->getSaveName()}";
                        // 将URL中的反斜杠'\'替换为正斜杠'/'，以兼容不同系统
                        $imgURL = str_replace('\\', '/', $imgURLReplace);
                        // 向'gallery'数据表插入一条新的图片记录
                        db('gallery')->insert(['classify_id' => request()->param('gclasid'), 'img_url' => $imgURL, 'img_title' => $info->getSaveName(), 'much_id' => $this->much_id]);
                        // 提交数据库事务
                        Db::commit();
                        // 捕捉可能发生的异常
                    } catch (\Exception $e) {
                        // 如果发生异常，则回滚数据库事务
                        Db::rollback();
                        // 返回包含错误信息的数组
                        return ['code' => 0, 'status' => 'error , ' . $e->getMessage()];
                    }
                    // 如果成功，返回成功状态
                    return ['code' => 1, 'status' => 'success'];
                    // 如果不是上传到图库
                } else {
                    // 判断上传的是否为图片类型
                    if ($uploadTypesOf) {
                        // 如果是图片，则使用包含日期目录的路径
                        $imgURLReplace = "{$absRess}static/uploads/{$agreement}/{$info->getSaveName()}";
                        // 如果不是图片（如音频、视频）
                    } else { // 如果不是图片文件。
                        // 则使用不包含日期目录的路径
                        $imgURLReplace = "{$absRess}static/uploads/{$info->getSaveName()}";
                    }
                    // 统一将URL路径中的反斜杠替换为正斜杠
                    $imgURL = str_replace('\\', '/', $imgURLReplace);
                    // 返回成功状态及文件URL
                    return ['code' => 1, 'status' => 'success', 'url' => $imgURL];
                }
            // 如果存储类型不是0，则处理所有其他的第三方云存储。
            default:
                // 创建一个映射数组，用于将内部的存储类型ID映射到对应的云存储配置信息。
                $configMap = [
                    // 1: 阿里云OSS
                    1 => [ // 阿里云OSS的配置映射。
                        'source' => 'oss_follow',
                        'url_key' => 'oss_url',
                        'keys' => [
                            'accessKeyId' => 'oss_access_key_id',
                            'accessKeySecret' => 'oss_access_key_secret',
                            'endpoint' => 'oss_endpoint',
                            'bucket' => 'oss_bucket',
                        ]
                    ],
                    // 2: 七牛云Kodo
                    2 => [ // 七牛云Kodo的配置映射。
                        'source' => 'qiniu_follow',
                        'url_key' => 'qiniu_url',
                        'keys' => [
                            'accessKey' => 'qiniu_access_key',
                            'secretKey' => 'qiniu_secret_key',
                            'bucket' => 'qiniu_bucket',
                        ]
                    ],
                    // 3: 腾讯云COS
                    3 => [ // 腾讯云COS的配置映射。
                        'source' => 'cos_follow',
                        'url_key' => 'cos_url',
                        'keys' => [
                            'region' => 'cos_region',
                            'appId' => 'cos_app_id',
                            'secretId' => 'cos_secret_id',
                            'secretKey' => 'cos_secret_key',
                            'bucket' => 'cos_bucket',
                        ]
                    ],
                    // 4: 又拍云USS
                    4 => [ // 又拍云USS的配置映射。
                        'source' => 'upyun_follow',
                        'url_key' => 'upyun_url',
                        'keys' => [
                            'service_name' => 'upyun_service_name',
                            'operator_name' => 'upyun_operator_name',
                            'operator_password' => 'upyun_operator_password',
                        ]
                    ],
                    // 5: FTP
                    5 => [ // FTP的配置映射。
                        'source' => 'ftp_follow',
                        'url_key' => 'ftp_url',
                        'keys' => [
                            'ftp_host' => 'ftp_host',
                            'ftp_username' => 'ftp_username',
                            'ftp_password' => 'ftp_password',
                            'ftp_port' => 'ftp_port',
                            'ftp_pasv' => 'ftp_pasv',
                        ]
                    ],
                    // 6: 亚马逊AWS S3
                    6 => [ // 亚马逊AWS S3的配置映射。
                        'source' => 'aws_follow',
                        'url_key' => 'aws_url',
                        'keys' => [
                            'key' => 'aws_key',
                            'secret' => 'aws_secret',
                            'region' => 'aws_region',
                            'endpoint' => 'aws_endpoint',
                            'bucket' => 'aws_bucket',
                            'force_path_style' => 'aws_force_path_style',
                        ]
                    ],
                    // 7: 123云盘
                    7 => [ // 123云盘的配置映射。
                        'source' => 'pan123_follow',
                        'url_key' => 'pan123_url',
                        'keys' => [
                            'client_id' => 'pan123_client_id',
                            'client_secret' => 'pan123_client_secret',
                        ]
                    ]
                ];
                // 检查当前存储类型是否存在于配置映射表中
                if (!isset($configMap[$quicken_type])) {
                    // 如果不存在，则返回错误信息
                    return ['code' => 0, 'status' => 'error', 'msg' => '未知的存储类型'];
                }
                // 根据当前存储类型获取对应的配置规则
                $typeConfig = $configMap[$quicken_type];
                // 从系统配置中获取对应云存储的完整配置数据
                $sourceData = $systemOutlying[$typeConfig['source']];
                // 获取目标配置的键名数组（如：'accessKeyId', 'accessKeySecret'）
                $targetKeys = array_keys($typeConfig['keys']);
                // 使用array_map处理源配置的值，同时检查是否存在，不存在则为null
                $sourceValues = array_map(function ($sourceKey) use ($sourceData) {
                    return isset($sourceData[$sourceKey]) ? $sourceData[$sourceKey] : null;
                }, array_values($typeConfig['keys']));
                // 将目标键名和处理后的源值合并成最终的配置数组
                $config = array_combine($targetKeys, $sourceValues);
                // 移除那些值为null的条目，以防某些配置项不存在
                $config = array_filter($config, function ($value) {
                    // 保留所有非null的值。
                    return !is_null($value);
                });
                // 向配置数组中添加文件扩展名
                $config['extend'] = $info->getExtension();
                // 向配置数组中添加文件的本地路径
                $config['path'] = $localFilePath;
                // 向配置数组中添加云存储的访问URL前缀
                $config['far_url'] = $systemOutlying[$typeConfig['source']][$typeConfig['url_key']];
                // 调用通用的上传组件进行文件传输
                $assembly = Assembly::transfer($quicken_type, $config);
                // 判断是否是上传到图库
                if ($picture) {
                    // 如果第三方上传返回成功状态。
                    if ($assembly['status'] == 'success') {
                        // 开启数据库事务
                        Db::startTrans(); // 启动数据库事务。
                        // 尝试执行数据库操作
                        try {
                            // 向'gallery'数据表插入一条新的图片记录
                            db('gallery')->insert(['classify_id' => request()->param('gclasid'), 'img_url' => $assembly['url'], 'img_title' => $assembly['title'], 'much_id' => $this->much_id]);
                            // 提交数据库事务
                            Db::commit();
                            // 捕捉可能发生的异常
                        } catch (\Exception $e) {
                            // 如果发生异常，则回滚数据库事务
                            Db::rollback();
                            // 返回包含错误信息的数组
                            return ['code' => 0, 'status' => 'error , ' . $e->getMessage()];
                        }
                        // 如果成功，返回成功状态
                        return ['code' => 1, 'status' => 'success'];
                        // 如果上传失败
                    } else {
                        // 返回包含云存储返回的错误信息
                        return ['code' => 0, 'status' => 'error', 'msg' => $assembly['msg']];
                    }
                    // 如果不是上传到图库
                } else {
                    // 直接返回通用上传组件的结果
                    return $assembly;
                }
        }
    }
}
