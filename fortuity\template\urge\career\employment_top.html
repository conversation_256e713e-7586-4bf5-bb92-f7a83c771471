{extend name="/base"/}
{block name="main"}
<style>.span-ranking{background:#477a98;border-radius:3px;padding:6px 10px;font-size:12px;cursor:pointer;color:#f1f4f5;margin-left:5px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 求职招聘置顶列表
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr>
                            <th class="text-center" width="10%">用户信息</th>
                            <th class="text-center" width="15%">岗位名称</th>
                            <th class="text-center" width="20%">支付金额</th>
                            <th class="text-center" width="15%">置顶天数</th>
                            <th class="text-center" width="15%">置顶时间</th>
                            <th class="text-center" width="15%">置顶到期时间</th>
                            <th class="text-center" width="15%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle text-center">
                                {if $vo.user.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user.user_wechat_open_id}&page=1" title="{$vo.user.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user.user_nick_name|emoji_decode}">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="am-text-middle text-center">
                                <a href="{:url('career/employment_found')}&fid={$vo.ei_id}" target="_self">
                                    {$vo.ei_name}
                                </a>
                            </td>
                            <td class="am-text-middle text-center">
                                {$vo.pay_info}
                            </td>
                            <td class="am-text-middle text-center">
                                {$vo.total_top_day}天
                            </td>
                            <td class="am-text-middle text-center">
                                {:date('Y-m-d H:i:s',$vo.add_time)}
                            </td>
                            <td class="am-text-middle text-center">
                                {:date('Y-m-d H:i:s',$vo.ei_top_time)}
                            </td>
                            <td class="am-text-middle text-center">
                                {if $vo.ei_top_time > time()}
                                <span style="cursor: pointer;" onclick="cancelStrike('{$vo.ei_id}');">取消置顶</span>
                                {else}
                                —
                                {/if}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var cancelStrike = function (hid) {
        layer.confirm('您确定要取消置顶这条数据吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('career/employment_cancel_top')}", {'hid': hid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1600}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }
</script>
{/block}