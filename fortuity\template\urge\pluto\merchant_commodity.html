{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 商品列表
        </div>
        <div style="text-align: right;">
            <div class="am-btn-toolbar" style="padding-top: 5px;">
                <a href="javascript:void(0);" class="customize-span" data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 400, height: 240}">
                    <span class="am-icon-adn"></span> 新增商品
                </a>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div id="search-list" style="display: flex;flex-wrap: wrap;width: 100%;min-height: 38px;">
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">ID</label>
                <div class="search-input-inline">
                    <input type="text" name="fid" value="{$fid}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">商品ID</label>
                <div class="search-input-inline">
                    <input type="text" name="pid" value="{$pid}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">商家ID</label>
                <div class="search-input-inline">
                    <input type="text" name="eid" value="{$eid}" class="search-input">
                </div>
            </div>
            <button class="search-wide-pitch search-btn" onclick="turtle();">
                <i class="am-icon-search"></i> 搜 索
            </button>
        </div>
        <div class="am-g" style="margin-top: 15px;">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="14.28%">ID</th>
                            <th width="14.28%">商品名称</th>
                            <th width="14.28%">所属商家</th>
                            <th width="14.28%">绑定时间</th>
                            <th width="14.28%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                {$vo.id}
                            </td>
                            <td>
                                <a href="{:url('marketing/shop')}&egon=0&sid={$vo.product_id}" target="_blank">
                                {$vo.product_name}
                                </a>
                            </td>
                            <td>{$vo.merchant_name}</td>
                            <td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                            <td>
                                <div class="am-btn-toolbar">
                                    <div class="am-btn-group am-btn-group-xs">
                                        <button type="button" class="am-btn am-btn-default am-btn-xs am-text-danger am-hide-sm-only" onclick="symbolDel('{$vo.id}')">
                                            <span class="am-icon-trash-o"></span>
                                            删除
                                        </button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
        <div class="am-modal-dialog" style="background:#fefefe;">
            <div class="am-modal-hd">
                <span style="font-size: 14px;position: absolute;left:12px;top:7px;">新增商品</span>
                <a id="euModalClose" href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd am-form tpl-form-line-form">
                <div class="am-form-group" style="margin-top:35px;">
                    <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">商家ID</label>
                    <div class="am-u-sm-8">
                        <input type="number" name="eid" class="tpl-form-input" style="margin:3px 0 0 -20px;" placeholder="请输入商家编号" oninput="digitalCheck(this);">
                    </div>
                </div>
                <div class="am-form-group" style="margin-top:15px;">
                    <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">商品ID</label>
                    <div class="am-u-sm-8">
                        <input type="number" name="pid" class="tpl-form-input" style="margin:3px 0 0 -20px;" placeholder="请输入商品编号" oninput="digitalCheck(this);">
                    </div>
                </div>
                <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top:10px;">
                    <button type="button" class="am-btn am-btn-sm" style="border:1px solid #ccc;" onclick="sendGifts();">
                        确定保存
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    function turtle() {
        var searchInput = $('#search-list .search-input');
        var n = 0;
        for (var i = 0; i < searchInput.length; i++) {
            if (searchInput[i].value.trim() !== '') {
                n++;
            }
        }
        if (n > 0) {
            location.href = `{:url('pluto/merchant_commodity')}&${searchInput.serialize()}&page={$page}`;
        } else {
            location.href = "{:url('pluto/merchant_commodity')}&page={$page}";
        }
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var isLock = false;
    var sendGifts = function () {
        if (!isLock) {
            isLock = true;
            var eid = $('.am-modal-dialog [name=\'eid\']').val();
            var pid = $('.am-modal-dialog [name=\'pid\']').val();
            $.post("{:url('pluto/new_merchant_commodity')}", {'eid': eid, 'pid': pid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        isLock = false;
                    });
                }
            }, 'json');
        }
    }

    function symbolDel(mid) {
        layer.confirm('您确定要删除当前商品吗', {
            btn: ['确定', '取消']
        }, function () {
            $.post("{:url('pluto/del_merchant_commodity')}", {'fid': mid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

</script>
{/block}