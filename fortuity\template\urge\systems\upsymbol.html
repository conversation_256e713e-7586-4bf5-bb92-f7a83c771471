{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-edit"></span> 编辑广告
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label"> 广告图片</label>
                        <div class="am-u-sm-9">
                            <img src="{$list.playbill_url}" id="shion" onerror="this.src='static/wechat/image_vip_top.jpg'"
                                 onclick="cuonice();" style="width: 200px;height: 80px;cursor: pointer;"/>
                            <button type="button" style="margin-left:10px;margin-right:10px; font-size: 12px;"
                                    onclick="cuonice();">
                                选择图片
                            </button>
                            <small>
                                建议图片尺寸：900*500px ( 轮播广告 ) 270*405px ( 弹窗广告 )
                            </small>
                            <input type="hidden" value="{$list.playbill_url}" name="sngimg">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">选择广告类型</label>
                        <div class="am-u-sm-9">
                            <select id="adType">
                                <option value="0" {if $list.ad_type==0}selected{/if}>轮播广告</option>
                                <option value="1" {if $list.ad_type==1}selected{/if}>弹窗广告</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">选择跳转路径</label>
                        <div class="am-u-sm-9">
                            <select id="sgrant">
                                <option value="0" {if $list.practice_type==0}selected{/if}>内部页面跳转</option>
                                <option value="1" {if $list.practice_type==1}selected{/if}>外部网页跳转</option>
                                <option value="2" {if $list.practice_type==2}selected{/if}>小程序跳转</option>
                            </select>
                        </div>
                    </div>

                    <div id="exhibit-0" class="supply" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">内部链接</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="supply-url-0">
                                <small>
                                    *跳转到圈子请按以下格式填写,id值在圈子列表中获取( /yl_welore/pages/packageA/circle_info/index?id=1 )<br>
                                    *跳转到帖子详情页面请按以下格式填写,id值在帖子信息中获取( /yl_welore/pages/packageA/article/index?id=1&type=0 ) <br>
                                    ( type 帖子类型 0.图文 1.语音 2.视频 3.活动 )<br>
                                    *跳转到话题页面请按以下格式填写,id值在话题列表中获取( /yl_welore/pages/gambit/index?id=1 )<br>
                                </small>
                            </div>
                        </div>
                    </div>

                    <div id="exhibit-1" class="supply" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">外部链接</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="supply-url-1">
                                <small>*此链接为网页外部跳转链接，需要在小程序后台配置业务域名。</small>
                            </div>
                        </div>
                    </div>

                    <div id="exhibit-2" class="supply" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">跳转小程序 APPID</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="supply-url-2" placeholder="请输入跳转小程序的 APPID">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">跳转小程序 URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="wx-url" placeholder="请输入跳转小程序的 URL 地址">
                                <small style="color:red;">不填写默认跳转到首页</small>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">状态</label>
                        <div class="am-u-sm-9">
                            <select id="status">
                                <option value="0" {if $list.status==0}selected{/if}>隐藏</option>
                                <option value="1" {if $list.status==1}selected{/if}>显示</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="scores" value="{$list.scores}" placeholder="请输入排序数字">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-push-3">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    $(function () {

        var img = new Image();
        img.src = '{$list.playbill_url}';
        if (img.width > img.height) {
            $('#shion').css({'width': '220px', 'height': 'auto'});
        } else if (img.width < img.height) {
            $('#shion').css({'width': 'auto', 'height': '220px'});
        } else {
            $('#shion').css({'width': '150px', 'height': '150px'});
        }

        var shing = $('#sgrant').val();
        if (shing == 0) {
            $('#exhibit-0').show();
            $('#supply-url-0').val('{$list.url}');
        } else if (shing == 1) {
            $('#exhibit-1').show();
            $('#supply-url-1').val('{$list.url}');
        } else if (shing == 2) {
            $('#exhibit-2').show();
            $('#supply-url-2').val('{$list.url}');
            $('#wx-url').val('{$list.wx_app_url}');
        }
        $('#sgrant').change(function () {
            $('.supply').hide();
            var catype = $(this).val();
            if (catype == 0) {
                $('#exhibit-0').show();
            } else if (catype == 1) {
                $('#exhibit-1').show();
            } else if (catype == 2) {
                $('#exhibit-2').show();
            }
        });
    });

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        var img = new Image();
        img.src = eurl;
        if (img.width > img.height) {
            $('#shion').css({'width': '220px', 'height': 'auto'});
        } else if (img.width < img.height) {
            $('#shion').css({'width': 'auto', 'height': '220px'});
        } else {
            $('#shion').css({'width': '150px', 'height': '150px'});
        }
        $("[name='sngimg']").val(eurl);
        $('#shion').attr('src', eurl);
        $("[name='sngimg']").val(eurl);
        layer.closeAll();
    }

    var excheck = function (sngimg, supplyUrl, scores) {
        if (sngimg == '' || sngimg == 'undefined' || sngimg == null) {
            layer.msg('请上传广告图标');
            return false;
        }
        if (scores == '' || scores == 'undefined' || scores == null) {
            layer.msg('排序不能为空');
            return false;
        }
        if (scores > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            return false;
        }
        return true;
    }

    var slock = false;

    var holdSave = function () {
        if (!slock) {
            var sngimg = $.trim($("[name='sngimg']").val());
            var adType = $('#adType').val();
            var wxappUrl = "";
            var sgrant = $.trim($('#sgrant').val());
            if (sgrant == 0) {
                var supplyUrl = $('#supply-url-0').val();
            } else if (sgrant == 1) {
                var supplyUrl = $('#supply-url-1').val();
            } else if (sgrant == 2) {
                var supplyUrl = $('#supply-url-2').val();
                wxappUrl = $.trim($('#wx-url').val());
            }
            var status = $.trim($('#status').val());
            var scores = $.trim($('#scores').val());
            if (excheck(sngimg, supplyUrl, scores)) {
                slock = true;
                $.ajax({
                    type: "post",
                    url: "{:url('upsymbol')}",
                    data: {
                        'usid': '{$list.id}',
                        'ad_type': adType,
                        'playbill_url': sngimg,
                        'practice_type': sgrant,
                        'url': supplyUrl,
                        'wx_app_url': wxappUrl,
                        'status': status,
                        'scores': scores
                    },
                    dataType: 'json',
                    success: function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.href = "{:url('systems/symbol')}";
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                slock = false;
                            });
                        }
                    }
                });
            }
        }
    }
</script>
{/block}