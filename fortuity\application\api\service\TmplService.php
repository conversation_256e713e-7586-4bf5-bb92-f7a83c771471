<?php

namespace app\api\service;

use app\api\controller\Wechat;
use app\common\Gyration;
use think\Db;

class TmplService
{
    /**
     * 发送模版消息
     */
    public function add_template($data)
    {
        //获取后台APPID
        $util=new Util();
        $access_token=$util->getWchatAcctoken($data['much_id']);
        //通知
        $tmpl_url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" . $access_token;
        //接收者用户OPENID
        $user_open_id = Db::name('user')->where('id', $data['user_id'])->where('much_id', $data['much_id'])->field('user_wechat_open_id')->find();
        //查询模版消息表
        $tmpl_sql = Db::name('subscribe')->where('much_id', $data['much_id'])->find();

        $json = json_decode($tmpl_sql['parallelism_data'], true);
        $wx_popular = new Wechat();
        //查询公众号用户open_id
        $popular_user = Db::name('wx_popular_bind_user')->where('user_id', $data['user_id'])->where('much_id', $data['much_id'])->find();
        //新的评论提醒 （文章标题、评论用户、评论内容、评论时间）
        if ($data['at_id'] == 'YL0099') {
            if (!empty($popular_user)) {
                $key = [
                    'open_id' => $popular_user['open_id'],
                    'first' => $data['keyword1'],
                    'keyword1' => '审核提醒',
                    'keyword2' => '请前往小程序内审核！',
                    'remark' => '查看详情',
                    'page' => $data['page'],
                ];
                return $wx_popular->send_template($key, $data['much_id']);
            }
        }
        //新的评论提醒 （文章标题、评论用户、评论内容、评论时间）
        if ($data['at_id'] == 'YL0001') {
            $tmpl_data = array(
                'touser' => $user_open_id['user_wechat_open_id'],
                'template_id' => $json['YL0001'],
                'page' => $data['page'],
                'data' => array(
                    'thing1' => ['value' => $data['keyword1']],
                    'thing5' => ['value' => $data['keyword2']],
                    'thing2' => ['value' => $data['keyword3']],
                    'time3' => ['value' => $data['keyword4']]
                ),
            );
            if (!empty($popular_user)) {
                $key = [
                    'open_id' => $popular_user['open_id'],
                    'first' => $data['keyword2'] . '评论了您的帖子：' . $data['keyword1'],
                    'keyword1' => '评论提醒',
                    'keyword2' => $data['keyword3'],
                    'remark' => '查看详情',
                    'page' => $data['page'],
                ];
                $wx_popular->send_template($key, $data['much_id']);
            }
        }
        //动态点赞通知（帖子标题、点赞用户、点赞时间）
        if ($data['at_id'] == 'YL0002') {
            $tmpl_data = array(
                'touser' => $user_open_id['user_wechat_open_id'],
                'template_id' => $json['YL0002'],
                'page' => $data['page'],
                'data' => array(
                    'thing3' => ['value' => emoji_decode($data['keyword1'])],
                    'name1' => ['value' => $data['keyword2']],
                    'date2' => ['value' => $data['keyword3']]
                ),
            );
//            if (!empty($popular_user)) {
//                $key = [
//                    'open_id' => $popular_user['open_id'],
//                    'first' => $data['keyword2'] . '赞了您的帖子：' . $data['keyword1'],
//                    'keyword1' => '点赞提醒',
//                    'keyword2' => '赞+1',
//                    'remark' => '前往小程序查看！',
//                    'page' => $data['page'],
//                ];
//                $wx_popular->send_template($key, $data['much_id']);
//            }
        }
        //帖子被收藏通知（收藏帖子、收藏用户、收藏时间）
        if ($data['at_id'] == 'YL0003') {
            $tmpl_data = array(
                'touser' => $user_open_id['user_wechat_open_id'],
                'template_id' => $json['YL0003'],
                'page' => $data['page'],
                'data' => array(
                    'thing3' => ['value' => $data['keyword1']],
                    'name1' => ['value' => $data['keyword2']],
                    'time2' => ['value' => $data['keyword3']]
                ),
            );
//            if (!empty($popular_user)) {
//                $key = [
//                    'open_id' => $popular_user['open_id'],
//                    'first' => $data['keyword2'] . '收藏您的帖子：' . $data['keyword1'],
//                    'keyword1' => '收藏提醒',
//                    'keyword2' => '收藏+1',
//                    'remark' => '前往小程序查看！',
//                    'page' => $data['page'],
//                ];
//                $wx_popular->send_template($key, $data['much_id']);
//            }
        }
        //新的回复提醒（留言主题、用户、回复内容、回复时间）
        if ($data['at_id'] == 'YL0004') {
            $tmpl_data = array(
                'touser' => $user_open_id['user_wechat_open_id'],
                'template_id' => $json['YL0004'],
                'page' => $data['page'],
                'data' => array(
                    'thing1' => ['value' => $data['keyword1']],
                    'name6' => ['value' => $data['keyword2']],
                    'thing2' => ['value' => $data['keyword3']],
                    'date3' => ['value' => $data['keyword4']]
                ),
            );
            if (!empty($popular_user)) {
                $key = [
                    'open_id' => $popular_user['open_id'],
                    'first' => $data['keyword1'],
                    'keyword1' => '评论回复提醒',
                    'keyword2' => $data['keyword2'] . '回复了',
                    'remark' => '查看详情',
                    'page' => $data['page'],
                ];
                $wx_popular->send_template($key, $data['much_id']);
            }
        }
        //申请加入通知（圈子名称、申请人、温馨提示、申请时间）
        if ($data['at_id'] == 'YL0005') {
            $tmpl_data = array(
                'touser' => $user_open_id['user_wechat_open_id'],
                'template_id' => $json['YL0005'],
                'page' => $data['page'],
                'data' => array(
                    'thing1' => ['value' => $data['keyword1']],
                    'name2' => ['value' => $data['keyword2']],
                    'thing4' => ['value' => $data['keyword3']],
                    'time3' => ['value' => $data['keyword4']]
                ),
            );
            if (!empty($popular_user)) {
                $key = [
                    'open_id' => $popular_user['open_id'],
                    'first' => $data['keyword2'] . '申请加入：' . $data['keyword1'],
                    'keyword1' => '申请加入通知',
                    'keyword2' => $data['keyword3'],
                    'remark' => '查看详情',
                    'page' => $data['page'],
                ];
                $wx_popular->send_template($key, $data['much_id']);
            }
        }
        //留言提醒 （留言内容、时间、留言者）
        if ($data['at_id'] == 'YL0006') {
            $tmpl_data = array(
                'touser' => $user_open_id['user_wechat_open_id'],
                'template_id' => $json['YL0006'],
                'page' => $data['page'],
                'data' => array(
                    'thing1' => ['value' => $data['keyword1']],
                    'time2' => ['value' => $data['keyword2']],
                    'name3' => ['value' => $data['keyword3']]
                ),
            );
            if (!empty($popular_user)) {
                $key = [
                    'open_id' => $popular_user['open_id'],
                    'first' => $data['keyword3'] . '发来一条留言',
                    'keyword1' => '留言提醒',
                    'keyword2' => $data['keyword1'],
                    'remark' => '查看详情',
                    'page' => $data['page'],
                ];
                $wx_popular->send_template($key, $data['much_id']);
            }
        }
        //收到赞赏通知（赞赏详情、赞赏时间）
        if ($data['at_id'] == 'YL0007') {
            $tmpl_data = array(
                'touser' => $user_open_id['user_wechat_open_id'],
                'template_id' => $json['YL0007'],
                'page' => $data['page'],
                'data' => array(
                    'thing2' => ['value' => $data['keyword1']],
                    'date1' => ['value' => $data['keyword2']]
                ),
            );
            if (!empty($popular_user)) {
                $key = [
                    'open_id' => $popular_user['open_id'],
                    'first' => $data['keyword1'],
                    'keyword1' => '收到赞赏通知',
                    'keyword2' => '礼物+1',
                    'remark' => '查看详情',
                    'page' => $data['page'],
                ];
                $wx_popular->send_template($key, $data['much_id']);
            }
        }
        //审核结果通知（审核内容、审核结果、审核时间）
        if ($data['at_id'] == 'YL0008') {
            $tmpl_data = array(
                'touser' => $user_open_id['user_wechat_open_id'],
                'template_id' => $json['YL0008'],
                'page' => $data['page'],
                'data' => array(
                    'thing5' => ['value' => $data['keyword1']],
                    'phrase1' => ['value' => $data['keyword2']],
                    'date4' => ['value' => $data['keyword3']]
                ),
            );
            if (!empty($popular_user)) {
                $key = [
                    'open_id' => $popular_user['open_id'],
                    'first' => $data['keyword1'],
                    'keyword1' => '审核结果通知',
                    'keyword2' =>  $data['keyword2'],
                    'remark' => '查看详情',
                    'page' => $data['page'],
                ];
                $wx_popular->send_template($key, $data['much_id']);
            }
        }
        //站内信提醒 （站内信内容、时间）
        if ($data['at_id'] == 'YL0009') {
            $tmpl_data = array(
                'touser' => $user_open_id['user_wechat_open_id'],
                'template_id' => $json['YL0009'],
                'page' => $data['page'],
                'data' => array(
                    'thing4' => ['value' => $data['keyword1']],
                    'date2' => ['value' => $data['keyword2']]
                ),
            );
            if (!empty($popular_user)) {
                $key = [
                    'open_id' => $popular_user['open_id'],
                    'first' => $data['keyword1'],
                    'keyword1' => '站内信提醒',
                    'keyword2' =>  '消息提醒',
                    'remark' => '查看详情',
                    'page' => $data['page'],
                ];
                $wx_popular->send_template($key, $data['much_id']);
            }
        }
        //事件处理进度通知（事件内容、处理意见）
        if ($data['at_id'] == 'YL0010') {
            $tmpl_data = array(
                'touser' => $user_open_id['user_wechat_open_id'],
                'template_id' => $json['YL0010'],
                'page' => $data['page'],
                'data' => array(
                    'thing1' => ['value' => $data['keyword1']],
                    'thing5' => ['value' => $data['keyword2']],
                ),
            );
            if (!empty($popular_user)) {
                $key = [
                    'open_id' => $popular_user['open_id'],
                    'first' => $data['keyword1'],
                    'keyword1' => '处理进度通知',
                    'keyword2' =>  $data['keyword2'],
                    'remark' => '查看详情',
                    'page' => $data['page'],
                ];
                $wx_popular->send_template($key, $data['much_id']);
            }
        }
        //认证结果通知
        if ($data['at_id'] == 'YL0011') {
            $tmpl_data = array(
                'touser' => $user_open_id['user_wechat_open_id'],
                'template_id' => $json['YL0011'],
                'page' => $data['page'],
                'data' => array(
                    'date3' => ['value' => $data['keyword1']],
                    'thing2' => ['value' => $data['keyword2']],
                    'phrase1' => ['value' => $data['keyword3']],
                    'thing4' => ['value' => $data['keyword4']],
                ),
            );
            if (!empty($popular_user)) {
                $key = [
                    'open_id' => $popular_user['open_id'],
                    'first' => '您申请的：'.$data['keyword2'],
                    'keyword1' => '认证结果通知',
                    'keyword2' =>  $data['keyword3'],
                    'remark' => '查看详情',
                    'page' => $data['page'],
                ];
                $wx_popular->send_template($key, $data['much_id']);
            }
        }
        $result = Gyration::_requestPost($tmpl_url, json_encode($tmpl_data));
        $error_json = json_decode($result, true);
        if ($error_json['errcode'] != 0 && $data['at_id'] == 'YL0004') {
            $tmpl_data = array(
                'touser' => $user_open_id['user_wechat_open_id'],
                'template_id' => $json['YL0004'],
                'page' => $data['page'],
                'data' => array(
                    'thing1' => ['value' => $data['keyword1']],
                    'thing7' => ['value' => $data['keyword2']],
                    'thing2' => ['value' => $data['keyword3']],
                    'date3' => ['value' => $data['keyword4']]
                ),
            );
            $result = Gyration::_requestPost($tmpl_url, json_encode($tmpl_data));
            $error_json = json_decode($result, true);
        }
        return $error_json['errcode'];
    }

}