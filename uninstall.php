<?php
$sql =<<<ETO
DROP TABLE IF EXISTS `yl_welore_advertise`;
DROP TABLE IF EXISTS `yl_welore_attest`;
DROP TABLE IF EXISTS `yl_welore_authority`;
DROP TABLE IF EXISTS `yl_welore_avatar_frame`;
DROP TABLE IF EXISTS `yl_welore_brisk_team`;
DROP TABLE IF EXISTS `yl_welore_call_phone_config`;
DROP TABLE IF EXISTS `yl_welore_camouflage_card`;
DROP TABLE IF EXISTS `yl_welore_combination`;
DROP TABLE IF EXISTS `yl_welore_config`;
DROP TABLE IF EXISTS `yl_welore_contrar`;
DROP TABLE IF EXISTS `yl_welore_copyright`;
DROP TABLE IF EXISTS `yl_welore_design`;
DROP TABLE IF EXISTS `yl_welore_easy_info_config`;
DROP TABLE IF EXISTS `yl_welore_easy_info_list`;
DROP TABLE IF EXISTS `yl_welore_easy_info_shop_assistant`;
DROP TABLE IF EXISTS `yl_welore_easy_info_shop_order`;
DROP TABLE IF EXISTS `yl_welore_easy_info_shop_order_verify_log`;
DROP TABLE IF EXISTS `yl_welore_easy_info_shop_products`;
DROP TABLE IF EXISTS `yl_welore_easy_info_type`;
DROP TABLE IF EXISTS `yl_welore_employment_item`;
DROP TABLE IF EXISTS `yl_welore_employment_item_config`;
DROP TABLE IF EXISTS `yl_welore_employment_item_top`;
DROP TABLE IF EXISTS `yl_welore_employment_item_type`;
DROP TABLE IF EXISTS `yl_welore_employment_user_attention`;
DROP TABLE IF EXISTS `yl_welore_event_raffle`;
DROP TABLE IF EXISTS `yl_welore_feeling`;
DROP TABLE IF EXISTS `yl_welore_feeling_pay`;
DROP TABLE IF EXISTS `yl_welore_feeling_stipulate`;
DROP TABLE IF EXISTS `yl_welore_gallery`;
DROP TABLE IF EXISTS `yl_welore_gallery_classify`;
DROP TABLE IF EXISTS `yl_welore_gambit`;
DROP TABLE IF EXISTS `yl_welore_help`;
DROP TABLE IF EXISTS `yl_welore_home_topping`;
DROP TABLE IF EXISTS `yl_welore_lament`;
DROP TABLE IF EXISTS `yl_welore_login_checking`;
DROP TABLE IF EXISTS `yl_welore_lost_item`;
DROP TABLE IF EXISTS `yl_welore_lost_item_config`;
DROP TABLE IF EXISTS `yl_welore_lost_item_reply`;
DROP TABLE IF EXISTS `yl_welore_lost_item_top`;
DROP TABLE IF EXISTS `yl_welore_lost_item_type`;
DROP TABLE IF EXISTS `yl_welore_medal`;
DROP TABLE IF EXISTS `yl_welore_micro_series_config`;
DROP TABLE IF EXISTS `yl_welore_micro_series_content_list`;
DROP TABLE IF EXISTS `yl_welore_micro_series_info_list`;
DROP TABLE IF EXISTS `yl_welore_micro_series_info_review`;
DROP TABLE IF EXISTS `yl_welore_micro_series_type`;
DROP TABLE IF EXISTS `yl_welore_micro_series_unlock_paid_content_list`;
DROP TABLE IF EXISTS `yl_welore_micro_series_user_like`;
DROP TABLE IF EXISTS `yl_welore_motion`;
DROP TABLE IF EXISTS `yl_welore_mouldboard`;
DROP TABLE IF EXISTS `yl_welore_much_admin`;
DROP TABLE IF EXISTS `yl_welore_mucilage`;
DROP TABLE IF EXISTS `yl_welore_mucilage_use_annaly`;
DROP TABLE IF EXISTS `yl_welore_needle`;
DROP TABLE IF EXISTS `yl_welore_netdisc`;
DROP TABLE IF EXISTS `yl_welore_netdisc_belong`;
DROP TABLE IF EXISTS `yl_welore_netdisc_config`;
DROP TABLE IF EXISTS `yl_welore_netdisc_sell`;
DROP TABLE IF EXISTS `yl_welore_netdisc_storage`;
DROP TABLE IF EXISTS `yl_welore_netdisc_user_volume`;
DROP TABLE IF EXISTS `yl_welore_new_user_task`;
DROP TABLE IF EXISTS `yl_welore_new_user_task_record`;
DROP TABLE IF EXISTS `yl_welore_outlying`;
DROP TABLE IF EXISTS `yl_welore_outlying_allude`;
DROP TABLE IF EXISTS `yl_welore_paper`;
DROP TABLE IF EXISTS `yl_welore_paper_buy_user`;
DROP TABLE IF EXISTS `yl_welore_paper_complaint`;
DROP TABLE IF EXISTS `yl_welore_paper_heat_banner_ads`;
DROP TABLE IF EXISTS `yl_welore_paper_heat_config`;
DROP TABLE IF EXISTS `yl_welore_paper_heat_put_top`;
DROP TABLE IF EXISTS `yl_welore_paper_red_packet`;
DROP TABLE IF EXISTS `yl_welore_paper_reply`;
DROP TABLE IF EXISTS `yl_welore_paper_reply_duplex`;
DROP TABLE IF EXISTS `yl_welore_paper_review_common_terms`;
DROP TABLE IF EXISTS `yl_welore_paper_review_config`;
DROP TABLE IF EXISTS `yl_welore_paper_review_score`;
DROP TABLE IF EXISTS `yl_welore_paper_smingle`;
DROP TABLE IF EXISTS `yl_welore_paper_vote`;
DROP TABLE IF EXISTS `yl_welore_paper_wechat_channel_video`;
DROP TABLE IF EXISTS `yl_welore_phone_blacklist`;
DROP TABLE IF EXISTS `yl_welore_polling`;
DROP TABLE IF EXISTS `yl_welore_prompt_count`;
DROP TABLE IF EXISTS `yl_welore_prompt_msg`;
DROP TABLE IF EXISTS `yl_welore_raws_setting`;
DROP TABLE IF EXISTS `yl_welore_reissue`;
DROP TABLE IF EXISTS `yl_welore_shaky_fission`;
DROP TABLE IF EXISTS `yl_welore_shop`;
DROP TABLE IF EXISTS `yl_welore_shop_attribute`;
DROP TABLE IF EXISTS `yl_welore_shop_order`;
DROP TABLE IF EXISTS `yl_welore_shop_type`;
DROP TABLE IF EXISTS `yl_welore_shop_vested`;
DROP TABLE IF EXISTS `yl_welore_special_nickname`;
DROP TABLE IF EXISTS `yl_welore_sprout`;
DROP TABLE IF EXISTS `yl_welore_sprout_reply`;
DROP TABLE IF EXISTS `yl_welore_subscribe`;
DROP TABLE IF EXISTS `yl_welore_sweepstake_config`;
DROP TABLE IF EXISTS `yl_welore_sweepstake_list`;
DROP TABLE IF EXISTS `yl_welore_sweepstake_participate`;
DROP TABLE IF EXISTS `yl_welore_sweepstake_winning`;
DROP TABLE IF EXISTS `yl_welore_task`;
DROP TABLE IF EXISTS `yl_welore_task_logger`;
DROP TABLE IF EXISTS `yl_welore_template_slot`;
DROP TABLE IF EXISTS `yl_welore_template_slot_valve`;
DROP TABLE IF EXISTS `yl_welore_territory`;
DROP TABLE IF EXISTS `yl_welore_territory_interest`;
DROP TABLE IF EXISTS `yl_welore_territory_learned`;
DROP TABLE IF EXISTS `yl_welore_territory_petition`;
DROP TABLE IF EXISTS `yl_welore_tribute`;
DROP TABLE IF EXISTS `yl_welore_tribute_taxation`;
DROP TABLE IF EXISTS `yl_welore_used_goods_item`;
DROP TABLE IF EXISTS `yl_welore_used_goods_item_config`;
DROP TABLE IF EXISTS `yl_welore_used_goods_item_reply`;
DROP TABLE IF EXISTS `yl_welore_used_goods_item_top`;
DROP TABLE IF EXISTS `yl_welore_used_goods_item_type`;
DROP TABLE IF EXISTS `yl_welore_user`;
DROP TABLE IF EXISTS `yl_welore_user_amount`;
DROP TABLE IF EXISTS `yl_welore_user_applaud`;
DROP TABLE IF EXISTS `yl_welore_user_attest`;
DROP TABLE IF EXISTS `yl_welore_user_avatar_frame`;
DROP TABLE IF EXISTS `yl_welore_user_banned`;
DROP TABLE IF EXISTS `yl_welore_user_better_logger`;
DROP TABLE IF EXISTS `yl_welore_user_blacklist`;
DROP TABLE IF EXISTS `yl_welore_user_brisk_team`;
DROP TABLE IF EXISTS `yl_welore_user_camouflage_card`;
DROP TABLE IF EXISTS `yl_welore_user_collect`;
DROP TABLE IF EXISTS `yl_welore_user_currency_conversion`;
DROP TABLE IF EXISTS `yl_welore_user_exp_glory_logger`;
DROP TABLE IF EXISTS `yl_welore_user_feeling`;
DROP TABLE IF EXISTS `yl_welore_user_form_info`;
DROP TABLE IF EXISTS `yl_welore_user_forwarded`;
DROP TABLE IF EXISTS `yl_welore_user_honorary`;
DROP TABLE IF EXISTS `yl_welore_user_invitation_code`;
DROP TABLE IF EXISTS `yl_welore_user_leaderboard`;
DROP TABLE IF EXISTS `yl_welore_user_leave_word`;
DROP TABLE IF EXISTS `yl_welore_user_level`;
DROP TABLE IF EXISTS `yl_welore_user_maker`;
DROP TABLE IF EXISTS `yl_welore_user_medal`;
DROP TABLE IF EXISTS `yl_welore_user_mutter`;
DROP TABLE IF EXISTS `yl_welore_user_punch`;
DROP TABLE IF EXISTS `yl_welore_user_punch_range`;
DROP TABLE IF EXISTS `yl_welore_user_raffle_records`;
DROP TABLE IF EXISTS `yl_welore_user_recent_contacts`;
DROP TABLE IF EXISTS `yl_welore_user_red_packet`;
DROP TABLE IF EXISTS `yl_welore_user_respond_invitation`;
DROP TABLE IF EXISTS `yl_welore_user_screenshot`;
DROP TABLE IF EXISTS `yl_welore_user_serial`;
DROP TABLE IF EXISTS `yl_welore_user_smail`;
DROP TABLE IF EXISTS `yl_welore_user_special_nickname`;
DROP TABLE IF EXISTS `yl_welore_user_subsidy`;
DROP TABLE IF EXISTS `yl_welore_user_templet_history`;
DROP TABLE IF EXISTS `yl_welore_user_track`;
DROP TABLE IF EXISTS `yl_welore_user_trailing`;
DROP TABLE IF EXISTS `yl_welore_user_violation`;
DROP TABLE IF EXISTS `yl_welore_user_vote`;
DROP TABLE IF EXISTS `yl_welore_user_watch_ads`;
DROP TABLE IF EXISTS `yl_welore_user_withdraw_money`;
DROP TABLE IF EXISTS `yl_welore_version`;
DROP TABLE IF EXISTS `yl_welore_version_overdue`;
DROP TABLE IF EXISTS `yl_welore_video_analysis_config`;
DROP TABLE IF EXISTS `yl_welore_wx_popular`;
DROP TABLE IF EXISTS `yl_welore_wx_popular_bind_user`;
ETO;
pdo_run($sql);