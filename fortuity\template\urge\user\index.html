{extend name="/base"/}
{block name="main"}
<style>.am-table>thead:first-child>tr:first-child>th,.am-table>tbody>tr>td{text-align: center;}.bunch{border:1px solid;padding:3px 6px;border-radius:5px;font-size:12px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}.span-ranking{background:#477a98;border-radius:3px;padding:6px 10px;font-size:12px;cursor:pointer;color:#f1f4f5;}.escalator a{margin:0 4%;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-users"></span> 用户列表
        </div>
    </div>
    <div class="tpl-block">
        <div id="search-list" style="display: flex;flex-wrap: wrap;width: 100%;min-height: 38px;">
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">UID</label>
                <div class="search-input-inline">
                    <input type="text" name="uid" value="{$uid}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">用户昵称</label>
                <div class="search-input-inline">
                    <input type="text" name="userName" value="{$userName}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">openid</label>
                <div class="search-input-inline">
                    <input type="text" name="openid" value="{$openid}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">手机号</label>
                <div class="search-input-inline">
                    <input type="text" name="phone" value="{$phone}" class="search-input">
                </div>
            </div>
            <button class="search-wide-pitch search-btn" onclick="turtle();">
                <i class="am-icon-search"></i> 搜 索
            </button>
        </div>
        <div class="am-g">
            <div class="am-u-sm-6 am-u-md-6">
                <div class="am-btn-toolbar" style="margin: 0px 0px 15px 20px;">
                    <div class="am-btn-group am-btn-group-xs resth">
                        <a href="{:url('user/index')}&egon=0" class="cust-btn cust-btn-one {if $egon==0}cust-btn-activate{/if}">全部</a>
                        <a href="{:url('user/index')}&egon=1" class="cust-btn {if $egon==1}cust-btn-activate{/if}">状态正常</a>
                        <a href="{:url('user/index')}&egon=2" class="cust-btn {if $egon==2}cust-btn-activate{/if}">状态封禁</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g" style="margin-top: 10px;">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table table-main am-table-radius">
                        <thead>
                        <tr>
                            <th width="6%">UID</th>
                            <th width="8%">用户头像</th>
                            <th width="12%">用户昵称</th>
                            <th width="10%">用户等级</th>
                            <th width="17%">openid</th>
                            <th width="8%">{$defaultNavigate.confer}</th>
                            <th width="8%">{$defaultNavigate.currency}</th>
                            <th width="8%">荣誉点</th>
                            <th width="8%">用户状态</th>
                            <th width="10%">授权时间</th>
                            <th width="6%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">{$vo.id}</td>
                            <td class="am-text-middle">
                                {if $vo.user_head_sculpture && $vo.user_head_sculpture !='/yl_welore/style/icon/default.png'}
                                <img src="{$vo.user_head_sculpture}" style="width: 50px;height: 50px;border-radius: 50%;">
                                {else}
                                <img src="{:urlBridging('static/disappear/tourist.png')}" style="width: 50px;height: 50px;border-radius: 50%;">
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                <span title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </span>
                            </td>
                            <td class="am-text-middle">
                                <span title="当前经验值：{$vo.experience}">
                                Lv.{$vo.level}
                                </span>
                            </td>
                            <td class="am-text-middle">{$vo.user_wechat_open_id}</td>
                            <td class="am-text-middle">{$vo.fraction}</td>
                            <td class="am-text-middle">{$vo.conch}</td>
                            <td class="am-text-middle">{$vo.honor_point}</td>
                            <td class="am-text-middle">
                                {if $vo.status == 0}
                                <span style="color: red;">禁止</span>
                                {else}
                                <span style="color: lightgreen;">正常</span>
                                {/if}
                            </td>
                            <td class="am-text-middle"> {:date('Y-m-d H:i:s',$vo.user_reg_time)}</td>
                            <td class="am-text-middle">
                                <span class="am-icon-angle-down am-icon-sm" style="cursor: pointer;margin-left: 8px;" onclick="gains(this,'{$vo.id}');"></span>
                            </td>
                        </tr>
                        <tr id="kshatri-{$vo.id}" class="shild" style="display: none;background: #f3f4f6;text-align: center;">
                            </td>
                            <td class="escalator" colspan="11">
                                <a href="{:url('user/material')}&usid={$vo.id}" class="bunch" target="_blank">个人资料</a>
                                <a href="{:url('manual/dailyTask')}&uid={$vo.id}" class="bunch" target="_blank">任务完成详情</a>
                                <a href="{:url('essay/index')}&egon=0&hazy_name={$vo.user_wechat_open_id}&page=1" class="bunch" target="_blank">发帖记录</a>
                                <a href="{:url('user/wallet')}&usid={$vo.id}" class="bunch" target="_blank">钱包明细</a>
                                <a href="{:url('manual/expGloryRecord')}&uid={$vo.id}" class="bunch" target="_blank">经验荣誉获得记录</a>
                                {if $vo.status==1}
                                <a href="javascript:void(0);" class="bunch" style="color: red;" onclick="uronet('{$vo.id}','0');" >封禁</a>
                                {else}
                                <a href="javascript:void(0);" class="bunch" style="color: green;" onclick="uronet('{$vo.id}','1');" >解封</a>
                                {/if}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var gains = function (obj, suid) {
        var brahman = $(obj).attr('class');
        $('.am-icon-angle-up').attr('class', 'am-icon-angle-down am-icon-sm');
        $('.shild').hide();
        if (brahman != 'am-icon-angle-up am-icon-sm') {
            $(obj).attr('class', 'am-icon-angle-up am-icon-sm');
            $('#kshatri-' + suid).show();
        }
    }

    var uronet = function (uid, their) {
        if (their == 0) {
            layer.confirm('您确定要封禁当前用户吗？', {
                btn: ['确定', '取消'], title: '提示'
            }, function (index) {
                layer.close(index);
                forbidCause(uid, their);
            }, function (index) {
                layer.close(index);
            });
        } else {
            layer.confirm('您确定要解封当前用户吗？', {
                btn: ['确定', '取消'], title: '提示'
            }, function () {
                pulate(uid, their);
            }, function (index) {
                layer.close(index);
            });
        }
    }

    var forbidCause = function (uid, their) {
        layer.prompt({title: '请输入封禁理由', formType: 0}, function (cause, index) {
            cause = $.trim(cause);
            if (cause == '') {
                return;
            }
            pulate(uid, their, cause);
        });
    }


    var pulate = function (uid, their, cause) {
        $.post("{:url('user/pulate')}", {nuid: uid, ntheir: their, cause: cause}, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000});
            }
        }, 'json');
    }

    function turtle() {
        var searchInput = $('#search-list .search-input');
        var n = 0;
        for (var i = 0; i < searchInput.length; i++) {
            if (searchInput[i].value.trim() !== '') {
                n++;
            }
        }
        if (n > 0) {
            location.href = `{:url('user/index')}&${searchInput.serialize()}&page={$page}`;
        } else {
            location.href = "{:url('user/index')}&page={$page}";
        }
    }
</script>
{/block}