$(function () {

    var dataType = $('body').attr('data-type');
    for (key in pageData) {
        if (key == dataType) {
            pageData[key]();
        }
    }

    $('.tpl-switch').find('.tpl-switch-btn-view').on('click', function () {
        $(this).prev('.tpl-switch-btn').prop("checked", function () {
            if ($(this).is(':checked')) {
                return false
            } else {
                return true
            }
        })

    })
})

// 页面数据
var pageData = {
    'index': function indexData() {
        var sinvite_url = location.href.split('/web/index.php');
        sinvite_url = sinvite_url[0] + '/web/index.php?s=/urge/index/userCount';
        var curDate = new Date();
        var curMonth = curDate.getMonth();
        curDate.setMonth(curMonth + 1);
        curDate.setDate(0);
        var echartsA = echarts.init(document.getElementById('tpl-echarts-A'));
        var echartData = [];
        if (curDate.getDate() > 30) {
            for (var i = 0; i < 31; i++) {
                echartData.push(i + 1 + '日');
            }
        } else {
            for (var i = 0; i < 30; i++) {
                echartData.push(i + 1 + '日');
            }
        }
        $.ajax({
            url: sinvite_url,
            async: true,
            type: 'post',
            dataType: 'json',
            success: function (data) {
                // --- Final, Official ECharts Configuration ---
                var option = {
                    // Define a global color palette. The first color will be used for the first series.
                    color: ['#8B5CF6'],

                    title: {
                        text: '本月新增用户',
                        left: 'left',
                        textStyle: {
                            fontWeight: 'bold',
                            color: '#343a40'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            label: {
                                backgroundColor: '#6a7985'
                            }
                        }
                    },
                    toolbox: {
                        feature: {
                            restore: {},
                            saveAsImage: {}
                        }
                    },
                    dataZoom: [
                        {
                            type: 'inside',
                            start: 0,
                            end: 100
                        },
                        {
                            start: 0,
                            end: 100
                        }
                    ],
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: echartData,
                        axisLine: { lineStyle: { color: '#adb5bd' } },
                        axisLabel: { color: '#6c757d' }
                    },
                    yAxis: {
                        type: 'value',
                        splitLine: { lineStyle: { color: '#e9ecef', type: 'dashed' } },
                        axisLabel: { color: '#6c757d' }
                    },
                    series: [{
                        name: '新增用户',
                        type: 'bar',
                        barWidth: '60%',
                        data: data,
                        // The barStyle will automatically use the color from the global palette.
                        itemStyle: {
                            // We can add a subtle gradient to the bars
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(139, 92, 246, 0.8)' },
                                { offset: 1, color: 'rgba(139, 92, 246, 0.5)' }
                            ]),
                            borderRadius: [4, 4, 0, 0]
                        }
                    }]
                };
                echartsA.setOption(option, true); // Use 'true' to clear previous styles
            }
        });
    }
}