<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\Alternative;
use app\api\service\TmplService;
use app\common\Playful;
use app\common\Remotely;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

class Career extends Base
{
    /*
     * 求职招聘岗位类型
     */
    public function employment_type()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $list = Db::name('employment_item_type')
            ->where('name', 'like', "%{$hazy_name}%")
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->order('sort', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 求职招聘岗位类型排序
     */
    public function employment_type_type_sort()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $sort = request()->post('dalue');
            Db::startTrans();
            try {
                Db::name('employment_item_type')->where('id', $syid)->where('much_id', $this->much_id)->update(['sort' => $sort]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 新增求职招聘岗位类型
     */
    public function new_employment_type()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data['name'] = trim(input('post.name'));
            $data['sort'] = intval(input('post.sort'));
            $data['status'] = intval(input('post.status'));
            $data['create_time'] = time();
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('employment_item_type')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            return $this->fetch();
        }
    }

    /*
     * 编辑求职招聘岗位类型
     */
    public function edit_employment_type()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $data['name'] = trim(input('post.name'));
            $data['sort'] = intval(input('post.sort'));
            $data['status'] = intval(input('post.status'));
            Db::startTrans();
            try {
                Db::name('employment_item_type')->where('id', $fid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $fid = intval(input('get.fid'));
            $litInfo = Db::name('employment_item_type')->where('id', $fid)->where('much_id', $this->much_id)->find();
            if ($litInfo) {
                $this->assign('list', $litInfo);
                return $this->fetch();
            } else {
                abort(404);
            }
        }
    }

    /*
     * 删除求职招聘
     */
    public function del_employment_type()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            Db::startTrans();
            try {
                Db::name('employment_item_type')->where('id', $fid)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     *  求职招聘列表
     */
    public function employment_found()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        $fid = intval(input('get.fid', 0));
        $when = [];
        //  如果fid不等于0 则查询
        $fid !== 0 && $when[] = function ($query) use ($fid) {
            $query->where('id', '=', $fid);
        };
        $hazy_name = trim(input('get.hazy_name', ''));
        $page = intval(input('get.page', 1));
        $list = Db::name('employment_item')
            ->where($when)
            ->where('job_name|job_description', 'like', "%{$hazy_name}%")
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->orderRaw('audit_status <> 0 asc,case when audit_status = 0 then id end asc,case when audit_status <> 0 then id end desc')
            ->field('job_description', true)
            ->paginate(10, false, ['query' => ['s' => $this->defaultQuery(), 'hazy_name' => $hazy_name]])
            ->each(function ($item) {
                $item['type_name'] = Db::name('employment_item_type')->where('id', $item['job_type'])->where('much_id', $this->much_id)->value('name');
                $item['user'] = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    /*
     * 查看求职招聘详情
     */
    public function employment_found_detail()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        $fid = intval(input('get.fid'));
        $liInfo = Db::name('employment_item')->where('id', $fid)->where('much_id', $this->much_id)->find();
        if ($liInfo) {
            $liInfo['type_name'] = Db::name('employment_item_type')->where('id', $liInfo['job_type'])->where('much_id', $this->much_id)->value('name');
            $liInfo['user'] = Db::name('user')->where('id', $liInfo['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
            $expressionHtml = function ($val) {
                return Alternative::ExpressionHtml($val);
            };
            $this->assign('expressionHtml', $expressionHtml);
            $this->assign('list', $liInfo);
            return $this->fetch();
        } else {
            abort(404);
        }
    }

    /*
     * 求职招聘审核
     */
    public function trial_employment_found()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            $liInfo = Db::name('employment_item')->where('id', $fid)->where('much_id', $this->much_id)->find();
            //  判断审核状态
            if (intval($liInfo['audit_status']) !== 0) {
                return json(['code' => 0, 'msg' => '系统繁忙，请刷新页面后重试！']);
            }
            $data['audit_status'] = intval(input('post.process', 0));
            //  审核拒绝
            if ($data['audit_status'] === 2) {
                $data['audit_reason'] = trim(input('post.inject', ''));
            }
            Db::startTrans();
            try {
                //  更改审核状态
                Db::name('employment_item')->where('id', $fid)->where('audit_status', 0)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($data['audit_status'] >= 1 && $data['audit_status'] <= 2) {
                $tmplService = new TmplService();
                $templateData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $liInfo['user_id'],
                    'page' => 'yl_welore/pages/user_smail/index',
                    'keyword4' => date('Y年m月d日 H:i:s', time()),
                ];
                $maringText = '';
                //  求职招聘审核通过
                switch ($data['audit_status']) {
                    case 1:
                        $templateData['keyword1'] = "您发布的求职招聘信息已通过审核";
                        $maringText = "{$templateData['keyword1']}！";
                        break;
                    case 2:
                        $templateData['keyword1'] = "您发布的求职招聘信息未通过审核";
                        $maringText = "很抱歉，您发布的求职招聘信息未通过审核，拒绝原因：{$data['audit_reason']}";
                        break;
                }
                //  模板消息
                $tmplService->add_template($templateData);
                //  站内信
                Db::name('user_smail')->insert(['user_id' => $liInfo['user_id'], 'maring' => $maringText, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     *  删除求职招聘信息
     */
    public function del_employment_found()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            Db::startTrans();
            try {
                Db::name('employment_item')->where('id', $fid)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 求职招聘置顶列表
     */
    public function employment_top()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        $page = request()->get('page', 1);
        $list = Db::name('employment_item_top')
            ->alias('eit')
            ->join('employment_item ei', 'eit.ei_id = ei.id')
            ->join('user u', 'eit.user_id = u.id')
            ->where('eit.is_pay', 1)
            ->whereRaw('eit.add_time + (eit.top_day * 86400) > ' . time())
            ->where('eit.much_id', $this->much_id)
            ->field('eit.ei_id, COUNT(eit.ei_id) as ei_count, SUM(eit.top_day) as total_top_day, eit.pay_type, ei.job_name as ei_name, ei.top_time as ei_top_time, u.user_nick_name, u.user_wechat_open_id, u.uvirtual')
            ->group('eit.ei_id')
            ->order('eit.id', 'desc')
            ->paginate(10, false, ['query' => ['s' => $this->defaultQuery()]])
            ->each(function ($item) {
                $item['user'] = [
                    'user_nick_name' => $item['user_nick_name'],
                    'user_wechat_open_id' => $item['user_wechat_open_id'],
                    'uvirtual' => $item['uvirtual'],
                ];
                $item['pay_info'] = '';
                $eitPrice = [];
                for ($i = 0; $i < 3; $i++) {
                    $eitPrice[] = Db::name('employment_item_top')->where('ei_id', $item['ei_id'])->where('pay_type', $i)->whereRaw('add_time + (top_day * 86400) > ' . time())->where('much_id', $this->much_id)->sum('pay_price');
                }
                $getDescription = function ($key) {
                    switch ($key) {
                        case 0:
                            return ' ( 贝壳 ) ';
                        case 1:
                            return ' ( 积分 ) ';
                        case 2:
                            return ' ( 微信支付 ) ';
                        default:
                            return '';
                    }
                };
                $str = '';
                foreach ($eitPrice as $key => $value) {
                    if ($value != 0) {
                        $str .= ' ' . $value . $getDescription($key) . '+';
                    }
                }
                $item['pay_info'] = rtrim($str, '+');
                $item['add_time'] = Db::name('employment_item_top')->where('ei_id', $item['ei_id'])->order(['id' => 'asc'])->where('much_id', $this->much_id)->value('add_time');
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 求职招聘取消置顶
     */
    public function employment_cancel_top()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $hid = intval(input('post.hid'));
            Db::startTrans();
            try {
                Db::name('employment_item')->where('id', $hid)->where('much_id', $this->much_id)->update(['top_time' => time()]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 求职招聘配置
     */
    public function employment_config()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data = input('post.item/a', []);
            Db::startTrans();
            try {
                Db::name('employment_item_config')->where('much_id', $this->much_id)->update(['custom_title' => trim($data['customTitle']), 'is_auto_audit' => intval($data['isAutoAudit']), 'reply_is_auto_audit' => intval($data['replyIsAutoAudit']), 'top_twig' => intval($data['topTwig']), 'price_type' => intval($data['priceType']), 'top_price' => floatval($data['topPrice']), 'help_document' => $this->safe_html($data['helpDocument'])]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $prcInfo = Db::name('employment_item_config')->where('much_id', $this->much_id)->find();
            if (!$prcInfo) {
                $prcInfo = ['custom_title' => '求职招聘', 'is_auto_audit' => 0, 'reply_is_auto_audit' => 0, 'top_twig' => 0, 'top_price' => 0.00, 'price_type' => '', 'help_document' => '', 'much_id' => $this->much_id];
                Db::startTrans();
                try {
                    $prcInfo['id'] = Db::name('employment_item_config')->insertGetId($prcInfo);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
            }
            $this->assign('list', $prcInfo);
            return $this->fetch();
        }
    }

    /*
     * 新增求职招聘信息
     */
    public function new_employment_found()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $data = [
                'user_id' => intval(input('post.userId')),
                'job_type' => intval(input('post.jobType')),
                'release_type' => intval(input('post.releaseType')),
                'job_name' => trim(input('post.jobName')),
                'job_salary' => trim(input('post.jobSalary')),
                'job_description' => trim(input('post.jobDescription')),
                'work_address' => trim(input('post.workAddress')),
                'contact_details' => trim(input('post.contactDetails')),
                'audit_status' => intval(input('post.auditStatus')),
                'create_time' => time(),
                'much_id' => $this->much_id
            ];
            
            Db::startTrans();
            try {
                Db::name('employment_item')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $typeList = json_encode(Db::name('employment_item_type')
                ->where('is_del', 0)
                ->where('much_id', $this->much_id)
                ->field('id,name')
                ->select());
            $this->assign('typeList', $typeList);
            return $this->fetch();
        }
    }

    /*
     * 编辑求职招聘信息
     */
    public function edit_employment_found()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5rGC6IGM5oub6IGY'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $data = [
                'user_id' => intval(input('post.userId')),
                'job_type' => intval(input('post.jobType')),
                'release_type' => intval(input('post.releaseType')),
                'job_name' => trim(input('post.jobName')),
                'job_salary' => trim(input('post.jobSalary')),
                'job_description' => trim(input('post.jobDescription')),
                'work_address' => trim(input('post.workAddress')),
                'contact_details' => trim(input('post.contactDetails')),
                'audit_status' => intval(input('post.auditStatus'))
            ];
            
            Db::startTrans();
            try {
                Db::name('employment_item')->where('id', $fid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $fid = intval(input('get.fid'));
            $EIinfo = Db::name('employment_item')->where('id', $fid)->where('much_id', $this->much_id)->find();
            if (!$EIinfo) {
                abort(404);
            }
            
            $typeList = json_encode(Db::name('employment_item_type')
                ->where('is_del', 0)
                ->where('much_id', $this->much_id)
                ->field('id,name')
                ->select());
                
            $this->assign('typeList', $typeList);
            $this->assign('list', base64_encode(rawurlencode(json_encode($EIinfo, true))));
            return $this->fetch();
        }
    }
}