<?php


namespace app\api\service;


use think\Db;

class RankingUtil
{
    public function calculation($wont_sort, $much_id, $type)
    {
        $arr_all = [];
        $time = '';
        if ($type == 1) {
            $time = 'week';
        } else if ($type == 2) {
            $time = 'month';
        } else {
            $time = 'last week';
        }
        foreach ($wont_sort as $k => $v) {
            switch ($v) {
                case 'chongzhi':
                    $arr_all[$k] = $this->chongzhi($much_id, $time);

                    break;
                case 'xiaofei':
                    $arr_all[$k] = $this->xiaofei($much_id, $time);
                    break;
                case 'fensi':
                    $arr_all[$k] = $this->fensi($much_id, $time);
                    break;
                case 'fatie':
                    $arr_all[$k] = $this->fatie($much_id, $time);
                    break;
                case 'huitie':
                    $arr_all[$k] = $this->huitie($much_id, $time);
                    break;
                case 'beihuitie':
                    $arr_all[$k] = $this->beihuitie($much_id, $time);
                    break;
                case 'dianzan':
                    $arr_all[$k] = $this->dianzan($much_id, $time);

                    break;
                case 'beidianzan':
                    $arr_all[$k] = $this->beidianzan($much_id, $time);
                    break;
                case 'fahongbao':
                    $arr_all[$k] = $this->fahongbao($much_id, $time);

                    break;
                case 'songli':
                    $arr_all[$k] = $this->songli($much_id, $time);

                    break;
                case 'shouli':
                    $arr_all[$k] = $this->shouli($much_id, $time);
                    break;
                case 'renwu':
                    $arr_all[$k] = $this->renwu($much_id, $time);

                    break;
                case 'jingyan':
                    $arr_all[$k] = $this->jingyan($much_id, $time);

                    break;
                case 'rongyu':
                    $arr_all[$k] = $this->rongyu($much_id, $time);

                    break;
                case 'yaoqing':
                    $arr_all[$k] = $this->yaoqing($much_id, $time);

                    break;
                case 'qiandao':
                    $arr_all[$k] = $this->qiandao($much_id, $time);
                    break;
                case 'jifen':
                    $arr_all[$k] = $this->jifen($much_id, $time);
                    break;
            }
        }
        $new_list = $this->array_sum_my($arr_all);
        arsort($new_list);
        //$new_list=array_chunk($new_list,1,true);
        $new_list = array_slice($new_list, 0, 10, true);
        $keys = [];
        foreach ($new_list as $k => $v) {
            $u = Db::name('user')->where('id', $k)->where('much_id', $much_id)->find();
            $keys[$k]['user_id'] = $k;
            $keys[$k]['finance'] = $v;
            $keys[$k]['user_head_sculpture'] = $u['user_head_sculpture'];
            $keys[$k]['user_nick_name'] = emoji_decode($u['user_nick_name']);
        }
        return array_values($keys);
    }

    /**
     * 计算数量
     */
    public function array_sum_my($arr)
    {
        $newArr = array();   //随便命名一个新的空数组
        foreach ($arr as $key => &$v) {             //这里的是有个“引用”
            if (!empty($arr[$key])) {   //只计算每天参加过摇一摇的用户
                foreach ($v as $k) {      //两次循环，取得最里面的数组
                    if (isset($newArr[$k['user_id']])) {
                        $newArr[$k['user_id']] += $k['finance'];
                    } else {
                        $newArr[$k['user_id']] = $k['finance'];
                    }
                }
            } else {
                unset($arr[$key]);
            }

        }
        return $newArr;
    }

    /**
     * 剩余积分排行
     */
    public function jifen($much_id, $time)
    {
        $info = Db::name('user')
            ->where('tourist', 0)
            ->where('much_id', $much_id)
            ->where('uvirtual', 0)
            ->where('status', 1)
            ->field('id as user_id,fraction as finance,user_head_sculpture,user_nick_name,status')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *充值金额
     */
    public function chongzhi($much_id, $time)
    {
        $info = Db::name('user_serial')->alias('a')
            ->join('user u', 'u.id=a.user_id')
            ->where('a.status', 1)
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->whereTime('a.add_time', $time)
            ->field('a.user_id,sum(ceiling(a.money)) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.add_time')
            ->group('a.user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *消费金额
     */
    public function xiaofei($much_id, $time)
    {
        $info = Db::name('user_amount')->alias('a')
            ->join('user u', 'u.id=a.user_id')
            ->where('a.category', 2)
            ->where('a.evaluate', 0)
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->whereTime('a.ruins_time', $time)
            ->field('a.user_id,sum(ceiling(abs(a.finance))) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.ruins_time')
            ->group('a.user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *粉丝
     */
    public function fensi($much_id, $time)
    {
        $info = Db::name('user_track')->alias('a')
            ->join('user u', 'u.id=a.qu_user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->whereTime('a.fo_time', $time)
            ->field('a.qu_user_id as user_id,count(*) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.fo_time')
            ->group('a.qu_user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *发帖
     */
    public function fatie($much_id, $time)
    {
        $info = Db::name('paper')->alias('a')
            ->join('user u', 'u.id=a.user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->where('a.whether_delete', 0)
            ->where('a.study_status', 1)
            ->whereTime('a.prove_time', $time)
            ->field('a.user_id,count(*) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.prove_time')
            ->group('a.user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *回帖
     */
    public function huitie($much_id, $time)
    {
        $info = Db::name('paper_reply')->alias('a')
            ->join('user u', 'u.id=a.user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->where('a.whether_delete', 0)
            ->where('a.reply_status', 1)
            ->whereTime('a.prove_time', $time)
            ->field('a.user_id,count(*) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.prove_time')
            ->group('a.user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *被回帖
     */
    public function beihuitie($much_id, $time)
    {
        $info = Db::name('paper_reply')->alias('a')
            ->join('paper p', 'p.id=a.paper_id')
            ->join('user u', 'u.id=p.user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->where('a.whether_delete', 0)
            ->where('a.reply_status', 1)
            ->whereTime('a.prove_time', $time)
            ->field('p.user_id,count(*) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.prove_time')
            ->group('p.user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *点赞
     */
    public function dianzan($much_id, $time)
    {
        $info = Db::name('user_applaud')->alias('a')
            ->join('user u', 'u.id=a.user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->whereTime('a.laud_time', $time)
            ->field('a.user_id,count(*) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.laud_time')
            ->group('a.user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *被点赞
     */
    public function beidianzan($much_id, $time)
    {
        $info = Db::name('user_applaud')->alias('a')
            ->join('paper p', 'p.id=a.paper_id')
            ->join('user u', 'u.id=p.user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->where('p.whether_delete', 0)
            ->whereTime('a.laud_time', $time)
            ->field('p.user_id,count(*) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.laud_time')
            ->group('p.user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *发红包
     */
    public function fahongbao($much_id, $time)
    {
        $info = Db::name('paper_red_packet')->alias('a')
            ->join('paper p', 'p.id=a.paper_id')
            ->join('user u', 'u.id=p.user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->whereTime('prove_time', $time)
            ->field('p.user_id,sum(ceiling(abs(a.initial_conch))) as finance,u.user_head_sculpture,u.user_nick_name,u.status,p.prove_time')
            ->group('p.user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *送礼
     */
    public function songli($much_id, $time)
    {
        $info = Db::name('user_subsidy')->alias('a')
            ->join('user u', 'u.id=a.con_user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->whereTime('a.bute_time', $time)
            ->field('a.con_user_id as user_id,sum(ceiling(abs(a.bute_price))) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.bute_time')
            ->group('a.con_user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *收礼
     */
    public function shouli($much_id, $time)
    {
        $info = Db::name('user_subsidy')->alias('a')
            ->join('user u', 'u.id=a.sel_user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->whereTime('a.bute_time', $time)
            ->field('a.sel_user_id as user_id,sum(ceiling(abs(a.bute_price))) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.bute_time')
            ->group('user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *任务完成次数
     */
    public function renwu($much_id, $time)
    {
        $info = Db::name('task_logger')->alias('a')
            ->join('user u', 'u.id=a.user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->whereTime('a.complete_time', $time)
            ->field('a.user_id as user_id,count(*) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.complete_time')
            ->group('a.user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *经验累积获取
     */
    public function jingyan($much_id, $time)
    {
        $info = Db::name('user_exp_glory_logger')->alias('a')
            ->join('user u', 'u.id=a.user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->where('a.type', 0)
            ->where('a.cypher', 0)
            ->whereTime('a.receive_time', $time)
            ->field('a.user_id as user_id,sum(ceiling(abs(a.points))) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.receive_time')
            ->group('a.user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *荣誉累积获取
     */
    public function rongyu($much_id, $time)
    {
        $info = Db::name('user_exp_glory_logger')->alias('a')
            ->join('user u', 'u.id=a.user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->where('a.type', 1)
            ->where('a.cypher', 0)
            ->whereTime('a.receive_time', $time)
            ->field('a.user_id as user_id,sum(ceiling(abs(a.points))) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.receive_time')
            ->group('a.user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *邀请数量
     */
    public function yaoqing($much_id, $time)
    {
        $info = Db::name('user_respond_invitation')->alias('a')
            ->join('user_invitation_code c', 'c.code=a.re_code')
            ->join('user u', 'u.id=c.user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->whereTime('a.re_time', $time)
            ->field('c.user_id as user_id,count(*) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.re_time')
            ->group('a.re_code')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     *签到次数
     */
    public function qiandao($much_id, $time)
    {
        $info = Db::name('user_punch')->alias('a')
            ->join('user u', 'u.id=a.user_id')
            ->where('a.much_id', $much_id)
            ->where('u.status', 1)
            ->whereTime('a.punch_time', $time)
            ->field('a.user_id as user_id,count(*) as finance,u.user_head_sculpture,u.user_nick_name,u.status,a.punch_time')
            ->group('a.user_id')
            ->order('finance desc')
            ->limit(10)
            ->select();
        return $info;
    }

    /**
     * w我的网盘总大小
     */
    public function get_my_netdisc_big($data)
    {

        $list = Db::name('netdisc_user_volume')
            ->where('much_id', $data['much_id'])
            ->where('user_id', $data['user_id'])
            ->select();
        $user_big = 0;
        foreach ($list as $k => $v) {
            if ($v['exp_time'] == 0) {
                //不过期加
                $user_big = bcadd($user_big, $v['quota_size']);
            } else {
                //临时时间，判断是否到期
                if (bccomp(time(), $v['exp_time']) != 1) {
                    $user_big = bcadd($user_big, $v['quota_size']);
                }
            }

        }
        return $user_big;
    }

    /**
     *  已使用大小
     */
    public function get_my_netdisc_use($data)
    {
        //查询已使用的容量
        $user_use = 0;
        $netdisc_belong = Db::name('netdisc_belong')
            ->where('user_id', $data['user_id'])
            ->where('is_dir', 0)
            ->where('much_id', $data['much_id'])
            ->where('is_del', 0)
            ->select();
        foreach ($netdisc_belong as $k => $v) {
            $netdisc = Db::name('netdisc')->where('id', $v['nc_id'])->where('much_id', $data['much_id'])->find();
            $user_use = bcadd($user_use, $netdisc['file_size']);
        }
        return $user_use;
    }

    /**
     * 获取后缀文件图标
     */
    public function CheckfileTypeIcon($type)
    {
        switch ($type) {
            case 'jpg':
            case 'jpeg':
            case 'bmp':
            case 'png':
            case 'gif':
            case 'mp4':
            case 'mkv':
            case 'avi':
            case 'mpeg':
            case 'wav':
            case 'ogg':
            case 'doc':
            case 'docx':
            case 'xls':
            case 'xlsx':
            case 'ppt':
            case 'pptx':
            case 'pdf':
            case 'm4a':
            case 'aac':
            case 'mp3':
                $icon = '1';
                break;
            case 'rar':
            case 'zip':
            case '7z':
            case 'txt':
                $icon = '0';
                break;
            default:
                $icon = '0';
        }
        return $icon;
    }

    /**
     * 获取后缀文件图标
     */
    public function fileTypeIcon($type)
    {
        switch ($type) {
            case 'jpg':
            case 'jpeg':
            case 'bmp':
            case 'png':
            case 'gif':
                $icon = 'tupian.png';
                break;
            case 'mp4':
            case 'avi':
            case 'mpeg':
            case 'wav':
            case 'ogg':
                $icon = 'shipin.png';
                break;
            case 'mp3':
                $icon = 'music.png';
                break;
            case 'txt':
                $icon = 'txt.png';
                break;
            case 'doc':
            case 'docx':
                $icon = 'word.png';
                break;
            case 'xls':
            case 'xlsx':
                $icon = 'excel.png';
                break;
            case 'ppt':
            case 'pptx':
                $icon = 'ppt.png';
                break;
            case 'pdf':
                $icon = 'pdf.png';
                break;
            case 'rar':
            case 'zip':
            case '7z':
                $icon = 'yasuobao.png';
                break;
            case 'dir':
                $icon = 'dir.png';
                break;
            default:
                $icon = 'weizhiwenjian.png';
        }
        return $icon;
    }
}