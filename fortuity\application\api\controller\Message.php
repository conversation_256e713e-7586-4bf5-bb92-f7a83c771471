<?php

namespace app\api\controller;


use app\api\service\Util;
use app\common\Remotely;
use think\Db;
use think\Request;

class Message extends Base
{

    /**
     * 获取疑难解答列表
     */
    public function get_help_info()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('help')
            ->where('much_id', $data['much_id'])
            ->order('scores')
            ->select();
        $rs['info'] = $list;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取疑难解答详情
     */
    public function get_help_info_desc()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('help')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->find();
        $rs['info'] = $list;
        return $this->json_rewrite($rs);
    }


    /**
     * 获取站点信息
     */
    public function get_authority()
    {
        $data = input('param.');
        $info = Db::name('authority')->where('much_id', $data['much_id'])->find();
        $net = Db::name('netdisc_config')->where('much_id', $data['much_id'])->value('rel_paper_icon_hide');
        $info['copyright'] = str_replace('&copy;', '©', $info['copyright']);
        $util = new Util();
        $vip = $util->get_user_vip($this->user_info['id']);
        $reissue = Db::name('reissue')->where('much_id', $data['much_id'])->find();
        $info['vip'] = $vip;
        $info['rel_paper_icon_hide'] = $net;
        $info['forward'] = $reissue;
        $info['version'] = $this->version;
        $incentive_id = Db::name('advertise')->where('much_id', $data['much_id'])->field('incentive_id')->find();
        $info['jili'] = $incentive_id['incentive_id'];
        $info['tory_sort_arbor'] = 0;
        //查询
        $stipulate = Db::name('feeling_stipulate')->where('much_id', $data['much_id'])->find();
        $info['feeling_stipulate'] = empty($stipulate) ? 0 : $stipulate['bare_location'];
        //首页 展示位置
        $info['bare_direction'] = empty($stipulate) ? 1 : $stipulate['bare_direction'];
        //距离底部距离
        $info['direction_bottom'] = empty($stipulate) ? 20 : $stipulate['direction_bottom'];
        //首页图片
        $delimiter = DS;
        $request = Request::instance();
        $domain = $request->domain();
        $keySymbol = "{$domain}{$delimiter}addons{$delimiter}yl_welore{$delimiter}web{$delimiter}static{$delimiter}mineIcon{$delimiter}material{$delimiter}tape_img.png";
        $info['bare_img_url'] = empty($stipulate) || empty($stipulate['bare_img_url']) ? $keySymbol : $stipulate['bare_img_url'];
        $info['conceal'] = $this->conceal;
        //获取插件视频解析备注
        $vacInfo = Db::name('video_analysis_config')->where('is_default', 1)->where('much_id', $data['much_id'])->value('app_remark');
        $info['app_remark']=$vacInfo;
        return $this->json_rewrite($info);
    }

    /**
     * 获取转发详情
     */
    public function get_forward()
    {
        $data = input('param.');
        $info = Db::name('reissue')->where('much_id', $data['much_id'])->find();
        return $this->json_rewrite($info);
    }

    /**
     * 获取禁言列表
     */
    public function get_user_banned()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('user_banned')->alias('b')
            ->join('territory t', 't.id=b.tory_id')
            ->where('b.user_id', $this->user_info['id'])
            ->where('b.much_id', $data['much_id'])
            ->field('b.*,t.realm_name')
            ->select();
        foreach ($list as $a => $b) {
            $user_is_mutter = Db::name('user_mutter')
                ->where('much_id', $data['much_id'])
                ->where('tory_id', $b['tory_id'])
                ->where('user_id', $this->user_info['id'])
                ->where('ban_id', $b['id'])
                ->where('status', 0)
                ->count();
            $user_mutter_list = Db::name('user_mutter')
                ->where('much_id', $data['much_id'])
                ->where('tory_id', $b['tory_id'])
                ->where('user_id', $this->user_info['id'])
                ->where('ban_id', $b['id'])
                ->select();
            $list[$a]['user_is_mutter'] = $user_is_mutter;
            $list[$a]['user_mutter_list'] = $user_mutter_list;
            $list[$a]['beget'] = emoji_decode($b['beget']);
            $list[$a]['reason_refusal'] = emoji_decode($b['reason_refusal']);
            $list[$a]['realm_name'] = emoji_decode($b['realm_name']);
            if ($b['refer_time'] != 0) {
                $list[$a]['refer_time'] = date('Y年m月d日 H:i:s', $b['refer_time']);
            }
        }
        $rs['info'] = $list;
        return $this->json_rewrite($rs);

    }

    /**
     * 申诉禁言
     */
    public function do_user_mutter()
    {
        $data = input('param.');
        $user_is_mutter = Db::name('user_mutter')
            ->where('much_id', $data['much_id'])
            ->where('tory_id', $data['tory_id'])
            ->where('user_id', $this->user_info['id'])
            ->where('ban_id', $data['id'])
            ->where('status', 0)
            ->count();
        if ($user_is_mutter > 0) {
            $rs = ['status' => 'error', 'msg' => '您已经申诉，请耐心等待！'];
            return $this->json_rewrite($rs);
        }
        $dd['user_id'] = $this->user_info['id'];
        $dd['tory_id'] = $data['tory_id'];
        $dd['ban_id'] = $data['id'];
        $dd['beget'] = emoji_encode($data['beget']);
        $dd['mute_time'] = time();
        $dd['status'] = 0;
        $dd['much_id'] = $data['much_id'];
        $dd['mute_type'] = 0;
        $dd['reason_refusal'] = '';
        $ins = Db::name('user_mutter')->insert($dd);
        if ($ins) {
            $rs = ['status' => 'success', 'msg' => '申诉成功！'];
            return $this->json_rewrite($rs);
        } else {
            $rs = ['status' => 'error', 'msg' => '申诉失败，请稍候重试！'];
            return $this->json_rewrite($rs);
        }
    }

    /**
     * 获取发帖须知
     */
    public function get_post_notice()
    {
        $data = input('param.');
        //获取发帖须知
        $setting = Db::name('paper_smingle')->where('much_id', $data['much_id'])->find();

        return $this->json_rewrite($setting);
    }


    /**
     * 获取站内信消息
     */
    public function get_user_smail()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('user_smail')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->order('clue_time desc')
            ->page($data['page'], '15')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                $list[$k]['is'] = 0;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['maring'] = emoji_decode($v['maring']);
                $list[$k]['clue_time'] = date('Y-m-d H:i:s', $v['clue_time']);
                if (empty($v['paper_id'])) {
                    $list[$k]['paper_id'] = 0;
                }
            }
        }
        //查询未读消息
        $user_male = Db::name('user_smail')->where('user_id', $data['uid'])->where('status', 0)->count();
        $msg_count = Db::name('user_leave_word')->where('re_user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->where('le_read_status', 0)->count();
        $rs['info'] = $list;
        $rs['user_male'] = $user_male;
        $rs['user_to_count'] = $msg_count;
        return $this->json_rewrite($rs);

    }

    /**
     * 更新为已读
     */
    public function up_user_smail()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $rr = Db::name('user_smail')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['status' => 1]);
        if (!$rr) {
            $rs = ['status' => 'error', 'msg' => '失败，请稍候重试！'];
            return $this->json_rewrite($rs);
        }
        return $this->json_rewrite($rs);
    }

    /**
     * 删除站内信
     */
    public function del_user_smail()
    {
        $data = input('param.');
        $rs = ['status' => 'success', 'msg' => '删除成功'];
        $rr = Db::name('user_smail')->where('id','in', $data['id'])->where('much_id', $data['much_id'])->delete();
        if (!$rr) {
            $rs = ['status' => 'error', 'msg' => '删除失败，请稍候重试！'];
            return $this->json_rewrite($rs);
        }
        return $this->json_rewrite($rs);
    }

    /**
     * 站内信全部标为已读
     */
    public function up_user_smail_all()
    {
        $rs = ['status' => 'success', 'msg' => '标记成功！'];
        $data = input('param.');
        $rr = Db::name('user_smail')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['status' => 1]);
        if ($rr !== false) {
            return $this->json_rewrite($rs);
        } else {
            $rs = ['status' => 'error', 'msg' => '标记失败，请稍候重试！'];
            return $this->json_rewrite($rs);
        }

    }

    /**
     * 获取流量主
     */
    public function get_ad()
    {
        $rs = ['status' => 'success', 'msg' => '成功！', 'data' => array()];
        $data = input('param.');
        //获取首页帖子浏览数量隐藏
        $paper_browse_num_hide=Db::name('authority')->where('much_id', $data['much_id'])->value('paper_browse_num_hide');
        $info = Db::name('advertise')->where('much_id', $data['much_id'])->find();
        $info_sw = Db::name('polling')->where('ad_type', '0')->where('status', 1)->where('much_id', $data['much_id'])->order('scores asc')->select();
        $info_zf = Db::name('reissue')->where('much_id', $data['much_id'])->find();
        $info_top = Db::name('polling')->where('ad_type', '1')->where('status', 1)->where('much_id', $data['much_id'])->order('scores asc')->select();
        $info_home = Db::name('polling')->where('ad_type', '2')->where('status', 1)->where('much_id', $data['much_id'])->order('scores asc')->select();
        if ($info['pre_post_twig'] == 0) {
            $info['pre_post_id'] = '';
        }
        foreach ($info_sw as $k => $v) {
            if ($v['practice_type'] == 1) {
                $info_sw[$k]['url'] = urlencode($v['url']);
            }
        }
        foreach ($info_top as $k => $v) {
            if ($v['practice_type'] == 1) {
                $info_top[$k]['url'] = urlencode($v['url']);
            }
        }
        foreach ($info_home as $k => $v) {
            if ($v['practice_type'] == 1) {
                $info_home[$k]['url'] = urlencode($v['url']);
            }
        }
        $chunk_result = array_chunk($info_home, 8);
        $info['paper_browse_num_hide']=$paper_browse_num_hide;
        $rs['data']['info'] = $info;
        $rs['data']['info_sw'] = $info_sw;
        $rs['data']['info_zf'] = $info_zf;
        $rs['data']['info_top'] = empty($info_top) ? '' : $info_top;
        $rs['data']['info_home'] = $info_home;
        $rs['data']['info_home_new'] = $chunk_result;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取diy数据
     */
    public function get_diy()
    {
        $json = ([
            'style' => [
                'backcolor' => '#ffffff',
                'font_color' => '#A7A8A5',
                'font_color_active' => '#2FA0DF'
            ], 'home' => [
                'title' => '首页',
                'images' => [
                    'img' => "../../style/icon/1.png",
                    'img_active' => "../../style/icon/1.png",
                ]
            ], 'plaza' => [
                'title' => '广场',
                'images' => [
                    'img' => "../../style/icon/2.png",
                    'img_active' => "../../style/icon/2.png",
                ]
            ],
            'release' => [
                'title' => '发布',
                'images' => [
                    'img' => "../../style/icon/home_add.png",
                    'img_active' => "../../style/icon/home_add.png",
                ],
                "list" => [
                    "writing" => [
                        "title" => "发图文",
                        "images" => "../../style/icon/a.png"
                    ],
                    "audio" => [
                        "title" => "发语音",
                        "images" => "../../style/icon/b.png"
                    ],
                    "graffito" => [
                        "title" => "发涂鸦",
                        "images" => "../../style/icon/c98.png"
                    ],
                    "video" => [
                        "title" => "发视频",
                        "images" => "../../style/icon/bpn.png"
                    ],
                    "brisk" => [
                        "title" => "发活动",
                        "images" => "../../style/icon/brisk.png"
                    ]
                ]
            ], 'goods' => [
                'title' => '站内信',
                'images' => [
                    'img' => "../../style/icon/3.png",
                    'img_active' => "../../style/icon/3.png",
                ]
            ], 'user' => [
                'title' => '我的',
                'images' => [
                    'img' => "../../style/icon/4.png",
                    'img_active' => "../../style/icon/4.png",
                ]
            ]
        ]);
        $data = input('param.');
        $diy = Db::name('design')->where('much_id', $data['much_id'])->find();
        if (empty($diy)) {
            $diy['confer'] = '积分';
            $diy['currency'] = '贝壳';
            $diy['landgrave'] = '圈子';
            $diy['home_title'] = '首页';
            $frist = mb_substr($diy['landgrave'], 0, 1, 'utf-8');
            $diy['qq_name'] = $frist;
        } else {
            $frist = mb_substr($diy['landgrave'], 0, 1, 'utf-8');
            $diy['qq_name'] = $frist;
            if (!empty($diy['pattern_data'])) {
                $json = json_decode($diy['pattern_data'], true);
                foreach ($json as $k => $v) {
                    if ($k[$v] == 'home') {
                        $json[$k]['home']['title'] = emoji_decode($v['home']['title']);
                    }
                    if ($k[$v] == 'plaza') {
                        $json[$k]['plaza']['title'] = emoji_decode($v['plaza']['title']);
                    }
                    if ($k[$v] == 'release') {
                        $json[$k]['release']['title'] = emoji_decode($v['release']['title']);
                    }
                    if ($k[$v] == 'goods') {
                        $json[$k]['goods']['title'] = emoji_decode($v['goods']['title']);
                    }
                    if ($k[$v] == 'user') {
                        $json[$k]['user']['title'] = emoji_decode($v['user']['title']);
                    }
                }
                if (!strpos($diy['pattern_data'], "release")) {
                    $json['release'] = [
                        'title' => '发布',
                        'images' => [
                            'img' => "../../style/icon/home_add.png",
                            'img_active' => "../../style/icon/home_add.png",
                        ]
                    ];
                }
                if (!isset($json['release']['list'])) {
                    $json['release']['list'] = [
                        "writing" => [
                            "title" => "发图文",
                            "images" => "../../style/icon/a.png"
                        ],
                        "audio" => [
                            "title" => "发语音",
                            "images" => "../../style/icon/b.png"
                        ],
                        "graffito" => [
                            "title" => "发涂鸦",
                            "images" => "../../style/icon/c98.png"
                        ],
                        "video" => [
                            "title" => "发视频",
                            "images" => "../../style/icon/bpn.png"
                        ],
                        "brisk" => [
                            "title" => "发活动",
                            "images" => "../../style/icon/brisk.png"
                        ]
                    ];
                }
            }
        }
        //获取是否开启会员发帖类型
        $user_vip = Db::name('authority')->where('much_id', $data['much_id'])->field('rel_paper_img_style,vote_member,video_member,voice_member,graffiti_member,brisk_member')->find();
        //检查当前用户是否是VIP
        $util = new Util();
        $vip = $util->get_user_vip($this->user_info['id']);
        //$vip = get_user_vip($this->user_info['id']);
        //获取当前开启的附件
        $open_file = Db::name('outlying')->where('much_id', $data['much_id'])->find();
        $diy['open_file'] = $open_file['quicken_type'];
        $diy['pattern_data'] = $json;
        $diy['vip'] = $vip;
        $diy['user_vip'] = $user_vip;
        $diy['version'] = $this->version;
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        $diy['admin'] = $cg;


        if (cache('market_' . $data['much_id'])) {
            $mod = cache('market_' . $data['much_id']);
        } else {
            //获取模版
            $mod = Db::name('combination')->where('much_id', $data['much_id'])->find();
            if (empty($mod)) {
                $mod['home'] = Remotely::templetEncode('0');
                $mod['plaza'] = Remotely::templetEncode('0');
                $mod['goods'] = Remotely::templetEncode('0');
                $mod['user'] = Remotely::templetEncode('0');
            }
            //cache('market_' . $data['much_id'], $mod);
        }
        $diy['mod'] = Remotely::templetDecodeAll($mod);
        //获取是否开启小商品
        $shop_arbor = Db::name('authority')->where('much_id', $data['much_id'])->find();
        $diy['shop_arbor'] = $shop_arbor['shop_arbor'];
        //商城名称
        $diy['mall'] = empty($diy['mall']) ? $diy['currency'] . '商城' : $diy['mall'];
        //金币图片
        $j = Db::name('user_punch_range')->where('much_id', $data['much_id'])->find();

        $domain = explode(':', $_SERVER['HTTP_HOST']);
        $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
        $absRess = "https://{$domain[0]}{$absAddress[0]}static/wechat";
        $diy['currency_icon'] = empty($j['currency_icon']) ? "{$absRess}/jinbi.png" : $j['currency_icon'];
        //查询当前用户是否授权过
        $wx_popular = Db::name('wx_popular_bind_user')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        $diy['popular'] = 0;
        if (!empty($wx_popular)) {
            $diy['popular'] = 1;
        }
        $diy['phone_config'] = 0;
        $plug = new Plugunit();
        //查询是否强制开启手机号
        if ($plug->check_plug('c2b0e5ea-90e6-16af-7086-5e095954cf05', $data['much_id'])) {
            $call_config = Db::name('call_phone_config')->where('much_id', $data['much_id'])->find();
            $diy['phone_config'] = empty($call_config) ? 0 : $call_config['force_input_phone'];
        }
        //查询有几个表情
        $absLocalRes = ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'expression';
        $chunk_result = [];
        if (is_dir($absLocalRes)) {
            // array_merge 重建数组索引 array_diff 过滤.和.. scandir 列出文件和目录
            $file = scandir($absLocalRes);
            array_multisort($file, SORT_ASC, SORT_NUMERIC);
            $arr = array_merge(array_diff($file, ['.', '..']));
            $chunk_result = array_chunk($arr, 36);
        }
        $diy['expression'] = $chunk_result;
        //查询小秘密别称
        //获取发帖须知
        $setting = Db::name('paper_smingle')->where('much_id', $data['much_id'])->find();
        $diy['custom_hiss_title'] = empty($setting['custom_hiss_title']) ? '树洞' : $setting['custom_hiss_title'];
        //查询二手交易的自定义名称
        $used_goods_item_config= Db::name('used_goods_item_config')->where('much_id', $data['much_id'])->find();
        $diy['custom_title']=empty($used_goods_item_config['custom_title'])?'二手交易':$used_goods_item_config['custom_title'];
        //查询求职的自定义名称
        $employment_item_config= Db::name('employment_item_config')->where('much_id', $data['much_id'])->find();
        $diy['custom_title_em']=empty($employment_item_config['custom_title'])?'求职招聘':$employment_item_config['custom_title'];
        //查询同城信息
        $easy_item_config= Db::name('easy_info_config')->where('much_id', $data['much_id'])->find();
        $diy['easy_title_em']=empty($easy_item_config['custom_title'])?'便民信息':$easy_item_config['custom_title'];
        //查询同城信息
        $feel_item_config= Db::name('feeling_stipulate')->where('much_id', $data['much_id'])->find();
        $diy['feel_title_em']=empty($feel_item_config['custom_title'])?'小纸条':$feel_item_config['custom_title'];
        //查询幸运抽奖
        $sweepstake_config= Db::name('sweepstake_config')->where('much_id', $data['much_id'])->find();
        $diy['sweepstake_title']=empty($sweepstake_config['custom_title'])?'幸运抽奖':$sweepstake_config['custom_title'];
        //查询短剧
        $micro_config= Db::name('micro_series_config')->where('much_id', $data['much_id'])->find();
        $diy['micro_title']=empty($micro_config['custom_title'])?'短剧视频':$micro_config['custom_title'];
        return $this->json_rewrite($diy);
    }

    /**
     * 关注列表
     */
    public function get_q_gz()
    {
        $data = input('param.');
        $util = new Util();
        $check = $util->check_qq($this->user_info['user_wechat_open_id'], $data['id']);
        if ($check == 'no') {
            return $this->json_rewrite(['status' => 'error', 'msg' => '没有权限！']);
        }
        $search_name_text = emoji_encode($data['search_name_text']);
        $where['u.user_nick_name'] = ['like', "%$search_name_text%"];
        $list = Db::name('user_trailing')->alias('t')
            ->join('user u', 'u.id=t.user_id')
            ->where('t.tory_id', $data['id'])
            ->where('t.much_id', $data['much_id'])
            ->where($where)
            ->page($data['page'], 30)
            ->field('t.id,t.ling_time,t.user_id,u.user_nick_name,u.user_head_sculpture')
            ->select();
        foreach ($list as $k => $v) {
            $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            $list[$k]['ling_time'] = date('Y-m-d H:i', $v['ling_time']);
        }
        return $this->json_rewrite($list);
    }

    /**
     * 移除关注
     */
    public function del_gz_do()
    {
        $data = input('param.');
        $util = new Util();
        $check = $util->check_qq($this->user_info['user_wechat_open_id'], $data['tory_id']);
        if ($check == 'no') {
            return $this->json_rewrite(['status' => 'error', 'msg' => '没有权限！']);
        }
        //判断权限
        $rs = Db::name('user_trailing')->where('much_id', $data['much_id'])->where('id', $data['id'])->delete();
        if ($rs === false) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '移除失败！']);
        } else {
            return $this->json_rewrite(['status' => 'success', 'msg' => '移除成功！']);
        }
    }
}