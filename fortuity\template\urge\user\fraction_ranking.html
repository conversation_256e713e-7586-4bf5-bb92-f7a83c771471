{extend name="/base"/}
{block name="main"}
<style>.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.bunch{border:1px solid;padding:3px 6px;border-radius:5px;font-size:12px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-bar-chart"></span> {$defaultNavigate.confer}排行榜
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}"
                           placeholder="搜索用户名...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-btn-toolbar" style="margin: 0px 0px 15px 20px;">
                    <div class="am-btn-group am-btn-group-xs resth">
                        <a href="{:url('user/fraction_ranking')}&egon=0" class="cust-btn cust-btn-one {if $egon==0}cust-btn-activate{/if}">全部时间</a>
                        <a href="{:url('user/fraction_ranking')}&egon=1" class="cust-btn {if $egon==1}cust-btn-activate{/if}">今日排行</a>
                        <a href="{:url('user/fraction_ranking')}&egon=2" class="cust-btn {if $egon==2}cust-btn-activate{/if}">本周排行</a>
                        <a href="{:url('user/fraction_ranking')}&egon=3" class="cust-btn {if $egon==3}cust-btn-activate{/if}">本月排行</a>
                        <a href="{:url('user/fraction_ranking')}&egon=4" class="cust-btn {if $egon==4}cust-btn-activate{/if}">今年排行</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="20%">用户信息</th>
                            <th width="25%">openid</th>
                            <th width="15%">最后一次访问时间</th>
                            <th width="20%" style="text-align:right;">
                                {switch name="egon"}{case value="0"}全部时间{/case}{case value="1"}今日{/case}{case value="2"}本周{/case}{case value="3"}本月{/case}{case value="4"}今年{/case}{/switch}获得{$defaultNavigate.confer}数量 ( 含支出 )
                            </th>
                            <th width="20%" style="text-align:right;">当前剩余{$defaultNavigate.confer}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                {if $vo.user_head_sculpture}
                                <img src="{$vo.user_head_sculpture}" style="width:70px;height:70px;border-radius:50%;">
                                {else}
                                <img style="width:70px;height:70px;border-radius:50%;">
                                {/if}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1"
                                   target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                            </td>
                            <td class="am-text-middle">
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1"
                                   target="_blank" title="{$vo.user_wechat_open_id}">
                                    {$vo.user_wechat_open_id}
                                </a>
                            </td>
                            <td class="am-text-middle">
                                {if $vo.user_last_time}{:date('Y-m-d H:i:s',$vo.user_last_time)}{else}已经很长时间没有来过了{/if}
                            </td>
                            <td class="am-text-middle" style="text-align:right;">
                                <a href="{:url('user/wallet')}&usid={$vo.id}" target="_blank" title="点击跳转到用户钱包查看明细">
                                    {if $vo.sance >= 0}
                                    <span style="color:green;">+{$vo.sance} ( {$defaultNavigate.confer} )</span>
                                    {else}
                                    <span style="color:red;">{$vo.sance} ( {$defaultNavigate.confer} )</span>
                                    {/if}
                                </a>
                            </td>
                            <td class="am-text-middle" style="text-align:right;">
                                <span style="color:green;">
                                {$vo.fraction} ( {$defaultNavigate.confer} )
                                </span>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>
    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('user/fraction_ranking')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('user/fraction_ranking')}&egon={$egon}&page={$page}";
        }
    }
</script>
{/block}