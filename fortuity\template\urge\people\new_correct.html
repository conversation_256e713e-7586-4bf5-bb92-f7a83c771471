<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>新增点评</title>
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <script src="./assets/js/jquery.min.js"></script>
</head>
<body>
<div class="tpl-content-wrapper">
    <div class="am-g tpl-amazeui-form" style="background: #fcfcfc;width: 100%;height: 100%;">
        <div class="am-form am-form-horizontal" style="margin-left: -40px;">
           <div style="display: flex;flex-direction: column;align-items: center;">
               <div class="am-form-group" style="margin-top: 35px;">
                   <label class="am-u-sm-4 am-form-label" style="text-align: right;margin-right: -30px;">
                       ㅤㅤ点评用户UID
                   </label>
                   <div class="am-u-sm-8" style="margin-top: 3px;">
                       <input id="uid" type="text" style="width: 300px;">
                       <small style="color: #1E9FFF;font-weight: bold;">ㅤ</small>
                   </div>
               </div>
               <div class="am-form-group">
                   <label class="am-u-sm-4 am-form-label" style="text-align: right;">
                       ㅤㅤ点评分数
                   </label>
                   <div class="am-u-sm-8 am-u-end" style="margin-top: 3px;">
                       <select id="assessScore" style="width: 300px;">
                           <option value="5">5分</option>
                           <option value="4">4分</option>
                           <option value="3">3分</option>
                           <option value="2">2分</option>
                           <option value="1">1分</option>
                       </select>
                   </div>
               </div>
               <div class="am-form-group">
                   <label class="am-u-sm-4 am-form-label" style="text-align: right;">
                       ㅤㅤ点评内容
                   </label>
                   <div class="am-u-sm-8" style="margin-top: 3px;">
                       <textarea id="assessContent" style="resize: none;width: 300px;height: 180px;"></textarea>
                   </div>
               </div>
               <div class="am-form-group" style="margin-top: 20px;">
                   <label class="am-u-sm-4 am-form-label" style="text-align: right;">
                       是否公开显示
                   </label>
                   <div class="am-u-sm-8" style="margin-top: 3px;">
                       <select id="isShow" style="width: 300px;">
                           <option value="-1">请选择</option>
                           <option value="0">否</option>
                           <option value="1">是</option>
                       </select>
                   </div>
               </div>
               <div class="am-form-group" style="margin-top: 20px;display: flex;justify-content: center;">
                   <div style="cursor:pointer;color:#ffffff;background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;">
                       <div style="width: 100%;height: 100%;" onclick="holdSave()">
                           保存数据
                       </div>
                   </div>
               </div>
           </div>
        </div>
    </div>
</div>
</body>
<script>
    var isLock = false;
    var holdSave = function () {
        var setData = {};
        setData['pid'] = '{$fid}';
        setData['uid'] = Number($('#uid').val());
        console.log(setData['uid']);
        if (setData['uid'] === -1) {
            parent.layer.msg('请选择点评用户！');
            return;
        }
        setData['assessScore'] = Number($('#assessScore').val());
        setData['assessContent'] = $.trim($('#assessContent').val());
        if (setData['assessContent'] === '') {
            parent.layer.msg('请输入点评内容！');
            return;
        }
        setData['isShow'] = Number($('#isShow').val());
        if (setData['isShow'] === -1) {
            parent.layer.msg('请选择是否公开显示！');
            return;
        }
        if (!isLock) {
            isLock = true;
            $.post("{:url('people/newCorrect')}", setData, function (data) {
                if (data.code > 0) {
                    parent.layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        parent.location.reload();
                    });
                } else {
                    parent.layer.msg(data.msg, {icon: 5, time: 2200},function () {
                        isLock = false;
                    });
                }
            }, 'json');
        }
    }
</script>
</html>