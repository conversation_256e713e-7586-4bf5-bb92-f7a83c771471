{extend name="/base"/}
{block name="main"}
<style>.span-ranking{background:#477a98;border-radius:3px;padding:6px 10px;font-size:12px;cursor:pointer;color:#f1f4f5;margin-left:5px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 用户网盘
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="hazy_name" value="{$hazy_name}" placeholder="搜索用户...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr>
                            <th class="text-center" width="14.28%">UID</th>
                            <th class="text-center" width="14.28%">用户名称</th>
                            <th class="text-center" width="14.28%">openid</th>
                            <th class="text-center" width="14.28%">已用空间</th>
                            <th class="text-center" width="14.28%">空间大小</th>
                            <th class="text-center" width="14.28%">使用状态</th>
                            <th class="text-center" width="14.28%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle text-center">
                                {$vo.uid}
                            </td>
                            <td class="am-text-middle text-center">
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="am-text-middle text-center">
                                {$vo.user_wechat_open_id}
                            </td>
                            <td class="am-text-middle text-center">
                                {:$setupSize($vo.use_file_size)}
                            </td>
                            <td class="am-text-middle text-center">
                                {:$setupSize($vo.total_volume)}
                            </td>
                            <td class="am-text-middle text-center">
                                {if !$vo.user_id}
                                    <span style="color:dodgerblue;">未开通</span>
                                {else}
                                    {if $vo.use_status==1}
                                    <span style="color:green;">正常</span>
                                    {else}
                                    <span style="color:red;">封禁中</span>
                                    {/if}
                                {/if}
                            </td>
                            <td class="am-text-middle text-center">
                                <div class="am-btn-group am-btn-group-sm" style="display: flex;justify-content: center;">
                                    <button class="am-btn" style="padding: 0.5em 0.5em; color:#000;height: 30px;border-left:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                                        功能列表
                                    </button>
                                    <div class="am-dropdown" data-am-dropdown style="height: 30px;line-height: 0;border-right:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                                        <button class="am-btn am-dropdown-toggle" data-am-dropdown-toggle style="padding: 0.2em 0.5em;">
                                            <span class="am-icon-caret-down"></span>
                                        </button>
                                        <ul class="am-dropdown-content" style="width: 120px;min-width: initial;">
                                            {if !$vo.user_id}
                                                <li style="display: flex;justify-content: center;">
                                                    <a href="javascript:void(0);" onclick="openNetDisc('{$vo.uid}')" style="padding: 5px;color: #000;">
                                                        开通网盘功能
                                                    </a>
                                                </li>
                                            {else}
                                                <li style="display: flex;justify-content: center;">
                                                    <a href="javascript:void(0);" onclick="userFileSaves('{$vo.uid}')" style="padding: 5px;color: #000;">
                                                        网盘数据
                                                    </a>
                                                </li>
                                                <li style="display: flex;justify-content: center;">
                                                    <a href="javascript:void(0);" onclick="adjustCapacity('{$vo.uid}','{$vo.quota_size}')" style="padding: 5px;color: #000;">
                                                        调整容量
                                                    </a>
                                                </li>
                                                {if $vo.use_status == 0}
                                                <li style="display: flex;justify-content: center;">
                                                    <a href="javascript:void(0);" onclick="cloudForUpdate('{$vo.uid}',1)" style="padding: 5px; color: #000;">
                                                        取消封禁
                                                    </a>
                                                </li>
                                                {else}
                                                <li style="display: flex;justify-content: center;">
                                                    <a href="javascript:void(0);" onclick="cloudForUpdate('{$vo.uid}',0)" style="padding: 5px;color: #000;">
                                                        封禁用户
                                                    </a>
                                                </li>
                                                {/if}
                                            {/if}
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
<div id="mutual" class="hide">
    <div class="am-g tpl-amazeui-form" style="background: #fcfcfc;width: 100%;height: 100%;">
        <div class="am-form am-form-horizontal">
            <div class="am-form-group" style="margin-top: 35px;">
                <label class="am-u-sm-4 am-form-label">调整用户空间容量</label>
                <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                    <input class="uid" type="hidden">
                    <input class="quotaSize" type="text" style="width: 240px;" oninput="grender(this)">
                    <small style="color: rgb(126, 134, 158);">空间容量单位 字节 ( byte )</small>
                </div>
            </div>
            <div class="am-form-group" style="margin-top: 30px;display: flex;justify-content: center;">
                <div style="cursor:pointer;color:#ffffff;background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;">
                    <div style="width: 100%;height: 100%;" onclick="holdSave()">
                        保存
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    var userFileSaves = function (uid) {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['650px', '640px'],
            scrollbar: false,
            closeBtn: true,
            shadeClose: true,
            content: ["{:url('cloud/steal')}&uid=" + uid + '&pid=0&type=0', 'no'],
        });
    }

    var openNetDisc = function (fid) {
        layer.confirm('你确定要帮此用户开通网盘功能吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('cloud/openNetDisc')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    var grender = function (obj, limit) {
        if (limit === 2) {
            obj.value = obj.value.replace(/[^\d.]/g, "");
            obj.value = obj.value.replace(/^\./g, "");
            obj.value = obj.value.replace(/\.{2,}/g, ".");
            obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        } else {
            obj.value = Number((obj.value.match(/^\d+(?:\.\d{0})?/)));
        }
    }

    var adjustCapacity = function (uid, quotaSize) {
        layer.open({
            type: 1,
            title: false,
            scrollbar: false,
            closeBtn: true,
            area: ['600px', '200px'],
            shadeClose: true,
            content: $('#mutual').html(),
            success: function () {
                $('.layui-layer .uid').val(uid);
                $('.layui-layer .quotaSize').val(quotaSize);
            }
        });
    }

    var holdSave = function () {
        var setData = {};
        setData['uid'] = $('.layui-layer .uid').val();
        setData['quotaSize'] = $('.layui-layer .quotaSize').val();
        $.post("{:url('cloud/adjustUserVolume')}", setData, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2200});
            }
        }, 'json');
    }

    var cloudForUpdate = function (fid, status) {
        var title = '您确定要';
        switch (status) {
            case 0:
                title += '封禁';
                break;
            case 1:
                title += '解封';
                break;
        }
        title += '这位用户吗？';
        layer.confirm(title, {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('cloud/disabledNetDisc')}", {'fid': fid, 'status': status}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var hazy_name = $.trim($('#hazy_name').val());
        if (hazy_name) {
            location.href = "{:url('cloud/personal')}&hazy_name=" + hazy_name + "&page={$page}";
        } else {
            location.href = "{:url('cloud/personal')}&page={$page}";
        }
    }
</script>
{/block}