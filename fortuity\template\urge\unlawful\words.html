{extend name="/base"/}
{block name="main"}
<style>.el-textarea__inner{height:300px;resize:none}.width-69-5{width:69.5%}.width-29-5{width:29.5%}.virtual-scroll-textarea{height:300px;width:100%;overflow-y:auto;border:1px solid #DCDFE6;background-color:#FFF;border-radius:4px;padding:5px;margin:0}.virtual-scroll-content{width:100%;white-space:pre-wrap;word-break:break-all;font-family:inherit;font-size:14px;line-height:1.5;outline:none;padding:0;margin:0}.chunk-content{min-height:20px}</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-cog"></span> 违规词汇设置
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form isFormShow" style="display: none;">
            <div class="am-u-sm-12 am-u-md-12">
                <el-form label-position="top" ref="form" style="margin: 0 auto;width: 950px;">
                    <div style="display: flex;justify-content: center;">
                        <div class="width-69-5">
                            <el-card class="am-margin-top-xs">
                                <el-form-item label="图片安全验证">
                                    <el-select style="display: grid;" v-model="imageSafeValue" placeholder="请选择">
                                        <el-option v-for="item in imageOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                    <span style="top:-2px;position:relative;">是否选择开启网络验证 ( 开启后将调用第三方平台API进行验证 )</span>
                                </el-card>
                                <el-card class="am-margin-top-xs">
                                    <el-form-item label="用户名关键词禁用">
                                        <el-select style="display: grid;" v-model="nicknameSafeValue" placeholder="请选择">
                                            <el-option v-for="item in textOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                        </el-select>
                                        <span style="top:-2px;position:relative;">是否选择开启网络验证 ( 开启后将调用第三方平台API进行双重验证 )</span>
                                        <div class="el-textarea">
                                            <div class="virtual-scroll-textarea">
                                                <div class="virtual-scroll-content"
                                                     contenteditable="true"
                                                     @input="updateNicknameContent($event)"
                                                     @scroll="handleScroll($event, 'nickname')"
                                                     :ref="'nicknameEditor'">
                                                </div>
                                            </div>
                                        </div>
                                        <small>
                                            <span style="color:red;">多个关键词 请使用 | 分割
                                                例如 : <span style="color:black;font-weight:bold;">admin|管理员|网警|巡查</span> 等
                                            </span><br>
                                            <span style="font-weight:bold;">设置后用户将不能用以上关键词作为昵称</span>
                                        </small>
                                    </el-form-item>
                                </el-card>
                                <el-card class="am-margin-top-xs">
                                    <el-form-item label="内容关键词禁用">
                                        <el-select style="display: grid;" v-model="contentSafeValue" placeholder="请选择">
                                            <el-option v-for="item in textOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                        </el-select>
                                        <span style="top:-2px;position:relative;">是否选择开启网络验证 ( 开启后将调用第三方平台API进行双重验证 )</span>
                                        <div class="el-textarea">
                                            <div class="virtual-scroll-textarea">
                                                <div class="virtual-scroll-content"
                                                     contenteditable="true"
                                                     @input="updateContentContent($event)"
                                                     @scroll="handleScroll($event, 'content')"
                                                     :ref="'contentEditor'">
                                                </div>
                                            </div>
                                        </div>
                                        <small>
                                            <span style="color:red;">多个关键词 请使用 | 分割
                                                例如 : <span style="color:black;font-weight:bold;">abc|123|456</span> 等
                                            </span><br>
                                            <span style="font-weight:bold;">设置后用户将不能用以上关键词进行发帖或回帖等</span>
                                        </small>
                                    </el-form-item>
                                </el-card>
                                <div class="am-form-group am-margin-top-xl am-margin-bottom-xl">
                                    <div style="display: flex;justify-content: center;">
                                        <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存设置</button>
                                    </div>
                                </div>
                            </div>
                            <div style="width: 1%"></div>
                            <div class="width-29-5" v-if="imageSafeValue === '2' || nicknameSafeValue === '2' || contentSafeValue === '2'">
                                <el-card class="am-margin-top-xs">
                                    <div slot="header" class="clearfix">
                                        <span>腾讯云内容安全配置信息</span>
                                    </div>
                                    <el-form-item label="腾讯云SecretId">
                                        <el-input v-model="tencentCloudContentSecurityConfig.secretId" :placeholder="tencentCloudContentSecurityConfigPlaceholder.secretId"></el-input>
                                        <small>SecretID 是您项目的安全秘钥，具有该账户完全的权限，请妥善保管</small>
                                    </el-form-item>
                                    <el-form-item label="腾讯云SecretKey">
                                        <el-input v-model="tencentCloudContentSecurityConfig.secretKey" :placeholder="tencentCloudContentSecurityConfigPlaceholder.secretKey"></el-input>
                                        <small>SecretKey 是您项目的安全秘钥，具有该账户完全的权限，请妥善保管</small>
                                    </el-form-item>
                                    <el-form-item label="Region">
                                        <el-input v-model="tencentCloudContentSecurityConfig.region" :placeholder="tencentCloudContentSecurityConfigPlaceholder.region"></el-input>
                                        <small>Region 地区 例如: ap-beijing</small>
                                    </el-form-item>
                                </el-card>
                            </div>
                        </div>
                    </div>
                </el-form>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    var vm = new Vue({
        el: '#app',
        data() {
            // 在数据初始化时就清理内容
            const cleanContent = (text) => {
                return text
                    .replace(/\s+/g, '')  // 移除所有空白字符
                    .split('|')
                    .map(word => word.trim())
                    .filter(word => word)
                    .join('|');
            };
            
            // 解码并清理内容
            const nicknameContent = cleanContent(decodeURIComponent(atob('{$verificationList.nickname_offend}')));
            const contentContent = cleanContent(decodeURIComponent(atob('{$verificationList.content_offend}')));
            
            return {
                imageOptions: [{
                    value: '0',
                    label: '关闭'
                }, {
                    value: '1',
                    label: '珊瑚内容安全 ( 免费 )'
                }, {
                    value: '2',
                    label: '腾讯云 IMS ( 收费 )'
                }],
                textOptions: [{
                    value: '0',
                    label: '关闭'
                }, {
                    value: '1',
                    label: '珊瑚内容安全 ( 免费 )'
                }, {
                    value: '2',
                    label: '腾讯云 TMS ( 收费 )'
                }],
                imageSafeValue: '{$verificationList.open_network_images_offend}',
                nicknameSafeValue: '{$verificationList.open_network_nickname_offend}',
                contentSafeValue: '{$verificationList.open_network_content_offend}',
                nicknameOffend: nicknameContent,
                contentOffend: contentContent,
                tencentCloudContentSecurityConfig: {
                    secretId: '',
                    secretKey: '',
                    region: '{if $verificationList.tencent_cloud_content_security_config.region}{$verificationList.tencent_cloud_content_security_config.region}{else}ap-beijing{/if}'
                },
                tencentCloudContentSecurityConfigPlaceholder: {
                    secretId: '{if $verificationList.tencent_cloud_content_security_config.secretId}{$verificationList.tencent_cloud_content_security_config.secretId|ciphertext}{else}请输入腾讯云SecretId{/if}',
                    secretKey: '{if $verificationList.tencent_cloud_content_security_config.secretKey}{$verificationList.tencent_cloud_content_security_config.secretKey|ciphertext}{else}请输入腾讯云SecretKey{/if}',
                    region: '请输入区域 例如 ap-beijing',
                },
                nicknameOffendPlaceholder: '请输入用户名关键词禁用',
                contentOffendPlaceholder: '请输入内容关键词禁用',
                scrollPositions: {
                    nickname: 0,
                    content: 0
                },
                updateTimer: null,
                scrollTimer: null,
            };
        },
        methods: {
            holdSave() {
                // 在保存时处理内容格式
                const formatContent = (content) => {
                    return content
                        .replace(/\s+/g, '')  // 移除空白字符
                        .split('|')
                        .map(word => word.trim())
                        .filter(word => word)
                        .join('|');
                };

                var setData = {};
                setData['openNetworkImagesOffend'] = this.imageSafeValue;
                setData['openNetworkNicknameOffend'] = this.nicknameSafeValue;
                setData['nicknameOffend'] = formatContent(this.nicknameOffend);
                setData['openNetworkContentOffend'] = this.contentSafeValue;
                setData['contentOffend'] = formatContent(this.contentOffend);
                setData['tencentCloudContentSecurityConfig'] = this.tencentCloudContentSecurityConfig;
                
                $.post("{:url('unlawful/words')}", setData, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
            },
            updateEditorContent(editor, content) {
                // 使用 DocumentFragment 优化 DOM 操作
                const fragment = document.createDocumentFragment();
                const textNode = document.createTextNode(content);
                fragment.appendChild(textNode);
                
                // 清空原有内容并添加新内容
                editor.innerHTML = '';
                editor.appendChild(fragment);
            },
            updateNicknameContent(event) {
                const editor = event.target;
                const selection = window.getSelection();
                const range = selection.getRangeAt(0);
                const offset = range.startOffset;
                
                this.debouncedUpdateContent(() => {
                    const content = editor.innerText;
                    this.nicknameOffend = content;
                    
                    // 使用 DocumentFragment 更新内容
                    this.$nextTick(() => {
                        try {
                            this.updateEditorContent(editor, content);
                            
                            // 恢复光标位置
                            const newRange = document.createRange();
                            const textNode = editor.firstChild;
                            const newOffset = Math.min(offset, textNode.length);
                            newRange.setStart(textNode, newOffset);
                            newRange.collapse(true);
                            selection.removeAllRanges();
                            selection.addRange(newRange);
                        } catch (e) {
                            console.warn('Failed to update content:', e);
                        }
                    });
                });
            },
            updateContentContent(event) {
                const editor = event.target;
                const selection = window.getSelection();
                const range = selection.getRangeAt(0);
                const offset = range.startOffset;
                
                this.debouncedUpdateContent(() => {
                    const content = editor.innerText;
                    this.contentOffend = content;
                    
                    // 使用 DocumentFragment 更新内容
                    this.$nextTick(() => {
                        try {
                            this.updateEditorContent(editor, content);
                            
                            // 恢复光标位置
                            const newRange = document.createRange();
                            const textNode = editor.firstChild;
                            const newOffset = Math.min(offset, textNode.length);
                            newRange.setStart(textNode, newOffset);
                            newRange.collapse(true);
                            selection.removeAllRanges();
                            selection.addRange(newRange);
                        } catch (e) {
                            console.warn('Failed to update content:', e);
                        }
                    });
                });
            },
            saveScrollPosition(event, type) {
                this.scrollPositions[type] = event.target.scrollTop;
            },
            restoreScrollPosition(type) {
                const editor = this.$refs[type + 'Editor'];
                if (editor) {
                    editor.scrollTop = this.scrollPositions[type];
                }
            },
            throttle(fn, delay) {
                let last = 0;
                return function(...args) {
                    const now = Date.now();
                    if (now - last > delay) {
                        last = now;
                        fn.apply(this, args);
                    }
                }
            },
            debounce(fn, delay = 300) {
                return (...args) => {
                    if (this.updateTimer) clearTimeout(this.updateTimer);
                    this.updateTimer = setTimeout(() => fn.apply(this, args), delay);
                };
            },
            requestAnimationFrame(callback) {
                const raf = window.requestAnimationFrame ||
                    window.webkitRequestAnimationFrame ||
                    window.mozRequestAnimationFrame ||
                    window.msRequestAnimationFrame ||
                    (fn => setTimeout(fn, 1000/60));
                
                return raf.call(window, callback);
            },
            handleScroll(event, type) {
                if (!this.scrollTimer) {
                    this.scrollTimer = this.requestAnimationFrame(() => {
                        this.saveScrollPosition(event, type);
                        this.scrollTimer = null;
                    });
                }
            },
        },
        mounted() {
            this.debouncedUpdateContent = this.debounce(fn => fn());
            
            // 初始化编辑器内容
            this.$nextTick(() => {
                if (this.$refs.nicknameEditor) {
                    this.$refs.nicknameEditor.innerText = this.nicknameOffend;
                }
                if (this.$refs.contentEditor) {
                    this.$refs.contentEditor.innerText = this.contentOffend;
                }
            });
            
            $('.isFormShow').show();
        }
    });
</script>
{/block}