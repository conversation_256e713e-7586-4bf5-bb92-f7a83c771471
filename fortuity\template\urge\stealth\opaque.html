{extend name="/base"/}
{block name="main"}
<style>.am-table > tbody > tr > td{font-size:14px;margin-top:8px;}.am-table > tbody > tr > td:nth-child(1){text-align:right;color:#000000;padding-right:20px;}.am-table > tbody > tr > td:nth-child(2){padding-left:20px;color:#999;}table{table-layout:fixed;word-wrap:break-word;}img{max-width: 100% !important;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-comment-o"></span> 小秘密回复详情
        </div>
        <div style="text-align: right">
            <a href="{:url('stealth/janitor')}" target="_blank">
                <button type="button" class="am-btn am-btn-default am-btn-sm">小秘密回复列表</button>
            </a>
            <button type="button" class="am-btn am-btn-danger am-btn-sm" onclick="punishment();">删除小秘密回复</button>
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12">
                <div class="am-form am-form-horizontal" style="border:solid 1px #cccccc;width:700px;margin: 0 auto;padding:25px 3% 10px 3%;overflow: hidden;box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 1);">
                    <table class="am-table am-table-bordered">
                        <tr>
                            <td class="am-text-middle" style="width:35%;">用户昵称</td>
                            <td class="am-text-middle">
                                <a href="{:url('user/index')}&egon=0&openid={$list.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    <span style="font-size:13px;position:relative;top:2px;">{$list.user_nick_name|emoji_decode}</span>
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">小秘密的内容</td>
                            <td class="am-text-middle">{$spInfo.content|emoji_decode|$expressionHtml}</td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">小秘密回复的内容</td>
                            <td class="am-text-middle">{$list.re_content|emoji_decode|$expressionHtml}</td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">发布时间</td>
                            <td class="am-text-middle">{:date('Y-m-d H:i:s',$list.reply_time)}</td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">审核状态</td>
                            <td class="am-text-middle">
                                {if $list.status==0}
                                <span class="am-text-warning">待审核</span>
                                {elseif $list.status==1}
                                <span class="am-text-success">已通过</span>
                                {elseif $list.status==2}
                                <span class="am-text-danger">未通过</span>
                                {/if}
                            </td>
                        </tr>
                        {if $list.check_time}
                        <tr>
                            <td class="am-text-middle">审核时间</td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$list.check_time)}
                            </td>
                        </tr>
                        {/if}
                    </table>
                    {if $list.status==0}
                    <div class="am-form-group" style="text-align:center;margin-top:10px;">
                        <div class="am-u-sm-12">
                            <button type="button" class="am-btn am-btn-success am-btn-sm  am-round" style="margin-right: 120px;" onclick="judgment(1);">
                                通过
                            </button>
                            <button type="button" class="am-btn am-btn-warning am-btn-sm  am-round" onclick="judgment(2);">
                                拒绝
                            </button>
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var judgment = function (code) {
        if (code === 1) {
            layer.confirm("您确定同意通过审核吗？", {
                btn: ['确定', '取消'], title: '提示'
            }, function () {
                transmitDoom(code);
            }, function (index) {
                layer.close(index);
            });
        } else {
            layer.prompt({'title': "请您输入拒绝原因："}, function (rejectValue, index) {
                if ($.trim(rejectValue) === '') {
                    return false;
                }
                transmitDoom(code, rejectValue);
                layer.close(index);
            });
        }
    }

    var transmitDoom = function (code, caption) {
        $.ajaxSettings.async = false;
        $.post("{:url('stealth/politics')}", {'sid': '{$list.id}', code: code, caption: caption}, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                    location.reload();
                });
            }
        }, 'json');
        $.ajaxSettings.async = true;
    }

    var punishment = function () {
        layer.confirm('您确定要删除这条小秘密回复吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('stealth/delJanitor')}", {sid: '{$list.id}'}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.href = "{:url('stealth/janitor')}";
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

</script>
{/block}