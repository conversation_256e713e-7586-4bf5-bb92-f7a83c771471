{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-video-camera {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .action-btn {display: inline-block;padding: 6px 12px;background: #fff;border: 1px solid #ddd;color: #23b7e5;border-radius: 3px;font-size: 12px;cursor: pointer;transition: all 0.3s;text-decoration: none;}
    .action-btn:hover {border-color: #23b7e5;color: #23b7e5;background-color: #f5fafd;text-decoration: none;}
    .action-btn.btn-danger {background: #d9534f;border-color: #d9534f;color: #fff;}
    .action-btn.btn-danger:hover {background: #c9302c;border-color: #c9302c;color: #fff;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .sort-input {width: 50px;padding: 4px 6px;border: 1px solid #e8e8e8;border-radius: 3px;text-align: center;font-size: 12px;transition: all 0.3s;}
    .sort-input:focus {border-color: #23b7e5;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .drama-title {font-weight: 500;color: #333;}
    .episode-link {color: #23b7e5;text-decoration: none;font-weight: 500;}
    .episode-link:hover {color: #1a9bc0;text-decoration: underline;}
    .vip-badge {display: inline-block;padding: 3px 8px;border-radius: 12px;font-size: 11px;font-weight: 500;background-color: #ffd700;color: #856404;border: 1px solid #ffeaa7;}
    .price-info {font-weight: 600;color: #e67e22;}
    .audit-status {display: inline-block;padding: 3px 8px;border-radius: 12px;font-size: 11px;font-weight: 500;}
    .audit-pending {background-color: #fff3cd;color: #856404;border: 1px solid #ffeaa7;}
    .audit-passed {background-color: #d4edda;color: #155724;border: 1px solid #c3e6cb;}
    .audit-rejected {background-color: #f8d7da;color: #721c24;border: 1px solid #f5c6cb;}
    .display-status {display: inline-block;padding: 3px 8px;border-radius: 12px;font-size: 11px;font-weight: 500;}
    .display-normal {background-color: #d4edda;color: #155724;border: 1px solid #c3e6cb;}
    .display-offline {background-color: #f8d7da;color: #721c24;border: 1px solid #f5c6cb;}
    .modern-dropdown {position: relative;display: inline-block;}
    .modern-dropdown-btn {background: linear-gradient(135deg, #23b7e5 0%, #1a9bc0 100%);border: none;color: white;padding: 8px 16px;border-radius: 6px;font-size: 13px;cursor: pointer;transition: all 0.3s ease;box-shadow: 0 2px 4px rgba(35,183,229,0.2);display: flex;align-items: center;gap: 6px;}
    .modern-dropdown-btn:hover {background: linear-gradient(135deg, #1a9bc0 0%, #1587a8 100%);box-shadow: 0 4px 8px rgba(35,183,229,0.3);transform: translateY(-1px);}
    .modern-dropdown .am-dropdown-content {border: none;border-radius: 6px;box-shadow: 0 4px 12px rgba(0,0,0,0.15);margin-top: 4px;overflow: hidden;}
    .modern-dropdown .am-dropdown-content li a {padding: 10px 16px;color: #333;transition: all 0.2s ease;display: flex;align-items: center;gap: 8px;}
    .modern-dropdown .am-dropdown-content li a:hover {background-color: #f8f9fa;color: #23b7e5;}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-video-camera"></span> 短剧视频
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="commonSearch();"></i>
                <input type="text" id="searchName" value="{$searchName}" placeholder="搜索短剧名称...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12" style="text-align: right;">
                <button type="button" class="action-btn btn-danger" onclick="batchReviewProve();">
                    <span class="am-icon-trash"></span> 批量删除
                </button>
            </div>
        </div>

        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="7%">
                                <input id="withole" type="checkbox" class="tpl-table-fz-check"> 全选
                            </th>
                            <th width="7%">排序</th>
                            <th width="18%">短剧名称</th>
                            <th width="7%">集数</th>
                            <th width="9%">VIP观看</th>
                            <th width="11%">付费价格</th>
                            <th width="9%">审核状态</th>
                            <th width="8%">显示状态</th>
                            <th width="12%">创建时间</th>
                            <th width="12%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                <input type="checkbox" class="tpl-table-fz-check elctive" value="{$vo.id}">
                            </td>
                            <td class="am-text-middle">
                                <input type="text" class="sort-input" id="sort-fixed-{$vo.id}" value="{$vo.sort}" data-sort="{$vo.sort}" onblur="commonSort('{$vo.id}','#sort-fixed-{$vo.id}');">
                            </td>
                            <td class="am-text-middle">
                                <span class="drama-title">{$vo.msi_title}</span>
                            </td>
                            <td class="am-text-middle">
                                <a href="{$vo.msi_episode_url}" target="_blank" class="episode-link">
                                    第{$vo.msi_episode_number}集
                                </a>
                            </td>
                            <td class="am-text-middle">
                                {if $vo.is_allow_only_vip}
                                <span class="vip-badge">VIP专享</span>
                                {else}
                                <span style="color: #666;">普通</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.paid_unlocking_type > 0}
                                <span class="price-info">{$vo.paid_unlocking_price}</span>
                                {switch $vo.paid_unlocking_type}
                                {case 1}<span style="color: #666;"> 贝壳</span>{/case}
                                {case 2}<span style="color: #666;"> 积分</span>{/case}
                                {/switch}
                                {else}
                                <span style="color: #27ae60;font-weight: 500;">免费</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.status == 0}
                                <span class="audit-status audit-pending">待审核</span>
                                {elseif $vo.status == 1}
                                <span class="audit-status audit-passed">已通过</span>
                                {elseif $vo.status == 2}
                                <span class="audit-status audit-rejected">未通过</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.display_status == 0}
                                <span class="display-status display-offline">已下架</span>
                                {else}
                                <span class="display-status display-normal">正常</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s', $vo.create_time)}
                            </td>
                            <td class="am-text-middle">
                                <div class="modern-dropdown am-dropdown" data-am-dropdown>
                                    <button class="modern-dropdown-btn am-dropdown-toggle" data-am-dropdown-toggle>
                                        <span>功能列表</span>
                                        <span class="am-icon-caret-down"></span>
                                    </button>
                                    <ul class="am-dropdown-content" style="min-width: 120px;">
                                        <li>
                                            <a href="javascript:void(0);" onclick="reviewDetail('{$vo.id}')">
                                                <span class="am-icon-eye"></span> 查看详情
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" onclick="exportChildContentToCsv('{$vo.id}')">
                                                <span class="am-icon-download"></span> 导出数据
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" onclick="commonEdit('{$vo.id}')">
                                                <span class="am-icon-edit"></span> 编辑内容
                                            </a>
                                        </li>
                                        {if $vo.status==0}
                                        <li>
                                            <a href="javascript:void(0);" onclick="auditCorrect('{$vo.id}',1)">
                                                <span class="am-icon-check"></span> 审核通过
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" onclick="auditCorrect('{$vo.id}',2)">
                                                <span class="am-icon-times"></span> 审核拒绝
                                            </a>
                                        </li>
                                        {/if}
                                        <li>
                                            <a href="javascript:void(0);" onclick="commonDelete(1,'{$vo.id}')">
                                                <span class="am-icon-trash"></span> 删除内容
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    !function () {
        $('#withole').click(function () {
            var wecked = $(this).prop('checked');
            if (wecked) {
                $('.elctive').prop('checked', true);
            } else {
                $('.elctive').prop('checked', false);
            }
        });
        $('.elctive').click(function () {
            var jack = 1;
            $('.elctive').each(function () {
                var weck = $(this).prop('checked');
                if (weck == false) {
                    jack = 0;
                    return false;
                }
            });
            if (jack) {
                $('#withole').prop('checked', true);
            } else {
                $('#withole').prop('checked', false);
            }
        });
    }();

    function commonSort(fid, sortFixedId) {
        var newSortFixedId = $.trim($(sortFixedId).val());
        var oldSortFixedId = $.trim($(sortFixedId).attr('data-sort'));
        if (parseInt(newSortFixedId) > 2147483646) {
            layer.msg('排序数值太大');
            $(sortFixedId).val(oldSortFixedId);
            return false;
        }
        if (newSortFixedId !== oldSortFixedId) {
            var result = commonUpdateSort(fid, newSortFixedId);
            if (result.code > 0) {
                layer.msg(result.msg, {icon: 1, time: 800});
                $(sortFixedId).attr('data-sort', newSortFixedId);
            } else {
                layer.msg(result.msg, {icon: 5, time: 1600});
            }
        }
    }

    function commonUpdateSort(fid, newSortFixedId) {
        var result = [];
        $.ajax({
            type: "post",
            url: "{:url('dramas/micro_series_content_sort')}",
            data: {fid, sort: newSortFixedId},
            async: false,
            success: function (data) {
                result = data;
            }
        });
        return result;
    }

    var reviewDetail = function (fid) {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['550px', '600px'],
            scrollbar: true,
            closeBtn: true,
            shadeClose: true,
            content: ["{:url('dramas/micro_series_content_detail')}&fid=" + fid],
        });
    }

    function commonEdit(fid) {
        location.href = "{:url('dramas/edit_micro_series_content')}&fid=" + fid;
    }

    function exportChildContentToCsv(fid) {
        //  js下载文件
        location.href = "{:url('dramas/export_micro_series_content_to_csv')}&fid=" + fid;
    }


    var twoCheck = function (fid, process, reaValue) {
        $.ajax({
            type: "post",
            url: "{:url('dramas/trial_micro_series_content')}",
            data: {'fid': fid, 'process': process, 'inject': reaValue},
            dataType: 'json',
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }
        });
    }

    var auditCorrect = function (fid, process) {
        switch (process) {
            case 1:
                layer.confirm('您确定要审核通过这条数据吗？', {
                    btn: ['确定', '取消'], title: '提示'
                }, function () {
                    twoCheck(fid, process);
                }, function (index) {
                    layer.close(index);
                });
                break;
            case 2:
                layer.prompt({
                    title: '请输入这条数据未通过审核的原因：',
                    formType: 2,
                    area: ['300px', '100px'],
                    btn: ['确定', '取消'],
                }, function (reaValue, index) {
                    if (reaValue.trim() === '') {
                        return false;
                    }
                    twoCheck(fid, process, reaValue);
                    layer.close(index);
                });
                break;
        }
    }

    var batchReviewProve = function () {
        var acid = [];
        $('.elctive').each(function () {
            if ($(this).prop('checked')) {
                acid.push($(this).val());
            }
        });
        if (acid.length === 0) {
            layer.alert('很抱歉，当前没有选中要操作的数据', {'title': '系统提示'});
            return;
        }
        layer.confirm("您确定批量删除选中的视频吗？", {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            commonDelete(0, acid);
        }, function (index) {
            layer.close(index);
        });
    }

    function commonDelete(type, fid) {
        var confimDel = function (fid) {
            $.post("{:url('dramas/del_micro_series_content')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        }
        if (type === 0) {
            confimDel(fid);
        } else {
            var confirmMessage = '您确定要删除这条数据吗';
            layer.confirm(confirmMessage, {
                btn: ['确定', '取消']
            }, function () {
                confimDel([fid]);
            }, function (index) {
                layer.close(index);
            });
        }
    }

    function commonSearch() {
        var searchName = $.trim($('#searchName').val());
        if (searchName) {
            location.href = "{:url('dramas/micro_series_content')}&searchName=" + searchName + "&page={$page}";
        } else {
            location.href = "{:url('dramas/micro_series_content')}&page={$page}";
        }
    }
</script>
{/block}
