{extend name="/base"/}
{block name="main"}
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 一键拨号
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-8 am-u-sm-push-1" style="margin-top: 50px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">强制输入手机号</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.forceInputPhone">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>开启后用户发帖时一键拨号内容不能为空</small>
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;margin: 50px 0 30px 0;">
                        <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    new Vue({
        el: '#app',
        data() {
            return {
                item: {
                    forceInputPhone: '0'
                }
            }
        }, created() {
            this.item.forceInputPhone = '{$list.force_input_phone}';
        },
        methods: {
            holdSave() {
                $.post("{:url('people/dial')}", {'item': this.item}, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
            }
        }
    });
</script>
{/block}