<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"/>
    <meta name="format-detection" content="telephone=no,email=no,date=no,address=no"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="referrer" content="never">
    <title>上传文件</title>
    <link rel="stylesheet" href="./static/layui/css/layui.css">
</head>
<body>
<div style="text-align: center;margin: 50px 0px 20px 0px;">
    <i class="layui-icon layui-icon-upload-drag" style="font-size: 80px; color: #33CC66;"></i>
</div>
<!--<div style="text-align: center;font-size: 20px;font-weight: 300;">上传本地文件</div>-->

<div style="text-align: center;font-size: 16px;font-weight: 300;margin-top: 10px">
    {if condition="$key == 0"}
    <div style="text-align: center;font-size: 20px;font-weight: 300;">{$msg}</div>
    {else /}
    <div style="text-align: center;font-size: 20px;font-weight: 300;">{$msg}</div>
    {/if}
</div>
{if condition="$key == 0"}
<div style="text-align: center;margin: 40px;">
    <button id="sp" onclick="$('#netDiscFile').click();" type="button" class="layui-btn layui-btn-normal">
        <i class="layui-icon layui-icon-upload layui-font-12"></i> 上传
    </button>
</div>
{/if}
<span style="display: none;">
        <input id="netDiscFile" type="file" name="netDiscFile" onchange="uploadFiles();">
    </span>
<div id="percent_show" style="display: none;margin-top: 300px">
    <div style="margin: 0px 40px;">上传进度：</div>
    <div class="layui-progress layui-progress-big" style="text-align: center;margin: 10px 40px;">
        <div class="layui-progress-bar layui-bg-blue" id="percent"></div>
    </div>
</div>


<script src="./assets/js/jquery.min.js"></script>
<script src="./static/layui/layui.js"></script>
<script src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"></script>
<script>
    // 或者
    wx.miniProgram.getEnv(function (res) {
        console.log(res) // true
    })

    function open_url() {
        wx.miniProgram.postMessage({data: {key: '1'}})
        wx.miniProgram.navigateBack();
    }

    var uploadFiles = function () {
        var netDiscFile = $('#netDiscFile');
        var filesExtLimit = '{$allowedUploadTypes}';
        var filesExtLimitArray = filesExtLimit.split(',');
        var filesQualified = true;
        $(netDiscFile.get(0).files).each(function (i) {
            var suffix = netDiscFile.get(0).files[i].name.lastIndexOf('.');
            filesQualified = filesExtLimitArray.includes(netDiscFile.get(0).files[i].name.substring(suffix + 1, netDiscFile.get(0).files[i].name.length).toLowerCase());
            if (!filesQualified) {
                return false;
            }
        });
        if (filesQualified) {
            var loadIndex = layer.msg('上传中...', {
                icon: 16,
                shade: 0.3
            });
            $(netDiscFile.get(0).files).each(function (i) {
                var formData = new FormData();
                formData.append('netDiscFile', netDiscFile.get(0).files[i]);
                formData.append('uid', '{$uid}');
                formData.append('pid', '{$pid}');
                formData.append('much_id', '{$much_id}');
                $.ajax({
                    type: "post",
                    url: "{:url('Wechat/uploadNetDisc')}",
                    //async: false,
                    data: formData,
                    processData: false,
                    contentType: false,
                    dataType: 'json',
                    xhr: function() {
                        var xhr = new XMLHttpRequest();
                        $('#percent_show').css('display', 'block');
                        //使用XMLHttpRequest.upload监听上传过程，注册progress事件，打印回调函数中的event事件
                        xhr.upload.addEventListener('progress', function (e) {
                            //loaded代表上传了多少
                            //total代表总数为多少
                            var progressRate = (e.loaded / e.total) * 100 + '%';
                            //通过设置进度条的宽度达到效果
                            $('#percent').css('width', progressRate);
                        })

                        return xhr;
                    },
                    success: function (data) {
                        if (data.code > 0) {
                            layer.msg('上传成功', {icon: 1, time: 1000}, function () {
                                open_url();
                            });
                        } else {
                            layer.msg('上传失败，' + data.msg);
                        }
                    }
                });
            });
        } else {
            netDiscFile.val('');
            layer.msg('目前仅支持上传 ' + filesExtLimit + ' 类型的文件，请排除后再次尝试！', {time: 3000});
        }
    }
</script>
</body>
</html>