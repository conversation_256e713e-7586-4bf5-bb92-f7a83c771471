{extend name="/base"/}
{block name="main"}
<style>.am-modal-hd .am-close{top:-5px;right:-3px;}.w-e-menu{font-size:12px}.w-e-text,.w-e-text-container{height:500px !important;}@media screen and (max-width:1024px){.product-details{margin:10px 0 120px 0 !important;}}@media screen and (max-width:768px){.product-details{margin:10px 0 140px 0 !important;}}@media screen and (max-width:640px){.product-details{margin:10px 0 160px 0 !important;}}@media screen and (max-width:400px){.product-details{margin:10px 0 200px 0 !important;}}@media screen and (max-width:320px){.product-details{margin:10px 0 220px 0 !important;}}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 编辑商家
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商家名称</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="name" placeholder="请输入商家名称" value="{$list.merchant_name}">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商家类型</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select id="merchantType">
                                <option value="-1">请选择</option>
                                {volist name="eitList" id="vo"}
                                <option value="{$vo.id}" {if $list.merchant_type==$vo.id}selected{/if}>{$vo.name}</option>
                                {/volist}
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商家电话</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="merchantPhone" placeholder="请输入商家电话" value="{$list.merchant_phone}">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商家图片</label>
                        <div class="am-u-sm-9">
                            <div id="shion">
                                {volist name="list.merchant_icon_carousel" id="vo"}
                                <div class="multiple-img" style="width:120px;height:120px;margin:10px 20px 10px 0;position: relative;float: left;" data-multiple-img="{$vo}">
                                    <img src="{$vo}" name="sngimg" onerror="this.src='static/disappear/default.png'" style="width: 120px;height: 120px;margin: 7px 0px 0px 3px;"/>
                                    <div style="width: 126px;height: 126px;position: absolute;top: 4px;border: 1px solid #cccccc;border-radius: 3px;">
                                        <div class="am-modal-hd" style="text-align: left;">
                                            <a href="javascript: void(0);" class="am-close am-close-spin" style="color:#2D93CA;opacity:1;" onclick="multipleClose(this);">×</a>
                                        </div>
                                    </div>
                                </div>
                                {/volist}
                            </div>
                        </div>
                        <div class="am-u-sm-9" style="margin-top: 30px;">
                            <button type="button" style="font-size: 12px;" onclick="cuonice();">选择图片</button>
                            <small>建议图片尺寸：150*150px</small>
                            <span style="color: red;font-size: 12px;">商家图片可以通过拖拽进行排序 </span>
                            <small><strong>( 第一张图片将作为商家主图 )</strong></small>
                        </div>
                    </div>
                    <div class="am-form-group product-details" style="margin:10px 0 60px 0;">
                        <label class="am-u-sm-3 am-form-label">商家介绍</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div style="height:500px;" id="detail" placeholder="请输入商家介绍"></div>
                            <span id="customizeGallery" style="display:none;" onclick="cuonice(1);"></span>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">地址位置</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="addressName" placeholder="请输入地址位置" value="{$list.address_name}">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">地址经度</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="addressLongitude" placeholder="请输入地址经度" value="{$list.address_longitude}">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">地址纬度</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="addressLatitude" placeholder="请输入地址纬度" value="{$list.address_latitude}">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">显示状态</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select id="status">
                                <option value="-1">请选择</option>
                                <option value="0" {if $list.status==0}selected{/if}>隐藏</option>
                                <option value="1" {if $list.status==1}selected{/if}>显示</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-6 am-u-sm-push-6" style="margin-top: 30px;">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script src="assets/js/jquery.dad.min.js"></script>
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js"></script>
<script>

    var E = window.wangEditor;
    var editor = new E('#detail');
    editor.customConfig.uploadImgServer = true;
    editor.create();
    E.fullscreen.init('#detail');

    window.onload = function () {
        var content = '{$list.merchant_introduce}';
        editor.cmd.do('insertHTML', decodeURIComponent(atob(content)));
    }

    var cuonice = function (richText) {
        var dynamicUrl = "{:url('images/dialogImages')}&gclasid=0";
        if (richText == 1) {
            dynamicUrl += "&dynamicStyle=richText";
        }
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: [dynamicUrl, 'no']
        });
    }

    var sutake = function (eurl,richText) {
        if (richText == 1) {
            editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
        } else {
            var multipleImg = $.trim($('.multiple-img').eq(0).attr('data-multiple-img'));
            if (multipleImg == '') {
                $('#shion').html('');
            }
            var shtml = '<div class="multiple-img" style="width:120px;height:120px;margin:10px 20px 10px 0px;position: relative;float: left;" data-multiple-img="' + eurl + '">';
            shtml += '<img src="' + eurl + '" name="sngimg" onerror="this.src=\'static/disappear/default.png\'" style="width: 120px;height: 120px;margin: 7px 0px 0px 3px;"/>';
            shtml += '<div style="width: 126px;height: 126px;position: absolute;top: 4px;border: 1px solid #cccccc;border-radius: 3px;">';
            shtml += '<div class="am-modal-hd" style="text-align: left;">';
            shtml += '<a href="javascript: void(0);" class="am-close am-close-spin" style="color:#2D93CA;opacity:1;" onclick="multipleClose(this);">×</a>';
            shtml += '</div>';
            shtml += '</div>';
            shtml += '</div>';
            $('#shion').append(shtml);
            $('#shion').dad();
        }
    }

    function multipleClose(obj) {
        $(obj).parent().parent().parent().remove();
        setTimeout(function () {
            if ($('.multiple-img').length < 1) {
                var shtml = '<div class="multiple-img" style="width:120px;height:120px;margin:10px 20px 10px 0px;position: relative;float: left;">';
                shtml += '<img src="" name="sngimg" onerror="this.src=\'static/disappear/default.png\'" style="width: 120px;height: 120px;margin: 7px 0px 0px 3px;"/>';
                shtml += '<div style="width: 126px;height: 126px;position: absolute;top: 4px;border: 1px solid #cccccc;border-radius: 3px;">';
                shtml += '<div class="am-modal-hd" style="text-align: left;">';
                shtml += '<a href="javascript: void(0);" class="am-close am-close-spin" style="color:#2D93CA;opacity:1;" onclick="multipleClose(this);">×</a>';
                shtml += '</div>';
                shtml += '</div>';
                shtml += '</div>';
                $('#shion').append(shtml);
            }
        }, 500);
    }

    var onlock = false;
    var holdSave = function () {
        var setData = {};
        setData['fid'] = '{$list.id}';
        setData['name'] = $.trim($('#name').val());
        if (setData['name'] === '') {
            layer.msg('请输入商家名称');
            return;
        }
        setData['type'] = Number($('#merchantType').val());
        if (setData['type'] === -1) {
            layer.msg('请选择商家类型');
            return;
        }
        setData['phone'] = $.trim($('#merchantPhone').val());
        if (setData['phone'] === '') {
            layer.msg('请输入商家电话');
            return;
        }
        setData['iconCarousel'] = [];
        $('.multiple-img').each(function () {
            setData['iconCarousel'].push($.trim($(this).attr('data-multiple-img')));
        });
        if (setData['iconCarousel'][0] === '' || setData['iconCarousel'] === 'undefined' || setData['iconCarousel'] == null) {
            layer.msg('请选择商家图片');
            return false;
        }
        setData['content'] = $.trim(editor.txt.html());
        setData['address'] = $.trim($('#addressName').val());
        if (setData['address'] === '') {
            layer.msg('请输入商家地理位置');
            return;
        }
        setData['longitude'] = $.trim($('#addressLongitude').val());
        if (setData['longitude'] === '') {
            layer.msg('请输入商家位置经度');
            return;
        }
        setData['latitude'] = $.trim($('#addressLatitude').val());
        if (setData['latitude'] === '') {
            layer.msg('请输入商家位置纬度');
            return;
        }
        setData['status'] = Number($('#status').val());
        if (setData['status'] === -1) {
            layer.msg('请选择显示状态');
            return;
        }
        if (!onlock) {
            onlock = false;
            $.post("{:url('pluto/edit_merchant_list')}", setData, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1000}, function () {
                        onlock = false;
                    });
                }
            }, 'json');
        }
    }
</script>
{/block}