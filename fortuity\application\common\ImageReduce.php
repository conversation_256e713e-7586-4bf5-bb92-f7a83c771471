<?php

namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

class ImageReduce
{

    private $src;
    private $image;
    private $imageInfo;
    private $percent = 0.5;

    /*
    param    $src源图
    param    $percent压缩比例
    */
    public function __construct($src, $percent = 1)
    {
        $this->src = $src;
        $this->percent = $percent;
    }


    /*
    param string $saveName 图片名（可不带扩展名用原图名）用于保存。或不提供文件名直接显示
    */
    public function compressImg($saveName = '')
    {
        $this->_openImage();
        if (!empty($saveName)) {
            $this->_saveImage($saveName); //保存
        } else {
            $this->_showImage();
        }
    }


    /*
    内部：打开图片
    */
    private function _openImage()
    {
        list($width, $height, $type, $attr) = getimagesize($this->src);

        // 动态获取PHP内存限制，并计算一个安全的阈值
        $memory_limit_str = ini_get('memory_limit');
        $memory_limit = $this->convertToBytes($memory_limit_str);
        
        // 更精确地计算所需内存：原图 + 缩略图，因为它们会短暂地同时存在于内存中
        $memoryForOriginal = $width * $height * 4;
        $memoryForThumb = ($width * $this->percent) * ($height * $this->percent) * 4;
        $imageDataMemory = $memoryForOriginal + $memoryForThumb;

        // 动态计算安全余量：至少16MB，或者是图片所需内存的20%，取较大者
        $safety_margin = max(16 * 1024 * 1024, $imageDataMemory * 0.2);

        $totalMemoryRequired = $imageDataMemory + $safety_margin;

        if ($totalMemoryRequired > $memory_limit) {
            throw new \Exception(
                "Processing this image would require approximately " . round($totalMemoryRequired / 1024 / 1024) . "MB (image data + safety margin), " .
                "which exceeds the PHP memory limit of " . $memory_limit_str . "."
            );
        }

        $this->imageInfo = array(
            'width' => $width,
            'height' => $height,
            'type' => image_type_to_extension($type, false),
            'attr' => $attr
        );
        $fun = "imagecreatefrom" . $this->imageInfo['type'];
        $this->image = $fun($this->src);
        $this->_thumpImage();
    }

    /**
     * 将ini的内存字符串 (例如 '256M') 转换为字节.
     * @param string $val
     * @return int
     */
    private function convertToBytes($val)
    {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = intval($val);
        switch($last) {
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }
        return $val;
    }


    /**
     * 内部：操作图片
     */
    private function _thumpImage()
    {
        $new_width = $this->imageInfo['width'] * $this->percent;
        $new_height = $this->imageInfo['height'] * $this->percent;
        $image_thump = imagecreatetruecolor($new_width, $new_height);

        // 处理 PNG 格式的透明色
        if ($this->imageInfo['type'] == 'png') {
            imagealphablending($image_thump, false); // 关闭透明度混合
            $color = imagecolorallocatealpha($image_thump, 0, 0, 0, 127); // 创建新的透明颜色
            imagefill($image_thump, 0, 0, $color); // 用透明颜色填充背景
            imagesavealpha($image_thump, true); // 设置保存 alpha 通道信息
        }

        // 分块处理图像，减少内存占用
        $block_size = 100; // 块大小，适当调整此值以适应不同图片大小
        for ($y = 0; $y < $this->imageInfo['height']; $y += $block_size) {
            for ($x = 0; $x < $this->imageInfo['width']; $x += $block_size) {
                // 计算当前块的实际尺寸（避免超出图像边界）
                $block_width = min($block_size, $this->imageInfo['width'] - $x);
                $block_height = min($block_size, $this->imageInfo['height'] - $y);

                // 如果处理的是边缘块，增加一个像素的重叠，确保边缘像素被正确处理
                $src_x = $x == 0 ? 0 : $x - 1;
                $src_y = $y == 0 ? 0 : $y - 1;
                $src_w = $x == 0 ? $block_width : $block_width + 1;
                $src_h = $y == 0 ? $block_height : $block_height + 1;

                $dst_x = $x == 0 ? 0 : ($x - 1) * $this->percent;
                $dst_y = $y == 0 ? 0 : ($y - 1) * $this->percent;
                $dst_w = $src_w * $this->percent;
                $dst_h = $src_h * $this->percent;

                // 将当前块拷贝到目标图像上，并按比例缩放
                imagecopyresampled(
                    $image_thump,
                    $this->image,
                    $dst_x,
                    $dst_y,
                    $src_x,
                    $src_y,
                    $dst_w,
                    $dst_h,
                    $src_w,
                    $src_h
                );
            }
        }
        // 销毁原始图像资源，释放内存
        imagedestroy($this->image);
        $this->image = $image_thump;
    }


    /**
     * 输出图片:保存图片则用saveImage()
     */
    private function _showImage()
    {
        header('Content-Type: image/' . $this->imageInfo['type']);
        $funcs = "image" . $this->imageInfo['type'];
        $funcs($this->image);
    }


    /**
     * 保存图片到硬盘：
     * @param string $dstImgName 1、可指定字符串不带后缀的名称，使用源图扩展名 。2、直接指定目标图片名带扩展名。
     */
    private function _saveImage($dstImgName)
    {
        if (empty($dstImgName))
            return false;
        $allowImgs = ['.jpg', '.jpeg', '.png', '.bmp', '.wbmp', '.gif'];   //如果目标图片名有后缀就用目标图片扩展名 后缀，如果没有，则用源图的扩展名
        $dstExt = strrchr($dstImgName, ".");
        $sourseExt = strrchr($this->src, ".");
        if (!empty($dstExt))
            $dstExt = strtolower($dstExt);
        if (!empty($sourseExt))
            $sourseExt = strtolower($sourseExt);

        //有指定目标名扩展名
        if (!empty($dstExt) && in_array($dstExt, $allowImgs)) {
            $dstName = $dstImgName;
        } elseif (!empty($sourseExt) && in_array($sourseExt, $allowImgs)) {
            $dstName = $dstImgName . $sourseExt;
        } else {
            $dstName = $dstImgName . $this->imageInfo['type'];
        }
        $funcs = "image" . $this->imageInfo['type'];
        $funcs($this->image, $dstName);
    }


    /**
     * 销毁图片
     */
    public function __destruct()
    {
        imagedestroy($this->image);
    }

}