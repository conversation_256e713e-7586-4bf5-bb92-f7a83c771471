{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.resth button{margin-right:5px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-bar-chart"></span> 发帖排行榜
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}"
                           placeholder="搜索用户名...">
                </div>
            </div>
        </div>

    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-6 am-u-md-7">
                <div class="am-btn-toolbar" style="margin: 0px 0px 10px 20px;">
                    <div class="am-btn-group am-btn-group-xs resth">
                        <a href="{:url('user/speak_ranking')}&egon=0" class="cust-btn cust-btn-one {if $egon==0}cust-btn-activate{/if}">全部排行</a>
                        <a href="{:url('user/speak_ranking')}&egon=1" class="cust-btn {if $egon==1}cust-btn-activate{/if}">今日排行</a>
                        <a href="{:url('user/speak_ranking')}&egon=2" class="cust-btn {if $egon==2}cust-btn-activate{/if}">本周排行</a>
                        <a href="{:url('user/speak_ranking')}&egon=3" class="cust-btn {if $egon==3}cust-btn-activate{/if}">本月排行</a>
                        <a href="{:url('user/speak_ranking')}&egon=4" class="cust-btn {if $egon==4}cust-btn-activate{/if}">今年排行</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th class="text-center" width="20%">用户头像</th>
                            <th class="text-center" width="20%">用户名称</th>
                            <th class="text-center" width="25%">openid</th>
                            <th class="text-center" width="20%">发帖详情</th>
                            <th class="text-center" width="15%">发帖次数</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle text-center" style="height: 85px;">
                                <img src="{$vo.user_head_sculpture}" style="width:70px;height:70px;border-radius:50%;">
                            </td>
                            <td class="am-text-middle text-center" style="height: 85px;">
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="am-text-middle text-center" style="height: 85px;">
                                {if $vo.user_wechat_open_id}
                                {$vo.user_wechat_open_id}
                                {else}
                                ( 虚拟用户 )
                                {/if}
                            </td>
                            <td class="am-text-middle text-center" style="height: 85px;">
                                <span style="background: #75adbf;border-radius: 3px;color: white;padding: 5px 10px;cursor: pointer;" onclick="tance('{$vo.user_wechat_open_id}');">
                                    查 看
                                </span>
                            </td>
                            <td class="am-text-middle text-center" style="height: 85px;">{$vo.cuid} 次</td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var tance = function (openid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('essay/index')}&egon=0&hazy_name=" + openid + "&page=1");
        dynamicUrl.attr('target', '_blank');
        dynamicUrl.get(0).click();
    }

    function fuzzy() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('user/speak_ranking')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('user/speak_ranking')}&egon={$egon}&page={$page}";
        }
    }

</script>
{/block}