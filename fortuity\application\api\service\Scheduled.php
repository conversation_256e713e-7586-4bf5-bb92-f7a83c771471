<?php

namespace app\api\service;

use think\Controller;
use think\Db;

class Scheduled extends Controller
{
    /**
     * 查询用户任务完成数
     */
    public static function GetTaskComplete($uid, $much_id)
    {
        $list = Db::name('task')->where('much_id', $much_id)->where('is_being', 0)->order('scores')->select();
        $sum = 0;
        foreach ($list as $k => $v) {
            if ($v['task_cycle'] == 0) {//每日任务
                $ok_count = Scheduled::finished_count($v['id'], $uid, $much_id);
                $count = Scheduled::Finished($v['id'], $uid);
                if (($count >= $v['task_frequency']) && $ok_count == 0) {
                    $sum += 1;
                }
            }
            if ($v['task_cycle'] == 1) {//每周任务
                $ok_count = Scheduled::finished_count($v['id'], $uid, $much_id);
                $count = Scheduled::Finished($v['id'], $uid);
                if (($count >= $v['task_frequency']) && $ok_count == 0) {
                    $sum += 1;
                }
            }
            if ($v['task_cycle'] == 2) {//每月任务
                $ok_count = Scheduled::finished_count($v['id'], $uid, $much_id);
                $count = Scheduled::Finished($v['id'], $uid);
                if (($count >= $v['task_frequency']) && $ok_count == 0) {
                    $sum += 1;
                }
            }
            if ($v['task_cycle'] == 3) {//每年任务
                $ok_count = Scheduled::finished_count($v['id'], $uid, $much_id);
                $count = Scheduled::Finished($v['id'], $uid);
                if (($count >= $v['task_frequency']) && $ok_count == 0) {
                    $sum += 1;
                }
            }
        }
        return $sum;
    }

    /**
     * 查询完成数量
     */
    public static function finished_count($task_id, $user_id, $much_id)
    {
        //当前任务详情
        $task_info = Db::name('task')->where('much_id', $much_id)->where('id', $task_id)->find();
        $time = '';
        switch ($task_info['task_cycle']) {
            case 0:
                $time = 'today';
                break;
            case 1:
                $time = 'week';
                break;
            case 2:
                $time = 'month';
                break;
            case 3:
                $time = 'year';
                break;
        }
        //当前任务数量
        $task_logger = Db::name('task_logger')->whereTime('complete_time', $time)->where('much_id', $much_id)->where('user_id', $user_id)->where('task_id', $task_id)->count();
        return $task_logger;
    }

    /**
     * 是否完成任务
     */
    public static function Finished($task_id, $user_id)
    {
        //当前任务详情
        $task_info = Db::name('task')->where('id', $task_id)->find();
        $paper_time = '';
        switch ($task_info['task_cycle']) {
            case 0:
                $paper_time = 'today';
                break;
            case 1:
                $paper_time = 'week';
                break;
            case 2:
                $paper_time = 'month';
                break;
            case 3:
                $paper_time = 'year';
                break;
        }
        //发布帖子
        if ($task_info['task_type'] == 0) {
            if ($task_info['tory_id'] != 0) {
                $paper = Db::name('paper')
                    ->where('much_id', $task_info['much_id'])
                    ->where('whether_type', 0)
                    ->where('user_id', $user_id)
                    ->where('tory_id', $task_info['tory_id'])
                    ->whereTime('prove_time', $paper_time)
                    ->count();
            } else {
                $paper = Db::name('paper')
                    ->where('much_id', $task_info['much_id'])
                    ->where('whether_type', 0)
                    ->where('user_id', $user_id)
                    ->whereTime('prove_time', $paper_time)
                    ->count();
            }
            return $paper;
        }
        //回复帖子
        if ($task_info['task_type'] == 1) {
            if ($task_info['tory_id'] != 0) {
                $paper_reply = Db::name('paper_reply')->alias('r')
                    ->join('paper p', 'p.id=r.paper_id')
                    ->where('p.tory_id', $task_info['tory_id'])
                    ->where('r.much_id', $task_info['much_id'])
                    ->where('r.reply_status', 1)
                    ->where('r.user_id', $user_id)
                    ->whereTime('r.prove_time', $paper_time)
                    ->count();
            } else {
                $paper_reply = Db::name('paper_reply')
                    ->where('much_id', $task_info['much_id'])
                    ->where('reply_status', 1)
                    ->where('user_id', $user_id)
                    ->whereTime('prove_time', $paper_time)
                    ->count();
            }

            return $paper_reply;
        }
        //帖子点赞
        if ($task_info['task_type'] == 2) {
            if ($task_info['tory_id'] != 0) {
                $paper_reply = Db::name('user_applaud')->alias('a')
                    ->join('paper p', 'p.id=a.paper_id')
                    ->where('p.tory_id', $task_info['tory_id'])
                    ->where('a.much_id', $task_info['much_id'])
                    ->where('a.applaud_type', 0)
                    ->where('a.user_id', $user_id)
                    ->whereTime('a.laud_time', $paper_time)
                    ->count();
            } else {
                $paper_reply = Db::name('user_applaud')
                    ->where('much_id', $task_info['much_id'])
                    ->where('applaud_type', 0)
                    ->where('user_id', $user_id)
                    ->whereTime('laud_time', $paper_time)
                    ->count();
            }

            return $paper_reply;
        }
        //帖子收藏
        if ($task_info['task_type'] == 3) {
            if ($task_info['tory_id'] != 0) {
                $paper_reply = Db::name('user_collect')->alias('a')
                    ->join('paper p', 'p.id=a.paper_id')
                    ->where('p.tory_id', $task_info['tory_id'])
                    ->where('a.much_id', $task_info['much_id'])
                    ->where('a.user_id', $user_id)
                    ->whereTime('a.create_time', $paper_time)
                    ->count();
            } else {
                $paper_reply = Db::name('user_collect')
                    ->where('much_id', $task_info['much_id'])
                    ->where('user_id', $user_id)
                    ->whereTime('create_time', $paper_time)
                    ->count();
            }
            return $paper_reply;
        }
        //帖子转发
        if ($task_info['task_type'] == 4) {
            if ($task_info['tory_id'] != 0) {
                $count = Db::name('user_forwarded')->alias('a')
                    ->join('paper p', 'p.id=a.paper_id')
                    ->where('p.tory_id', $task_info['tory_id'])
                    ->where('a.much_id', $task_info['much_id'])
                    ->where('a.user_id', $user_id)
                    ->whereTime('a.fulfill_time', $paper_time)
                    ->count();
            } else {
                $count = Db::name('user_forwarded')
                    ->where('much_id', $task_info['much_id'])
                    ->where('user_id', $user_id)
                    ->whereTime('fulfill_time', $paper_time)
                    ->count();
            }
            return $count;
        }
        //赠送礼物
        if ($task_info['task_type'] == 5) {
            $count = Db::name('user_subsidy')
                ->where('much_id', $task_info['much_id'])
                ->where('con_user_id', $user_id)
                ->whereTime('bute_time', $paper_time)
                ->count();
            return $count;
        }
        //充值贝壳
        if ($task_info['task_type'] == 6) {
            $count = Db::name('user_serial')
                ->where('much_id', $task_info['much_id'])
                ->where('user_id', $user_id)
                ->where('status ', 1)
                ->whereTime('add_time', $paper_time)
                ->count();
            return $count;
        }
        //关注用户
        if ($task_info['task_type'] == 7) {
            $count = Db::name('user_track')
                ->where('much_id', $task_info['much_id'])
                ->where('at_user_id', $user_id)
                ->whereTime('fo_time', $paper_time)
                ->count();
            return $count;
        }
        //购买商品
        if ($task_info['task_type'] == 8) {
            $count = Db::name('shop_order')
                ->where('much_id', $task_info['much_id'])
                ->where('status', 4)
                ->where('user_id', $user_id)
                ->whereTime('buy_time', $paper_time)
                ->count();
            return $count;
        }
        //邀请好友
        if ($task_info['task_type'] == 9) {
            $count = Db::name('user_respond_invitation')->alias('r')
                ->join('user_invitation_code u', 'u.code=r.re_code')
                ->where('u.user_id', $user_id)
                ->where('r.much_id', $task_info['much_id'])
                ->whereTime('r.re_time', $paper_time)
                ->count();
            return $count;
        }
        //每日签到
        if ($task_info['task_type'] == 10) {
            $count = Db::name('user_punch')
                ->where('much_id', $task_info['much_id'])
                ->where('user_id', $user_id)
                ->whereTime('punch_time', $paper_time)
                ->count();
            return $count;
        }
        //购买会员
        if ($task_info['task_type'] == 11) {
            $user_better_logger = Db::name('user_better_logger')
                ->where('much_id', $task_info['much_id'])
                ->where('user_id', $user_id)
                ->whereTime('buy_time', $paper_time)
                ->count();
            return $user_better_logger;
        }
        //看广告
        if ($task_info['task_type'] == 12) {
            $user_better_logger = Db::name('user_watch_ads')
                ->where('much_id', $task_info['much_id'])
                ->where('user_id', $user_id)
                ->where('ad_type', 0)
                ->where('fulfill_type', 0)
                ->whereTime('fulfill_time', $paper_time)
                ->count();
            return $user_better_logger;
        }
    }

}