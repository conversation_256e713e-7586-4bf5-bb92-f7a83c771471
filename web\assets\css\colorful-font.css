.yl_style1 {
    background: #000 -webkit-linear-gradient(left, #561214, #febaf7 50%, #ff0 90%, #561214) no-repeat 0 0;
    background-size: 20% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-decoration: underline;
    -webkit-animation: slideShine 3s linear infinite;
    animation: slideShine 3s linear infinite;
}

@-webkit-keyframes slideShine {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 100% 100%;
    }
}

@keyframes slideShine {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 100% 100%;
    }
}

/*
2
*/
.yl_style2 {
    padding: 1px;
    -webkit-transform-origin: center center;
    -ms-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation-name: shaky-slow;
    -ms-animation-name: shaky-slow;
    animation-name: shaky-slow;
    -webkit-animation-duration: 4s;
    -ms-animation-duration: 4s;
    animation-duration: 4s;
    -webkit-animation-iteration-count: infinite;
    -ms-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-timing-function: ease-in-out;
    -ms-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    -webkit-animation-delay: 0s;
    -ms-animation-delay: 0s;
    animation-delay: 0s;
    -webkit-animation-play-state: running;
    -ms-animation-play-state: running;
    animation-play-state: running;
}

@-webkit-keyframes shaky-slow {
    0% {
        -webkit-transform: translate(0px, 0px) rotate(0deg)
    }
    2% {
        -webkit-transform: translate(-1px, 1.5px) rotate(1.5deg)
    }
    4% {
        -webkit-transform: translate(1.3px, 0px) rotate(-0.5deg)
    }
    6% {
        -webkit-transform: translate(1.4px, 1.4px) rotate(-2deg)
    }
    8% {
        -webkit-transform: translate(-1.3px, -1px) rotate(-1.5deg)
    }
    10% {
        -webkit-transform: translate(1.4px, 0px) rotate(-2deg)
    }
    12% {
        -webkit-transform: translate(-1.3px, -1px) rotate(-2deg)
    }
    14% {
        -webkit-transform: translate(1.5px, 1.3px) rotate(1.5deg)
    }
    16% {
        -webkit-transform: translate(1.5px, -1.5px) rotate(-1.5deg)
    }
    18% {
        -webkit-transform: translate(1.3px, -1.3px) rotate(-2deg)
    }
    20% {
        -webkit-transform: translate(1px, 1px) rotate(-0.5deg)
    }
    22% {
        -webkit-transform: translate(1.3px, 1.5px) rotate(-2deg)
    }
    24% {
        -webkit-transform: translate(-1.4px, -1px) rotate(2deg)
    }
    26% {
        -webkit-transform: translate(1.3px, -1.3px) rotate(0.5deg)
    }
    28% {
        -webkit-transform: translate(1.6px, -1.6px) rotate(-2deg)
    }
    30% {
        -webkit-transform: translate(-1.3px, -1.3px) rotate(-1.5deg)
    }
    32% {
        -webkit-transform: translate(-1px, 0px) rotate(2deg)
    }
    34% {
        -webkit-transform: translate(1.3px, 1.3px) rotate(-0.5deg)
    }
    36% {
        -webkit-transform: translate(1.3px, 1.6px) rotate(1.5deg)
    }
    38% {
        -webkit-transform: translate(1.3px, -1.6px) rotate(1.5deg)
    }
    40% {
        -webkit-transform: translate(-1.4px, -1px) rotate(-0.5deg)
    }
    42% {
        -webkit-transform: translate(-1.4px, 1.3px) rotate(-0.5deg)
    }
    44% {
        -webkit-transform: translate(-1.6px, 1.4px) rotate(0.5deg)
    }
    46% {
        -webkit-transform: translate(-2.1px, -1.3px) rotate(-0.5deg)
    }
    48% {
        -webkit-transform: translate(1px, 1.6px) rotate(1.5deg)
    }
    50% {
        -webkit-transform: translate(1.6px, 1.6px) rotate(1.5deg)
    }
    52% {
        -webkit-transform: translate(-1.4px, 1.6px) rotate(0.5deg)
    }
    54% {
        -webkit-transform: translate(1.6px, -1px) rotate(-2deg)
    }
    56% {
        -webkit-transform: translate(1.3px, -1.6px) rotate(-2deg)
    }
    58% {
        -webkit-transform: translate(-1.3px, -1.6px) rotate(0.5deg)
    }
    60% {
        -webkit-transform: translate(1.3px, 1.6px) rotate(-0.5deg)
    }
    62% {
        -webkit-transform: translate(0px, 0px) rotate(-1.5deg)
    }
    64% {
        -webkit-transform: translate(-1.6px, -1.6px) rotate(-2deg)
    }
    66% {
        -webkit-transform: translate(1.6px, -1.6px) rotate(0.5deg)
    }
    68% {
        -webkit-transform: translate(0px, -1.6px) rotate(-2deg)
    }
    70% {
        -webkit-transform: translate(-1.6px, 1px) rotate(1.5deg)
    }
    72% {
        -webkit-transform: translate(-1.6px, 1.6px) rotate(2deg)
    }
    74% {
        -webkit-transform: translate(1.3px, -1.6px) rotate(-0.5deg)
    }
    76% {
        -webkit-transform: translate(1.4px, 1px) rotate(-0.5deg)
    }
    78% {
        -webkit-transform: translate(-1px, 1.4px) rotate(2deg)
    }
    80% {
        -webkit-transform: translate(1.4px, 1.6px) rotate(2deg)
    }
    82% {
        -webkit-transform: translate(-1.6px, -1.6px) rotate(-0.5deg)
    }
    84% {
        -webkit-transform: translate(-1.4px, 1.4px) rotate(-2deg)
    }
    86% {
        -webkit-transform: translate(1px, 1.4px) rotate(-2deg)
    }
    88% {
        -webkit-transform: translate(-1.4px, 1.4px) rotate(-1.5deg)
    }
    90% {
        -webkit-transform: translate(-1.6px, -1.6px) rotate(-2deg)
    }
    92% {
        -webkit-transform: translate(-1.6px, 1.6px) rotate(2deg)
    }
    94% {
        -webkit-transform: translate(-1.6px, -1.6px) rotate(-2deg)
    }
    96% {
        -webkit-transform: translate(-1.4px, 1.3px) rotate(-2deg)
    }
    98% {
        -webkit-transform: translate(1.3px, 1px) rotate(-0.5deg)
    }
}

@keyframes shaky-slow {
    0% {
        transform: translate(0px, 0px) rotate(0deg)
    }
    2% {
        transform: translate(-1px, 1.5px) rotate(1.5deg)
    }
    4% {
        transform: translate(1.3px, 0px) rotate(-0.5deg)
    }
    6% {
        transform: translate(1.4px, 1.4px) rotate(-2deg)
    }
    8% {
        transform: translate(-1.3px, -1px) rotate(-1.5deg)
    }
    10% {
        transform: translate(1.4px, 0px) rotate(-2deg)
    }
    12% {
        transform: translate(-1.3px, -1px) rotate(-2deg)
    }
    14% {
        transform: translate(1.5px, 1.3px) rotate(1.5deg)
    }
    16% {
        transform: translate(1.5px, -1.5px) rotate(-1.5deg)
    }
    18% {
        transform: translate(1.3px, -1.3px) rotate(-2deg)
    }
    20% {
        transform: translate(1px, 1px) rotate(-0.5deg)
    }
    22% {
        transform: translate(1.3px, 1.5px) rotate(-2deg)
    }
    24% {
        transform: translate(-1.4px, -1px) rotate(2deg)
    }
    26% {
        transform: translate(1.3px, -1.3px) rotate(0.5deg)
    }
    28% {
        transform: translate(1.6px, -1.6px) rotate(-1.5deg)
    }
    30% {
        transform: translate(-1.3px, -1.3px) rotate(-1.5deg)
    }
    32% {
        transform: translate(-1px, 0px) rotate(2deg)
    }
    34% {
        transform: translate(1.3px, 1.3px) rotate(-0.5deg)
    }
    36% {
        transform: translate(1.3px, 1.6px) rotate(1.5deg)
    }
    38% {
        transform: translate(1.3px, -1.6px) rotate(1.5deg)
    }
    40% {
        transform: translate(-1.4px, -1px) rotate(-0.5deg)
    }
    42% {
        transform: translate(-1.4px, 1.3px) rotate(-0.5deg)
    }
    44% {
        transform: translate(-1.6px, 1.4px) rotate(0.5deg)
    }
    46% {
        transform: translate(-2.1px, -1.3px) rotate(-0.5deg)
    }
    48% {
        transform: translate(1px, 1.6px) rotate(1.5deg)
    }
    50% {
        transform: translate(1.6px, 1.6px) rotate(1.5deg)
    }
    52% {
        transform: translate(-1.4px, 1.6px) rotate(0.5deg)
    }
    54% {
        transform: translate(1.6px, -1px) rotate(-2deg)
    }
    56% {
        transform: translate(1.3px, -1.6px) rotate(-2deg)
    }
    58% {
        transform: translate(-1.3px, -1.6px) rotate(0.5deg)
    }
    60% {
        transform: translate(1.3px, 1.6px) rotate(-0.5deg)
    }
    62% {
        transform: translate(0px, 0px) rotate(-1.5deg)
    }
    64% {
        transform: translate(-1.6px, -1.6px) rotate(-2deg)
    }
    66% {
        transform: translate(1.6px, -1.6px) rotate(0.5deg)
    }
    68% {
        transform: translate(0px, -1.6px) rotate(-2deg)
    }
    70% {
        transform: translate(-1.6px, 1px) rotate(1.5deg)
    }
    72% {
        transform: translate(-1.6px, 1.6px) rotate(2deg)
    }
    74% {
        transform: translate(1.3px, -1.6px) rotate(-0.5deg)
    }
    76% {
        transform: translate(1.4px, 1px) rotate(-0.5deg)
    }
    78% {
        transform: translate(-1px, 1.4px) rotate(2deg)
    }
    80% {
        transform: translate(1.4px, 1.6px) rotate(2deg)
    }
    82% {
        transform: translate(-1.6px, -1.6px) rotate(-0.5deg)
    }
    84% {
        transform: translate(-1.4px, 1.4px) rotate(-2deg)
    }
    86% {
        transform: translate(1px, 1.4px) rotate(-2deg)
    }
    88% {
        transform: translate(-1.4px, 1.4px) rotate(-1.5deg)
    }
    90% {
        transform: translate(-1.6px, -1.6px) rotate(-2deg)
    }
    92% {
        transform: translate(-1.4px, 1.6px) rotate(2deg)
    }
    94% {
        transform: translate(-1.6px, -1.6px) rotate(-2deg)
    }
    96% {
        transform: translate(-1.4px, 1.3px) rotate(-2deg)
    }
    98% {
        transform: translate(1.3px, 1px) rotate(-0.5deg)
    }
}

.yl_style3 {
    text-shadow: -2px 0 rgba(0, 255, 255, .5), 2px 0 rgba(255, 0, 0, .5);
    animation: shake-it .7s reverse infinite cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

@keyframes shake-it {
    0% {
        text-shadow: 0 0 rgba(0, 255, 255, .5), 0 0 rgba(255, 0, 0, .5)
    }

    25% {
        text-shadow: -2px 0 rgba(0, 255, 255, .5), 2px 0 rgba(255, 0, 0, .5)
    }

    50% {
        text-shadow: -5px 0 rgba(0, 255, 255, .5), 3px 0 rgba(255, 0, 0, .5)
    }

    100% {
        text-shadow: 3px 0 rgba(0, 255, 255, .5), 5px 0 rgba(255, 0, 0, .5)
    }
}

.yl_style4 {
    color: #fff6a9;
    animation: blink 10s infinite;
    -webkit-animation: blink 10s infinite;
}

@keyframes blink {
    20%,
    24%,
    55% {
        color: #fff;
        text-shadow: none;
    }
    0%,
    19%,
    21%,
    23%,
    25%,
    54%,
    56%,
    100% {
        text-shadow: 0 0 5px #ffa500, 0 0 5px #ffa500, 0 0 10px #ffa500, 0 0 1px #ffa500, 0 0 1px #ff0000, 0 0 10px #ff8d00, 0 0 5px #ff0000;
        color: #fff6a9;
    }
}


.yl_style5 {
    color: #000000;
    text-shadow: 0 0 0.15em #ffffff;
    user-select: none;
    white-space: nowrap;
    filter: blur(0.007em);
    animation: cheaaru 2.5s linear infinite;
}


@keyframes cheaaru {
    5%, 15%, 25%, 35%, 55%, 65%, 75%, 95% {
        filter: blur(0.018em);
        transform: translateY(0.018em) rotate(0deg);
    }

    10%, 30%, 40%, 50%, 70%, 80%, 90% {
        filter: blur(0.07em);
        transform: translateY(-0.018em) rotate(0deg);
    }

    20%, 60% {
        filter: blur(0.02em);
        transform: translate(-0.018em, 0.018em) rotate(0deg);
    }

    45%, 85% {
        filter: blur(0.03em);
        transform: translate(0.018em, -0.018em) rotate(0deg);
    }

    100% {
        filter: blur(0.018em);
        transform: translate(0) rotate(-0.5deg);
    }
}

@keyframes crack1 {
    0%, 95% {
        transform: translate(-50%, -50%);
    }

    100% {
        transform: translate(-51%, -48%);
    }
}

@keyframes crack2 {
    0%, 95% {
        transform: translate(-50%, -50%);
    }

    100% {
        transform: translate(-49%, -53%);
    }
}


.yl_style6 {
    background-image: -webkit-linear-gradient(left,
    #00f2fe,
    #fadbd9 10%,
    #fa709a 20%,
    #fa709a 30%,
    #cce6ff 40%,
    #00f2fe 50%,
    #cce6ff 60%,
    #fa709a 70%,
    #e54d42 80%,
    #fadbd9 90%,
    #00f2fe);
    -webkit-text-fill-color: transparent;
    -webkit-background-clip: text;
    -webkit-background-size: 200% 100%;
    -webkit-animation: masked-animation 4s infinite linear;
}

@keyframes masked-animation {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: -100% 0;
    }
}

.yl_style7 {
    margin: 0;
    padding: 0;
    color: white;
    display: inline-block;
    font-size: 15px;
    text-shadow: 0 0 1px #fff, 0 0 2px #fff, 0 0 3px #e60073, 0 0 4px #e60073, 0 0 5px #e60073, 0 0 6px #e60073, 0 0 7px #e60073;
}

.yl_style7:nth-child(0n + 0) {
    -webkit-animation-name: animateGlithcy;
    animation-name: animateGlithcy;
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
    -webkit-animation-duration: 0s;
    animation-duration: 0s;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    animation-direction: alternate-reverse;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

.yl_style7:nth-child(0n + 1) {
    -webkit-animation-name: animateGlithcy;
    animation-name: animateGlithcy;
    -webkit-animation-delay: 0.2s;
    animation-delay: 0.2s;
    -webkit-animation-duration: 0.5s;
    animation-duration: 0.5s;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    animation-direction: alternate-reverse;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

@-webkit-keyframes animateGlithcy {
    0% {
        opacity: .1;
        background-position: 0 0;
        -webkit-filter: hue-rotate(0deg);
        filter: hue-rotate(0deg);
    }
    10% {
        background-position: 5px 0;
    }
    20% {
        background-position: -5px 0;
    }
    30% {
        background-position: 15px 0;
    }
    40% {
        background-position: -5px 0;
    }
    50% {
        background-position: -25px 0;
    }
    60% {
        background-position: -50px 0;
    }
    70% {
        background-position: 0 -20px;
    }
    80% {
        background-position: -60px -20px;
    }
    81% {
        background-position: 0 0;
    }
    100% {
        opacity: 1;
        background-position: 0 0;
        -webkit-filter: hue-rotate(360deg);
        filter: hue-rotate(360deg);
    }
}

@keyframes animateGlithcy {
    0% {
        opacity: .1;
        background-position: 0 0;
        -webkit-filter: hue-rotate(0deg);
        filter: hue-rotate(0deg);
    }
    10% {
        background-position: 5px 0;
    }
    20% {
        background-position: -5px 0;
    }
    30% {
        background-position: 15px 0;
    }
    40% {
        background-position: -5px 0;
    }
    50% {
        background-position: -25px 0;
    }
    60% {
        background-position: -50px 0;
    }
    70% {
        background-position: 0 -20px;
    }
    80% {
        background-position: -60px -20px;
    }
    81% {
        background-position: 0 0;
    }
    100% {
        opacity: 1;
        background-position: 0 0;
        -webkit-filter: hue-rotate(360deg);
        filter: hue-rotate(360deg);
    }
}

.yl_style8 {
    padding: 0 5px;
    position: relative;
    color: #000000;
    background-color: #FFFFFF;
}

.yl_style8:after {
    content: "\00A0";
    position: absolute;
    top: 0;
    right: 0;
    background: #FFFFFF;
    width: 100%;
    border-left: .1em solid transparent;
    -webkit-animation: typing 3s steps(30) infinite,
    cursor 1s infinite;
    animation: typing 3s steps(30) infinite,
    cursor 1s infinite;
}

@-webkit-keyframes typing {
    0% {
        width: 100%;
    }
    30% {
        width: 0%;
    }
    99% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

@keyframes typing {
    0% {
        width: 100%;
    }
    30% {
        width: 0%;
    }
    99% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

@-webkit-keyframes cursor {
    50% {
        border-color: #000000;
    }
}

@keyframes cursor {
    50% {
        border-color: #000000;
    }
}