{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-comment-o {margin-right: 5px;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}

    .batch-actions-group > .am-btn { border-radius: 3px;}
    .batch-actions-group > .am-btn + .am-btn { margin-left: 8px; }
    
    .action-btn {display: inline-block; padding: 4px 10px; background: #fff; border: 1px solid #ddd; color: #666; border-radius: 3px; font-size: 12px; cursor: pointer; transition: all 0.3s;}
    .action-btn:hover {border-color: #23b7e5; color: #23b7e5; background-color: #f5fafd;}
    
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-top: 15px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .am-table a { color: #23b7e5; }

    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a, .am-pagination > .am-active > a:hover {background-color: #23b7e5;border-color: #23b7e5;color:#fff;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-comment-o"></span> 小秘密回复列表
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" class="form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索小秘密回复或用户...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12" style="text-align: right;">
                {if $egon!=1}
                <div class="am-btn-group am-btn-group-sm batch-actions-group">
                    <button type="button" class="am-btn am-btn-success" onclick="rebatch('1');">批量通过</button>
                    <button type="button" class="am-btn am-btn-secondary" onclick="rebatch('2');">批量拒绝</button>
                    <button type="button" class="am-btn am-btn-danger" onclick="rebatch('3');">批量删除</button>
                </div>
                {/if}
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            {if $egon!=1}
                            <th width="5%">
                                <input id="withole" type="checkbox" class="tpl-table-fz-check">全选
                            </th>
                            {/if}
                            <th width="15%">用户昵称</th>
                            <th width="25%">回复的小秘密</th>
                            <th width="20%">小秘密内容</th>
                            <th width="15%">回复时间</th>
                            <th width="10%">审核状态</th>
                            <th width="10%">内容详情</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            {if $egon!=1}
                            <td>
                                <input type="checkbox" class="tpl-table-fz-check elctive" value="{$vo.id}">
                            </td>
                            {/if}
                            <td>
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td>
                                {$vo.re_content|emoji_decode|strip_tags|subtext=10|$expressionHtml}
                            </td>
                            <td>
                                {$vo.content|emoji_decode|strip_tags|subtext=6|$expressionHtml}
                            </td>
                            <td>
                                {:date('Y-m-d H:i:s',$vo.reply_time)}
                            </td>
                            <td>
                                {if $vo.status == 0}
                                <span class="am-text-warning">待审核</span>
                                {elseif $vo.status == 1}
                                <span class="am-text-success">已通过</span>
                                {elseif $vo.status == 2}
                                <span class="am-text-danger">未通过</span>
                                {/if}
                            </td>
                            <td>
                                <button type="button" class="action-btn" onclick="uploof('{$vo.id}');">
                                    <span class="am-icon-search"></span> 查看
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    !function () {
        $('#withole').click(function () {
            var wecked = $(this).prop('checked');
            if (wecked) {
                $('.elctive').prop('checked', true);
            } else {
                $('.elctive').prop('checked', false);
            }
        });
        $('.elctive').click(function () {
            var jack = 1;
            $('.elctive').each(function () {
                var weck = $(this).prop('checked');
                if (weck == false) {
                    jack = 0;
                    return false;
                }
            });
            if (jack) {
                $('#withole').prop('checked', true);
            } else {
                $('#withole').prop('checked', false);
            }
        });
    }();

    var rebatch = function (guid) {
        var tired = false;
        $('.elctive').each(function () {
            if ($(this).prop('checked')) {
                tired = true;
                return false;
            }
        });
        if (!tired) {
            layer.alert('很抱歉，当前没有选中要操作的数据', {'title': '系统提示'});
            return;
        }
        var reason = '';
        switch (parseInt(guid)) {
            case 1:
                layer.confirm('您确定要通过已选中的小秘密回复吗？', {
                    btn: ['确定', '取消'], title: '提示'
                }, function (index) {
                    setrike(guid, reason, index);
                }, function (index) {
                    layer.close(index);
                });
                break;
            case 2:
                layer.prompt({'title': '请您输入拒绝原因：'}, function (rea_value, index) {
                    if ($.trim(rea_value) === '') {
                        return false;
                    }
                    reason = rea_value;
                    setrike(guid, reason, index);
                });
                break;
            case 3:
                layer.confirm('您确定要删除已选中的小秘密回复吗？', {
                    btn: ['确定', '取消'], title: '提示'
                }, function (index) {
                    setrike(guid, reason, index);
                }, function (index) {
                    layer.close(index);
                });
                break;
        }
    }

    var setrike = function (guid, reason,index) {
        layer.close(index);
        var i = 0;
        var j = 0;
        $('.elctive').each(function () {
            if ($(this).prop('checked')) {
                var suid = $(this).val();
                i++;
                switch (parseInt(guid)) {
                    case 1:
                    case 2:
                        $.ajaxSettings.async = false;
                        $.post("{:url('stealth/politics')}", {'sid': suid, 'code': guid, 'caption': reason}, function (data) {
                            if (data.code > 0) {
                                j++;
                            }
                        });
                        $.ajaxSettings.async = true;
                        break;
                    case 3:
                        $.ajaxSettings.async = false;
                        $.post("{:url('stealth/delJanitor')}", {'sid': suid}, function (data) {
                            if (data.code > 0) {
                                j++;
                            }
                        });
                        $.ajaxSettings.async = true;
                        break;
                }
            }
        });
        if (i === j) {
            layer.msg('操作成功', {icon: 1, time: 1000}, function () {
                location.reload();
            });
        } else {
            layer.msg('未知错误', {icon: 5, time: 2000}, function () {
                location.reload();
            });
        }
    }


    var uploof = function (uplid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('stealth/opaque')}&uplid=" + uplid);
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('stealth/janitor')}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('stealth/janitor')}&page={$page}";
        }
    }

</script>
{/block}