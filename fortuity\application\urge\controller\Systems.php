<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\common\Playful;
use think\Cache;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

#系统管理
class Systems extends Base
{

    //  远程附件数据加密
    public static function annexOverlapping($quickType, $type, $muchId)
    {
        // 从缓存中获取指定类型和 muchId 的离lying数据
        $systemOutlying = Cache::get("outlying_{$type}_{$muchId}");
        // 如果缓存中没有数据
        if (!$systemOutlying) {
            // 初始化一个空数组
            $systemOutlying = [];
            // 根据类型从不同的数据表中查找数据
            switch ($type) {
                // 如果类型为 0
                case 0:
                    // 从 outlying 数据表查找
                    $systemOutlying = Db::name('outlying')->where('much_id', $muchId)->find();
                    // 结束 switch
                    break;
                // 如果类型为 1
                case 1:
                    // 从 netdisc_storage 数据表查找
                    $systemOutlying = Db::name('netdisc_storage')->where('much_id', $muchId)->find();
                    // 结束 switch
                    break;
            }
            // 解析获取到的（或空的）数据
            $systemOutlying = self::praise($systemOutlying);
            // 将解析后的数据存入缓存，有效期 7200 秒
            Cache::set("outlying_{$type}_{$muchId}", $systemOutlying, 7200);
        }
        // 定义不同附件类型的配置
        $config = [
            // 阿里云 OSS 配置
            1 => [
                'followKey' => 'oss_follow', // 存储键名
                'fields' => ['oss_access_key_id', 'oss_access_key_secret', 'oss_bucket', 'oss_endpoint', 'oss_url'], // 字段列表
            ],
            // 七牛云 Kodo 配置
            2 => [
                'followKey' => 'qiniu_follow', // 存储键名
                'fields' => ['qiniu_access_key', 'qiniu_secret_key', 'qiniu_bucket', 'qiniu_url'], // 字段列表
                'special' => [ // 特殊处理字段
                    'qiniu_watermark' => function () { // 处理水印字段的匿名函数
                        $watermark = request()->post('qiniu_watermark', ''); // 从 POST 请求获取水印值
                        return $watermark == '' ? '' : authcode($watermark, 'ENCODE', 'YuluoNetwork'); // 如果水印值不为空则加密，否则返回空字符串
                    }
                ]
            ],
            // 腾讯云 COS 配置
            3 => [
                'followKey' => 'cos_follow', // 存储键名
                'fields' => ['cos_app_id', 'cos_secret_id', 'cos_secret_key', 'cos_bucket', 'cos_region', 'cos_url'], // 字段列表
            ],
            // 又拍云 Upyun 配置
            4 => [
                'followKey' => 'upyun_follow', // 存储键名
                'fields' => ['upyun_service_name', 'upyun_operator_name', 'upyun_operator_password', 'upyun_url'], // 字段列表
            ],
            // FTP 配置
            5 => [
                'followKey' => 'ftp_follow', // 存储键名
                'fields' => ['ftp_host', 'ftp_username', 'ftp_password', 'ftp_port', 'ftp_url'], // 字段列表
                'special' => [ // 特殊处理字段
                    'ftp_pasv' => function () { // 处理 FTP 被动模式字段的匿名函数
                        $ftpPasv = intval(request()->post('ftp_pasv', '')) ?: 0; // 从 POST 请求获取并转换为整数
                        return authcode($ftpPasv, 'ENCODE', 'YuluoNetwork'); // 加密 FTP 被动模式的值
                    }
                ]
            ]
        ];
        // 初始化快速数据数组
        $quickData = [];
        // 检查当前附件类型是否存在于配置中
        if (isset($config[$quickType])) {
            // 获取当前类型的配置
            $typeConfig = $config[$quickType];
            // 初始化用于存储加密数据的数组
            $data = [];
            // 获取当前类型的存储键名
            $followKey = $typeConfig['followKey'];
            // 遍历标准字段列表
            foreach ($typeConfig['fields'] as $field) {
                // 从 POST 请求获取字段值，如果不存在则从系统原有数据中获取
                $value = request()->post($field, '') ?: (isset($systemOutlying[$followKey][$field]) ? $systemOutlying[$followKey][$field] : '');
                // 加密字段值并存入数据数组
                $data[$field] = authcode($value, 'ENCODE', 'YuluoNetwork');
            }
            // 检查是否存在需要特殊处理的字段
            if (isset($typeConfig['special'])) {
                // 遍历特殊处理字段配置
                foreach ($typeConfig['special'] as $field => $handler) {
                    // 调用对应的匿名函数处理并存入数据数组
                    $data[$field] = $handler();
                }
            }
            // 将加密后的数据数组转换为 JSON 字符串并存入快速数据数组
            $quickData[$followKey] = json_encode($data, 320);
        }
        // 将附件类型存入快速数据数组
        $quickData['quicken_type'] = $quickType;
        // 返回包含最终数据的数组
        return $quickData;
    }

    //  远程附件
    public function annex()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 从 POST 请求中获取 quicken_type 参数，并转换为整数，默认为 0
            $quickType = intval(request()->post('quicken_type', 0));
            // 调用 annexOverlapping 方法对附件数据进行加密处理
            $quickData = self::annexOverlapping($quickType, 0, $this->much_id);
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 outlying 表中对应 much_id 的数据，并清除相关缓存
                Db::name('outlying')->where('much_id', $this->much_id)->cache("outlying_0_{$this->much_id}")->update($quickData);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        // 如果不是 POST 或 AJAX 请求
        } else {
            //  从缓存中获取远程附件信息
            $systemOutlying = Cache::get("outlying_0_{$this->much_id}");
            //  如果缓存信息不存在
            if (!$systemOutlying) {
                //  从数据库中查询远程附件信息
                $systemOutlying = Db::name('outlying')->where('much_id', $this->much_id)->find();
                //  如果数据库信息也不存在
                if (!$systemOutlying) {
                    //  准备一条新的默认信息
                    $systemOutlying = ['quicken_type' => 0, 'much_id' => $this->much_id];
                    //  启动数据库事务
                    Db::startTrans();
                    // 尝试执行数据库插入操作
                    try {
                        //  在 outlying 表中插入新信息，并获取自增 ID
                        $systemOutlying['id'] = Db::name('outlying')->insertGetId($systemOutlying);
                        //  提交事务
                        Db::commit();
                    // 捕获可能发生的异常
                    } catch (\Exception $e) {
                        //  回滚事务
                        Db::rollback();
                        //  以 JSON 格式返回错误信息
                        return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                    }
                }
                //  调用 praise 方法解析（解密）数据
                $systemOutlying = self::praise($systemOutlying);
                //  缓存数据
                Cache::set("outlying_0_{$this->much_id}", $systemOutlying, 7200);
            }
            // 从缓存中获取 OSS 节点信息
            $ossEndpoint = Cache::get('ossEndpoint');
            // 如果缓存中不存在
            if (!$ossEndpoint) {
                // 从数据库查询并缓存 OSS 节点信息
                $ossEndpoint = Db::name('outlying_allude')->where('status', 1)->where('type', 0)->cache('ossEndpoint')->select();
            }
            // 从缓存中获取 OSS 区域信息
            $ossRegion = Cache::get('ossRegion');
            // 如果缓存中不存在
            if (!$ossRegion) {
                // 从数据库查询并缓存 OSS 区域信息
                $ossRegion = Db::name('outlying_allude')->where('status', 1)->where('type', 1)->cache('ossRegion')->select();
            }
            // 将远程附件信息分配到视图
            $this->assign('list', $systemOutlying);
            // 将 OSS 节点信息分配到视图
            $this->assign('ossEndpoint', $ossEndpoint);
            // 将 OSS 区域信息分配到视图
            $this->assign('ossRegion', $ossRegion);
            // 渲染并返回视图
            return $this->fetch();
        }
    }

    //  远程附件信息查询
    public static function praise($systemOutlying)
    {
        // 检查传入的数据是否是一个数组，如果不是则初始化为空数组以防止错误
        if (!is_array($systemOutlying)) {
            // 初始化为空数组
            $systemOutlying = [];
        }
        // 定义需要处理的附件信息在数据库中的所有键名
        $follow_keys = ['oss_follow', 'qiniu_follow', 'cos_follow', 'upyun_follow', 'ftp_follow'];
        // 遍历所有定义的键名
        foreach ($follow_keys as $follow_key) {
            // 从传入数据中获取对应键名的值，如果不存在则为 null
            $json_string = isset($systemOutlying[$follow_key]) ? $systemOutlying[$follow_key] : null;
            // 检查获取到的值是否为非空字符串
            if (!empty($json_string) && is_string($json_string)) {
                // 将 JSON 字符串解码为 PHP 关联数组
                $decoded_data = json_decode($json_string, true);
                // 检查解码后的数据是否是一个数组
                if (is_array($decoded_data)) {
                    // 遍历解码后的数组中的每一个键值对
                    foreach ($decoded_data as $key => $value) {
                        // 使用 authcode 方法对值进行解密
                        $decoded_data[$key] = authcode($value, 'DECODE', 'YuluoNetwork');
                    }
                    // 将解密后的数组重新赋值给 systemOutlying 中对应的键
                    $systemOutlying[$follow_key] = $decoded_data;
                } else {
                    // 将该键的值设置为空数组，以确保数据结构一致性
                    $systemOutlying[$follow_key] = [];
                }
            } else {
                // 同样将该键的值设置为空数组，以确保后续代码可以安全访问
                $systemOutlying[$follow_key] = [];
            }
        }
        // 返回处理（解密和结构化）后的完整数据
        return $systemOutlying;
    }

    //  站点设置
    public function warrior()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 从 POST 请求中获取 id
            $suid = request()->post('id');
            // 从 POST 请求中获取标题
            $data['title'] = request()->post('title');
            // 从 POST 请求中获取分享图
            $data['sgraph'] = request()->post('sngimg');
            // 从 POST 请求中获取客服电话
            $data['cust_phone'] = request()->post('phone');
            // 从 POST 请求中获取版权信息
            $data['copyright'] = request()->post('copyright');
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 authority 表中对应 id 和 much_id 的数据，并清除相关缓存
                Db::name('authority')->where('id', $suid)->where('much_id', $this->much_id)->cache('knight_' . $this->much_id)->update($data);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        // 渲染并返回视图
        return $this->fetch();
    }

    //  疑难解答
    public function help()
    {
        // 调用 defaultQuery 方法获取默认的 URL 查询参数
        $url = $this->defaultQuery();
        // 从 GET 请求中获取 hazy_name 参数，用于模糊搜索，默认为空字符串
        $hazy_name = request()->get('hazy_name', '');
        // 从 help 表中查询数据
        $list = Db::name('help')
            // 设置查询条件：trouble 字段模糊匹配 hazy_name
            ->where('trouble', 'like', "%{$hazy_name}%")
            // 设置查询条件：much_id 等于当前 much_id
            ->where('much_id', $this->much_id)
            // 按 scores 字段升序排序
            ->order('scores', 'asc')
            // 进行分页查询，每页 10 条，并传入分页参数
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        // 将查询结果列表分配到视图
        $this->assign('list', $list);
        // 将模糊搜索关键词分配到视图
        $this->assign('hazy_name', $hazy_name);
        // 从 GET 请求中获取当前页码，默认为 1
        $page = request()->get('page', 1);
        // 将当前页码分配到视图
        $this->assign('page', $page);
        // 渲染并返回视图
        return $this->fetch();
    }

    //  疑难解答排序
    public function shelp()
    {
        // 从 POST 请求中获取 ID
        $suid = request()->post('asyId');
        // 从 POST 请求中获取排序值
        $dalue = request()->post('dalue');
        // 启动数据库事务
        Db::startTrans();
        // 尝试执行数据库操作
        try {
            // 更新 help 表中对应 id 和 much_id 的记录的 scores 字段
            Db::name('help')->where('id', $suid)->where('much_id', $this->much_id)->update(['scores' => $dalue]);
            // 提交事务
            Db::commit();
        // 捕获可能发生的异常
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            // 以 JSON 格式返回错误信息
            return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
        }
        // 以 JSON 格式返回成功信息
        return json(['code' => 1, 'msg' => '保存成功']);
    }

    //  新增疑难解答
    public function ruhelp()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 获取所有 POST 数据
            $data = request()->post();
            // 设置当前时间戳
            $data['time'] = time();
            // 设置 much_id
            $data['much_id'] = $this->much_id;
            // 尝试执行数据库操作
            try {
                // 向 help 表中插入新数据
                Db::name('help')->insert($data);
                // 提交事务（此处应在 startTrans 之后使用）
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务（此处应在 startTrans 之后使用）
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        // 渲染并返回视图
        return $this->fetch();
    }

    //  编辑疑难解答
    public function uphelp()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 获取所有 POST 数据
            $data = request()->post();
            // 获取记录的 ID
            $usid = $data['usid'];
            // 从数据数组中移除 ID，防止被更新
            unset($data['usid']);
            // 设置当前时间戳
            $data['time'] = time();
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 help 表中对应 id 和 much_id 的记录
                Db::name('help')->where('id', $usid)->where('much_id', $this->much_id)->update($data);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        // 从 GET 请求中获取要编辑的记录的 ID
        $uplid = request()->get('uplid', '');
        // 如果 ID 存在
        if ($uplid) {
            // 从 help 表中查询对应 id 和 much_id 的记录
            $helpList = Db::name('help')->where('id', $uplid)->where('much_id', $this->much_id)->find();
            // 如果记录存在
            if ($helpList) {
                // 将查询到的数据分配到视图
                $this->assign('list', $helpList);
                // 渲染并返回编辑视图
                return $this->fetch();
            // 如果记录不存在
            } else {
                // 重定向到疑难解答列表页面
                $this->redirect('systems/help');
            }
        // 如果 ID 不存在
        } else {
            // 重定向到疑难解答列表页面
            $this->redirect('systems/help');
        }
    }

    //  删除疑难解答
    public function helplint()
    {
        // 从 POST 请求中获取要删除的记录的 ID
        $usid = request()->post('ecid');
        // 启动数据库事务
        Db::startTrans();
        // 尝试执行数据库操作
        try {
            // 从 help 表中删除对应 id 和 much_id 的记录
            Db::name('help')->where('id', $usid)->where('much_id', $this->much_id)->delete();
            // 提交事务
            Db::commit();
        // 捕获可能发生的异常
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            // 以 JSON 格式返回错误信息
            return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
        }
        // 以 JSON 格式返回成功信息
        return json(['code' => 1, 'msg' => '删除成功']);
    }


    //  小程序设置
    public function applets()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 调用 wiper 方法获取当前的小程序设置
            $getWiper = $this->wiper();
            // 从 POST 请求中获取记录 ID
            $usid = request()->post('usid');
            // 获取小程序名称，如果 POST 中没有则使用现有值
            $appName = request()->post('appName', '') ?: $getWiper['app_name'];
            // 获取 AppId，如果 POST 中没有则使用现有值
            $appId = request()->post('appId', '') ?: $getWiper['app_id'];
            // 获取 AppSecret，如果 POST 中没有则使用现有值
            $appSecret = request()->post('appSecret', '') ?: $getWiper['app_secret'];
            // 获取微信支付商户号，如果 POST 中没有则使用现有值
            $appMchid = request()->post('appMchid', '') ?: $getWiper['app_mchid'];
            // 获取微信支付 API 密钥，如果 POST 中没有则使用现有值
            $appKey = request()->post('appKey', '') ?: $getWiper['app_key'];
            // 获取微信支付 API v3 密钥，如果 POST 中没有则使用现有值
            $appKeyV3 = request()->post('appKeyV3', '') ?: $getWiper['app_key_v3'];
            // 获取证书序列号，如果 POST 中没有则使用现有值
            $certificateSerialNumber = request()->post('certificateSerial', '') ?: $getWiper['certificate_serial_number'];
            // 获取 apiclient_cert 证书内容，如果 POST 中没有则使用现有值
            $apiclientCert = request()->post('apiclientCert', '') ?: $getWiper['apiclient_cert'];
            // 获取 apiclient_key 密钥内容，如果 POST 中没有则使用现有值
            $apiclientKey = request()->post('apiclientKey', '') ?: $getWiper['apiclient_key'];
            // 如果小程序名称不为空，则加密后存入 $data 数组
            $data['app_name'] = $appName ? authcode($appName, 'ENCODE', 'YuluoNetwork') : null;
            // 如果 AppId 不为空，则加密后存入 $data 数组
            $data['app_id'] = $appId ? authcode($appId, 'ENCODE', 'YuluoNetwork') : null;
            // 如果 AppSecret 不为空，则加密后存入 $data 数组
            $data['app_secret'] = $appSecret ? authcode($appSecret, 'ENCODE', 'YuluoNetwork') : null;
            // 如果微信支付商户号不为空，则加密后存入 $data 数组
            $data['app_mchid'] = $appMchid ? authcode($appMchid, 'ENCODE', 'YuluoNetwork') : null;
            // 如果微信支付 API 密钥不为空，则加密后存入 $data 数组
            $data['app_key'] = $appKey ? authcode($appKey, 'ENCODE', 'YuluoNetwork') : null;
            // 如果微信支付 API v3 密钥不为空，则加密后存入 $data 数组
            $data['app_key_v3'] = $appKeyV3 ? authcode($appKeyV3, 'ENCODE', 'YuluoNetwork') : null;
            // 从 POST 请求中获取支付回调地址
            $data['pay_react'] = request()->post('payReact', '');
            // 如果证书序列号不为空，则加密后存入 $data 数组
            $data['certificate_serial_number'] = $certificateSerialNumber ? authcode($certificateSerialNumber, 'ENCODE', 'YuluoNetwork') : null;
            // 从 POST 请求中获取版本类型
            $data['version_type'] = request()->post('versionType', 0);
            // 如果 apiclient_cert 证书内容不为空，则加密后存入 $data 数组
            $data['apiclient_cert'] = $apiclientCert ? authcode($apiclientCert, 'ENCODE', 'YuluoNetwork') : null;
            // 如果 apiclient_key 密钥内容不为空，则加密后存入 $data 数组
            $data['apiclient_key'] = $apiclientKey ? authcode($apiclientKey, 'ENCODE', 'YuluoNetwork') : null;
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 config 表中对应 id 和 much_id 的记录，并清除相关缓存
                Db::name('config')->where('id', $usid)->where('much_id', $this->much_id)->cache('fatal_' . $this->much_id)->update($data);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        // 调用 wiper 方法获取小程序设置
        $configList = $this->wiper();
        // 将小程序设置分配到视图
        $this->assign('configList', $configList);
        // 将平台标识码分配到视图
        $this->assign('signCode', $this->sign_code);
        // 将 much_id 分配到视图
        $this->assign('much_id', $this->much_id);
        // 渲染并返回视图
        return $this->fetch();
    }

    // 构造函数
    public function __construct(Request $request = null)
    {
        // 调用父类的构造函数
        parent::__construct($request);
        // 调用当前类的初始化方法
        $this->_initialize();
    }

    // 初始化方法
    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        // 调用父类的初始化方法
        parent::_initialize();
        // 调用 Playful 类的 lovely 方法进行授权检测
        $lovely = Playful::lovely($this->much_id);
        // 判断授权检测结果是否通过
        if ($lovely['dried'] !== md5(time() * pi())) {
            // 实例化一个新的视图对象
            $view = new View();
            // 将授权码共享到视图，用于页面显示
            $view->share('depressed', $lovely['randCode']);
            // 抛出 HTTP 响应异常，直接渲染授权错误页面
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    //  获取小程序设置
    private function wiper()
    {
        // 从缓存中读取以 'fatal_' 和 much_id 拼接的键名对应的配置
        $getConfig = cache('fatal_' . $this->much_id);
        // 如果缓存中没有配置
        if (!$getConfig) {
            // 从数据库的 config 表中查询对应 much_id 的配置
            $getConfig = Db::name('config')->where('much_id', $this->much_id)->find();
            // 如果数据库中存在配置
            if ($getConfig) {
                // 遍历配置数组的每个键值对
                foreach ($getConfig as $key => $value) {
                    // 如果键名不是 id, pay_react, version_type, much_id
                    if ($key != 'id' && $key != 'pay_react' && $key != 'version_type' && $key != 'much_id') {
                        // 对值进行解密
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork');
                    }
                }
            // 如果数据库中也不存在配置
            } else {
                // 获取当前请求的域名
                $domain = explode(':', $_SERVER['HTTP_HOST']);
                // 获取当前脚本的路径
                $absRess = explode("index.php", $_SERVER['SCRIPT_NAME']);
                // 拼接生成默认的支付回调 URL
                $payReactURLReplace = "https://{$domain[0]}{$absRess[0]}payReact.php";
                // 将 URL 中的反斜杠替换为正斜杠
                $payReactURL = str_replace('\\', '/', $payReactURLReplace);
                // 准备要插入数据库的默认配置
                $getConfig = [
                    'pay_react' => $payReactURL, // 支付回调地址
                    'much_id' => $this->much_id // 当前 much_id
                ];
                // 启动数据库事务
                Db::startTrans();
                // 尝试执行数据库操作
                try {
                    // 将默认配置插入数据库，并获取自增 ID
                    $getConfig['id'] = Db::name('config')->insertGetId($getConfig);
                    // 提交事务
                    Db::commit();
                // 捕获可能发生的异常
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                }
            }
            // 将获取或创建的配置写入缓存
            cache('fatal_' . $this->much_id, $getConfig);
        }
        // 返回配置信息
        return $getConfig;
    }


    //  轮播广告
    public function symbol()
    {
        // 调用 defaultQuery 方法获取默认的 URL 查询参数
        $url = $this->defaultQuery();
        // 从 polling 表中查询数据
        $list = Db::name('polling')
            // 设置查询条件：ad_type 不等于 2
            ->where('ad_type', '<>', 2)
            // 设置查询条件：much_id 等于当前 much_id
            ->where('much_id', $this->much_id)
            // 按 scores 字段升序排序
            ->order('scores', 'asc')
            // 进行分页查询，每页 10 条，并传入分页参数
            ->paginate(10, false, ['query' => ['s' => $url]]);
        // 将查询结果列表分配到视图
        $this->assign('list', $list);
        // 从 GET 请求中获取当前页码，默认为 1
        $page = request()->get('page', 1);
        // 将当前页码分配到视图
        $this->assign('page', $page);
        // 渲染并返回视图
        return $this->fetch();
    }

    //  添加轮播广告
    public function rusymbol()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 获取所有 POST 数据
            $data = request()->post();
            // 如果 practice_type 等于 1（通常表示内部页面跳转）
            if ($data['practice_type'] == 1) {
                // 将 URL 中的 '?' 替换为 '&'，以兼容小程序页面路径带参数的格式
                $data['url'] = str_replace('?', '&', $data['url']);
            }
            // 设置 much_id
            $data['much_id'] = $this->much_id;
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 向 polling 表中插入新数据
                Db::name('polling')->insert($data);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        // 渲染并返回视图
        return $this->fetch();
    }

    //  编辑轮播广告
    public function upsymbol()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 获取所有 POST 数据
            $data = request()->post();
            // 获取记录的 ID
            $usid = $data['usid'];
            // 从数据数组中移除 ID，防止被更新
            unset($data['usid']);
            // 如果 practice_type 等于 1
            if ($data['practice_type'] == 1) {
                // 替换 URL 中的 '?'
                $data['url'] = str_replace('?', '&', $data['url']);
            }
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 polling 表中对应 id 和 much_id 的记录
                Db::name('polling')->where('id', $usid)->where('much_id', $this->much_id)->update($data);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        // 从 GET 请求中获取要编辑的记录的 ID
        $uplid = request()->get('uplid', '');
        // 如果 ID 存在
        if ($uplid) {
            // 从 polling 表中查询对应 id 和 much_id 的记录
            $poList = Db::name('polling')->where('id', $uplid)->where('much_id', $this->much_id)->find();
            // 如果记录存在
            if ($poList) {
                // 将查询到的数据分配到视图
                $this->assign('list', $poList);
                // 渲染并返回编辑视图
                return $this->fetch();
            // 如果记录不存在
            } else {
                // 重定向到轮播广告列表页面
                $this->redirect('systems/symbol');
            }
        // 如果 ID 不存在
        } else {
            // 重定向到轮播广告列表页面
            $this->redirect('systems/symbol');
        }
    }

    //  删除轮播广告
    public function symlint()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 从 POST 请求中获取要删除的记录的 ID
            $usid = request()->post('ecid');
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 从 polling 表中删除对应 id 和 much_id 的记录
                Db::name('polling')->where('id', $usid)->where('much_id', $this->much_id)->delete();
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '删除成功']);
        }
    }

    //  更改轮播广告排序
    public function slymben()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 从 POST 请求中获取记录的 ID
            $syid = request()->post('asyId');
            // 从 POST 请求中获取排序值
            $scores = request()->post('dalue');
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 polling 表中对应 id 和 much_id 的记录的 scores 字段
                Db::name('polling')->where('id', $syid)->where('much_id', $this->much_id)->update(['scores' => $scores]);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        }
    }

    //  货币兑换
    public function punch()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            //  从 POST 请求中获取记录 ID
            $puid = request()->post('puid');
            // 从 POST 请求中获取货币兑换渠道，并转换为整数
            $puData['currency_redemption_channel'] = intval(request()->post('currencyRedemptionChannel'));
            // 从 POST 请求中获取积分兑换渠道，并转换为整数
            $puData['fraction_redemption_channel'] = intval(request()->post('fractionRedemptionChannel'));
            // 从 POST 请求中获取货币图标，并去除首尾空格
            $puData['currency_icon'] = trim(request()->post('sngimg', ''));
            // 从 POST 请求中获取积分兑换比例，默认为 10
            $puData['fraction_scale'] = request()->post('fracScale', 10);
            // 确保积分比例不小于等于 0，否则设置为 10
            $puData['fraction_scale'] <= 0 && $puData['fraction_scale'] = 10;
            // 从 POST 请求中获取邀请奖励最小值，默认为 0
            $puData['invite_min'] = request()->post('inviteMin', 0);
            // 从 POST 请求中获取邀请奖励最大值，默认为 0
            $puData['invite_max'] = request()->post('inviteMax', 0);
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 user_punch_range 表中对应 id 和 much_id 的记录
                Db::name('user_punch_range')->where('id', $puid)->where('much_id', $this->much_id)->update($puData);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        // 从 user_punch_range 表中查询对应 much_id 的记录
        $punchRangeList = Db::name('user_punch_range')->where('much_id', $this->much_id)->find();
        // 将查询结果赋值给 $list
        $list = $punchRangeList;
        // 将数据分配到视图
        $this->assign('list', $list);
        // 调用 defaultNavigate 方法获取默认导航信息
        $defaultNavigate = $this->defaultNavigate();
        // 将默认导航信息分配到视图
        $this->assign('defaultNavigate', $defaultNavigate);
        // 渲染并返回视图
        return $this->fetch();
    }

    //  转发帖子
    public function partake()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 从 POST 请求中获取记录 ID
            $usid = request()->post('usid');
            // 从 POST 请求中获取是否开启转发功能
            $data['whether_open'] = request()->post('wetopen');
            // 从 POST 请求中获取转发标题
            $data['title'] = request()->post('title');
            // 从 POST 请求中获取转发图片
            $data['reis_img'] = request()->post('sngimg');
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 reissue 表中对应 id 和 much_id 的记录
                Db::name('reissue')->where('id', $usid)->where('much_id', $this->much_id)->update($data);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        // 从 reissue 表中查询对应 much_id 的记录
        $resList = Db::name('reissue')->where('much_id', $this->much_id)->find();
        // 将查询结果分配到视图
        $this->assign('list', $resList);
        // 渲染并返回视图
        return $this->fetch();
    }


    //  流量主
    public function proclaim()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 获取所有 POST 数据
            $rangeData = request()->post();
            // 获取首页广告位开关状态
            $data['adstory'] = $rangeData['adstory'];
            // 获取个人中心广告位开关状态
            $data['adsper'] = $rangeData['adsper'];
            // 获取帖子列表上方广告位开关状态
            $data['pre_post_twig'] = $rangeData['prePostTwig'];
            // 获取帖子详情页广告位开关状态
            $data['isolate'] = $rangeData['isolate'];
            // 获取 Banner 广告单元 ID
            $data['adunit_id'] = $rangeData['adunitId'];
            // 获取帖子列表上方广告单元 ID
            $data['pre_post_id'] = $rangeData['prePostId'];
            // 获取激励视频广告开关状态
            $data['incentive_duct'] = $rangeData['incentiveDuct'];
            // 获取激励视频广告单元 ID
            $data['incentive_id'] = $rangeData['incentiveId'];
            // 获取格子广告开关状态
            $data['lattice_twig'] = $rangeData['latticeTwig'];
            // 获取格子广告单元 ID
            $data['lattice_id'] = $rangeData['latticeId'];
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 advertise 表中对应 much_id 的记录
                Db::name('advertise')->where('much_id', $this->much_id)->update($data);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        // 从 advertise 表中查询对应 much_id 的记录
        $advList = Db::name('advertise')->where('much_id', $this->much_id)->find();
        // 将查询结果分配到视图
        $this->assign('list', $advList);
        // 渲染并返回视图
        return $this->fetch();
    }

    //  小程序导航
    public function navigate()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 获取所有 POST 数据
            $data = request()->post();
            // 将 pattern_data 数组转换为 JSON 字符串
            $data['pattern_data'] = json_encode($data['pattern_data'], 320);
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 design 表中对应 much_id 的记录，并清除相关缓存
                db('design')->where('much_id', $this->much_id)->cache('design_' . $this->much_id)->update($data);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        // 调用 defaultNavigate 方法获取默认导航信息
        $list = $this->defaultNavigate();
        // 将 pattern_data JSON 字符串解码为数组
        $list['pattern_data'] = json_decode($list['pattern_data'], true);
        // 将导航信息分配到视图
        $this->assign('list', $list);
        // 渲染并返回视图
        return $this->fetch();
    }


    //  订阅消息
    public function inform()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 获取所有 POST 数据
            $data = request()->post();
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 subscribe 表中对应 much_id 的记录，将数据编码为 JSON 字符串后存入 parallelism_data 字段
                Db::name('subscribe')->where('much_id', $this->much_id)->update(["parallelism_data" => json_encode($data, true)]);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        // 如果不是 POST 或 AJAX 请求
        } else {
            // 从 subscribe 表中查询对应 much_id 的记录
            $getSubscribe = Db::name('subscribe')->where('much_id', $this->much_id)->find();
            // 如果记录不存在
            if (!$getSubscribe) {
                // 启动数据库事务
                Db::startTrans();
                // 尝试执行数据库操作
                try {
                    // 创建一条默认的订阅消息模板数据
                    $getSubscribe['parallelism_data'] = json_encode(['YL0001' => '', 'YL0002' => '', 'YL0003' => '', 'YL0004' => '', 'YL0005' => '', 'YL0006' => '', 'YL0007' => '', 'YL0008' => '', 'YL0009' => '', 'YL0010' => '', 'YL0011' => ''], true);
                    // 设置 much_id
                    $getSubscribe['much_id'] = $this->much_id;
                    // 将默认数据插入 subscribe 表，并获取自增 ID
                    $getSubscribe['id'] = Db::name('subscribe')->insertGetId($getSubscribe);
                    // 提交事务
                    Db::commit();
                // 捕获可能发生的异常
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                }
            }
            // 将 parallelism_data JSON 字符串解码为数组
            $getSubscribe['parallelism_data'] = json_decode($getSubscribe['parallelism_data'], true);
            // 将订阅消息数据分配到视图
            $this->assign('list', $getSubscribe);
            // 渲染并返回视图
            return $this->fetch();
        }
    }

    //  小程序版本号
    public function audit()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 从 POST 请求中获取记录 ID
            $euid = request()->post('euid');
            // 从 POST 请求中获取状态值
            $data['status'] = request()->post('status');
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 version 表中对应 id 和 much_id 的记录的状态
                Db::name('version')->where('id', $euid)->where('much_id', $this->much_id)->update($data);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '更改状态成功']);
        }
        // 调用 defaultQuery 方法获取默认的 URL 查询参数
        $url = $this->defaultQuery();
        // 从 GET 请求中获取 hazy_name 参数，用于模糊搜索
        $hazy_name = request()->get('hazy_name', '');
        // 从 version 表中查询数据
        $list = Db::name('version')
            // 设置查询条件：sign_code 字段模糊匹配 hazy_name
            ->where('sign_code', 'like', "%{$hazy_name}%")
            // 设置查询条件：much_id 等于当前 much_id
            ->where('much_id', $this->much_id)
            // 按 id 字段降序排序
            ->order('id', 'desc')
            // 进行分页查询，每页 10 条，并传入分页参数
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        // 将查询结果列表分配到视图
        $this->assign('list', $list);
        // 从 authority 表中查询对应 much_id 的记录
        $authorityInfo = Db::name('authority')->where('much_id', $this->much_id)->find();
        // 将权限信息分配到视图
        $this->assign('authorityInfo', $authorityInfo);
        // 将模糊搜索关键词分配到视图
        $this->assign('hazy_name', $hazy_name);
        // 从 GET 请求中获取当前页码，默认为 1
        $page = request()->get('page', 1);
        // 将当前页码分配到视图
        $this->assign('page', $page);
        // 渲染并返回视图
        return $this->fetch();
    }

    //  小程序过审核开关
    public function limuda()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 从 POST 请求中获取状态值
            $data['ensure_arbor'] = request()->post('status');
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 authority 表中对应 much_id 的记录
                Db::name('authority')->where('much_id', $this->much_id)->update($data);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '更改状态成功']);
        }
    }

    //  视频设置
    public function video()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 从 POST 请求中获取视频设置
            $data['video_setting'] = request()->post('videoSetting');
            // 从 POST 请求中获取视频压缩设置
            $data['video_compression_setting'] = request()->post('videoCompressionSetting');
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 更新 authority 表中对应 much_id 的记录，并清除相关缓存
                Db::name('authority')->where('much_id', $this->much_id)->cache('knight_' . $this->much_id)->update($data);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        // 从 authority 表中查询视频设置相关的字段
        $video = Db::name('authority')->where('much_id', $this->much_id)->field('video_setting,video_compression_setting')->find();
        // 将查询结果分配到视图
        $this->assign('list', $video);
        // 渲染并返回视图
        return $this->fetch();
    }

    //  功能开关
    public function switch_control()
    {
        // 判断请求是否为 POST 方式且为 AJAX 请求
        if (request()->isPost() && request()->isAjax()) {
            // 从 POST 请求中获取记录 ID
            $suid = request()->post('suid');
            // 定义数据库字段与 POST 请求参数的映射关系，以简化数据获取过程
            $fieldsMap = [
                'prevent_duplication' => 'preventDuplication',
                'tory_arbor' => 'toryArbor',
                'welfare_arbor' => 'welfareArbor',
                'noble_arbor' => 'nobleArbor',
                'wallet_arbor' => 'walletArbor',
                'recharge_arbor' => 'rechargeArbor',
                'receipt_arbor' => 'receiptArbor',
                'title_arbor' => 'titleArbor',
                'hair_audio_arbor' => 'hairAudioArbor',
                'hair_graffiti_arbor' => 'hairGraffitiArbor',
                'hair_video_arbor' => 'hairVideoArbor',
                'voice_member' => 'voiceMember',
                'graffiti_member' => 'graffitiMember',
                'video_member' => 'videoMember',
                'title_input_arbor' => 'titleInputArbor',
                'force_phone_arbor' => 'forcePhoneArbor',
                're_force_phone_arbor' => 'reForcePhoneArbor',
                'buy_paper_arbor' => 'buyPaperArbor',
                'buy_paper_member' => 'buyPaperMember',
                'shop_arbor' => 'shopArbor',
                'tribute_arbor' => 'tributeArbor',
                'guard_arbor' => 'guardArbor',
                'reprint_arbor' => 'reprintArbor',
                'ios_pay_arbor' => 'iosPayArbor',
                'speech_arbor' => 'speechArbor',
                'home_random_arbor' => 'homeRandomArbor',
                'home_release_arbor' => 'homeReleaseArbor',
                'brisk_member' => 'briskMember',
                'hair_brisk_arbor' => 'hairBriskArbor',
                'user_info_update_arbor' => 'userInfoUpdateArbor',
                'warrant_arbor' => 'warrantArbor',
                'whisper_arbor' => 'whisperArbor',
                'video_auto_arbor' => 'videoAutoArbor',
                'hair_vote_arbor' => 'hairVoteArbor',
                'vote_member' => 'voteMember',
                'engrave_arbor' => 'engraveArbor',
                'travel_arbor' => 'travelArbor',
                'feeling_arbor' => 'feelingArbor',
                'overall_arbor' => 'overallArbor',
                'allow_user_topic' => 'allowUserTopic',
                'pre_content_arbor' => 'preContentArbor',
                'home_my_tory_arbor' => 'homeMyToryArbor',
                'rel_paper_img_style' => 'relPaperImgStyle',
                'paper_browse_num_hide' => 'paperBrowseNumHide',
                'rel_paper_location_hide' => 'relPaperLocationHide',
                'rel_paper_topicsd_hide' => 'relPaperTopicsdHide',
                'rel_paper_image_hide' => 'relPaperImageHide',
                'video_download_arbor' => 'videoDownloadArbor',
                'short_drama_arbor' => 'shortDramaArbor',
            ];
            /*
            $data['tory_sort_arbor'] = request()->post('torySortArbor');
            */
            // 使用 array_map 函数式编程方式，根据映射关系动态构建数据数组
            // 这种写法比 foreach 循环更简洁，同时保留了 POST 请求的原始值以支持多种数据类型
            $data = array_map(function ($postKey) {
                return intval(request()->post($postKey));
            }, $fieldsMap);
            // 启动数据库事务
            Db::startTrans();
            // 尝试执行数据库操作
            try {
                // 一次性更新 authority 表中对应 id 和 much_id 的所有功能开关数据
                Db::name('authority')->where('id', $suid)->where('much_id', $this->much_id)->update($data);
                // 提交事务
                Db::commit();
            // 捕获可能发生的异常
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                // 以 JSON 格式返回错误信息
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            // 以 JSON 格式返回成功信息
            return json(['code' => 1, 'msg' => '保存成功']);
        // 如果不是 POST 或 AJAX 请求
        } else {
            // 渲染并返回视图
            return $this->fetch();
        }
    }
}
