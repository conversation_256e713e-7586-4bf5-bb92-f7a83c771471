<?php

namespace app\api\controller;


use app\api\service\Scheduled;
use app\api\service\Util;
use think\Cache;
use think\Db;

class Task extends Base
{
    /**
     * 获取任务列表
     */
    public function get_task()
    {
        $data = input('param.');
        $rs = array('code' => 0, 'info' => []);
        $list = Db::name('task')->where('much_id', $data['much_id'])->where('is_being', 0)->order('scores')->select();
        $new_list = array();
        $sc=new Scheduled();
        foreach ($list as $k => $v) {
            if ($v['task_cycle'] == 0) {//每日任务
                $new_list[0]['title'] = '每日任务';
                $v['count'] = $sc->Finished($v['id'],$this->user_info['id']);
                $v['ok_count'] = $sc->finished_count($v['id'],$this->user_info['id'],$data['much_id']);
                $new_list[0]['list'][] = $v;
            }
            if ($v['task_cycle'] == 1) {//每周任务
                $new_list[1]['title'] = '每周任务';
                $v['count'] = $sc->Finished($v['id'],$this->user_info['id']);
                $v['ok_count'] = $sc->finished_count($v['id'],$this->user_info['id'],$data['much_id']);
                $new_list[1]['list'][] = $v;
            }
            if ($v['task_cycle'] == 2) {//每月任务
                $new_list[2]['title'] = '每月任务';
                $v['count'] = $sc->Finished($v['id'],$this->user_info['id']);
                $v['ok_count'] = $sc->finished_count($v['id'],$this->user_info['id'],$data['much_id']);
                $new_list[2]['list'][] = $v;
            }
            if ($v['task_cycle'] == 3) {//每年任务
                $new_list[3]['title'] = '每年任务';
                $v['count'] = $sc->Finished($v['id'],$this->user_info['id']);
                $v['ok_count'] = $sc->finished_count($v['id'],$this->user_info['id'],$data['much_id']);
                $new_list[3]['list'][] = $v;
            }

        }
        $util = new Util();
        $rs['info'] = $new_list;
        //查询是否是会员
        $rs['vip'] = $util->get_user_vip($this->user_info['id']);
        return $this->json_rewrite($rs);
    }


    /**
     * 完成激励视频任务
     */
    public function set_ad()
    {
        $data = input('param.');
        //查询激励视频次数
        $incentive_duct = Db::name('advertise')->where('much_id', $data['much_id'])->field('incentive_duct')->find();
        $vd = Db::name('user_watch_ads')
            ->whereTime('fulfill_time', 'today')
            ->where('ad_type', 0)
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->count();
        if ($incentive_duct['incentive_duct'] != 0) {
            if ($vd >= $incentive_duct['incentive_duct']) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '今天激励视频已达上限！']);
            }

        }

        $info = Db::name('user_watch_ads')->insert(['user_id' => $this->user_info['id'], 'ad_type' => $data['type'], 'fulfill_type' => 0, 'fulfill_time' => time(), 'much_id' => $data['much_id']]);
        if ($info) {
            return $this->json_rewrite(['status' => 'success', 'msg' => '完成任务']);
        } else {
            return $this->json_rewrite(['status' => 'error', 'msg' => '未完成任务']);
        }

    }

    /**
     * 领取
     */
    public function task_receive()
    {
        $data = input('param.');
        //当前用户是否是会员
        $util = new Util();
        $user_vip = $util->get_user_vip($this->user_info['id']);
        //当前任务详情
        $task_info = Db::name('task')->where('id', $data['task_id'])->where('much_id', $data['much_id'])->where('is_being', 0)->find();
        if (empty($task_info)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '当前任务已被管理员删除！']);
        }
        //查询是否完成
        $sc=new Scheduled();
        $task_log = $sc->finished_count($data['task_id'],$this->user_info['id'],$data['much_id']);
        if ($task_log >= $task_info['task_frequency']) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '当前任务已完成！']);
        }
        $msg = '';
        //  获取明天0点时间戳
        $tomorrowTime = strtotime(date("Y-m-d"), time()) + 86400;
        //  创建随机值
        $lockRandContent = md5(uniqid(mt_rand(), true));
        //  缓存有效期到明天零点
        $check_d = Cache::remember("task_receive_{$this->user_info['id']}_{$data['task_id']}_{$data['much_id']}", $lockRandContent, $tomorrowTime - time());
        if ($check_d == $lockRandContent) {
            // 启动事务
            Db::startTrans();
            try {
                //增加完成表数据
                $task_logger = Db::name('task_logger')->insert([
                    'task_id' => $data['task_id'],
                    'user_id' => $this->user_info['id'],
                    'is_rich_person' => $user_vip,
                    'task_salary' => $user_vip == 1 ? $task_info['rich_task_salary'] : $task_info['poor_task_salary'],
                    'complete_description' => $task_info['task_name'],
                    'complete_time' => time(),
                    'much_id' => $data['much_id']]);
                if (!$task_logger) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '领取失败，请稍候重试！1']);
                }
                //判断增加相应数据类型
                if ($task_info['task_reward_type'] == 0) { //积分
                    $res = $this->fraction($task_info);
                    $msg = $this->design['confer'];
                    if ($res == 1) {
                        Db::rollback();
                        return $this->json_rewrite(['status' => 'error', 'msg' => '领取失败，请稍候重试！2']);
                    }
                }
                if ($task_info['task_reward_type'] == 1) { //经验
                    $res = $this->experience($task_info, 0);
                    $msg = '经验';
                    if ($res != 0) {
                        Db::rollback();
                        return $this->json_rewrite(['status' => 'error', 'msg' => '领取失败，请稍候重试！' . $res]);
                    }
                }
                if ($task_info['task_reward_type'] == 2) { //荣誉点
                    $res = $this->experience($task_info, 1);
                    $msg = '荣誉点';
                    if ($res == 1) {
                        Db::rollback();
                        return $this->json_rewrite(['status' => 'error', 'msg' => '领取失败，请稍候重试！' . $res]);
                    }
                }
                Db::commit();
                $msg = $msg . "+" . ($user_vip == 0 ? $task_info['poor_task_salary'] : $task_info['rich_task_salary']);
                //Cache::rm('task_receive_' . $data['openid']);
                return $this->json_rewrite(['status' => 'success', 'msg' => $msg]);
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '领取失败，请稍候重试！5']);
            }
        } else {
            return $this->json_rewrite(['status' => 'error', 'msg' => '点的太快了...']);
        }
    }

    /**
     * 增加积分
     */
    public function fraction($task_info)
    {
        $util = new Util();
        $user_vip = $util->get_user_vip($this->user_info['id']);
        $money = $user_vip == 1 ? $task_info['rich_task_salary'] : $task_info['poor_task_salary'];
        Db::startTrans();
        try {
            //货币明细表增加数据
            $amount['user_id'] = $this->user_info['id'];
            $amount['category'] = 3;
            $amount['finance'] = $money;
            $amount['poem_fraction'] = $this->user_info['fraction'];//初始积分
            $amount['surplus_fraction'] = bcadd($this->user_info['fraction'], $money, 2);//落实积分
            $amount['poem_conch'] = $this->user_info['conch'];//初始贝壳
            $amount['surplus_conch'] = $this->user_info['conch'];//落实贝壳
            $amount['ruins_time'] = time();
            $amount['solution'] = '完成<' . $task_info['task_name'] . '>';
            $amount['evaluate'] = 1;
            $amount['much_id'] = $task_info['much_id'];
            $amount['ruins_time'] = time();
            $user_amount = Db::name('user_amount')->insert($amount);
            if (!$user_amount) {
                Db::rollback();
                return 1;
            }
            //给用户增加积分
            $user_fraction = Db::name('user')->where('id', $this->user_info['id'])->update(['fraction' => bcadd($this->user_info['fraction'], $money, 2)]);
            if (!$user_fraction) {
                Db::rollback();
                return 1;
            }
            // 提交事务
            Db::commit();
            return 0;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return 1;
        }
    }

    /**
     * 增加经验值/荣誉点
     */
    public function experience($task_info, $type)
    {
        $util = new Util();
        $user_vip = $util->get_user_vip($this->user_info['id']);
        $money = $user_vip == 1 ? $task_info['rich_task_salary'] : $task_info['poor_task_salary'];
        //$money = 999999;
        Db::startTrans();
        try {
            if ($type == 0) {//经验
                //当前用户经验值
                $this_exp = bcadd($this->user_info['experience'], $money, 2);
                //当前用户等级
                $this_level = $this->user_info['level'];
                $exp = 0;
                while (true) {
                    $this_level++;
                    $user_level = Db::name('user_level')->where('level_hierarchy', $this_level)->where('much_id', $task_info['much_id'])->find();
                    //下一级没有内容跳出循环
                    if (empty($user_level)) {
                        $user_fraction = Db::name('user')->where('id', $this->user_info['id'])->update(['experience' => $this_exp]);
                        if ($user_fraction === false) {
                            Db::rollback();
                            return 1;
                        }
                        break;
                    }
                    //return $this_exp >= $user_level['need_experience'];
                    //当前经验大于下一级经验
                    if ($this_exp >= $user_level['need_experience']) {
                        //计算余下经验值
                        $this_exp = bcsub($this_exp, $user_level['need_experience'], 2);
                        //更新用户信息
                        $user_fraction = Db::name('user')->where('id', $this->user_info['id'])->update(['honor_point' => bcadd($this->user_info['honor_point'], $user_level['honor_point'], 2), 'level' => $this_level, 'experience' => $this_exp]);
                        if (!$user_fraction) {
                            Db::rollback();
                            return 1;
                        }
                    } else {
                        //更新用户信息
                        //判断是否和数据库一致

                        $user_fraction = Db::name('user')->where('id', $this->user_info['id'])->update(['experience' => $this_exp]);
                        if ($user_fraction === false) {
                            Db::rollback();
                            return 1;
                        }
                        break;
                    }
                    $exp++;
                }
            } else {//荣誉点
                $exp = 0;
                //当前用户荣誉点
                $this_exp = bcadd($this->user_info['honor_point'], $money, 2);

                //更新用户信息
                $user_honor_point = Db::name('user')->where('id', $this->user_info['id'])->update(['honor_point' => $this_exp]);
                if ($user_honor_point === false) {
                    Db::rollback();
                    return 1;
                }
            }
            //货币明细表增加数据
            $logger['user_id'] = $this->user_info['id'];
            $logger['type'] = $type;
            $logger['cypher'] = 0;
            $logger['dot_before'] = $type == 0 ? $this->user_info['experience'] : $this->user_info['honor_point'];
            $logger['points'] = $money;
            $logger['dot_after'] = $this_exp;
            $logger['receive_time'] = time();
            $logger['much_id'] = $task_info['much_id'];
            $logger['dot_cap'] = $exp == 0 ? '完成任务<' . $task_info['task_name'] . '>' : '完成任务<' . $task_info['task_name'] . '>并且升了' . $exp . '级';
            $user_amount = Db::name('user_exp_glory_logger')->insert($logger);
            if (!$user_amount) {
                Db::rollback();
                return 1;
            }
            // 提交事务
            Db::commit();
            return 0;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return 1;
        }
    }

    /**
     * 完成任务列表
     */
    public function get_task_logger()
    {
        $data = input('param.');
        $rs = array('code' => 0, 'info' => []);
        $list = Db::name('task_logger')->alias('t')
            ->join('task a', 't.task_id=a.id')
            ->where('t.much_id', $data['much_id'])
            ->where('t.user_id', $this->user_info['id'])
            ->order('t.complete_time desc')
            ->field('t.*,a.task_reward_type')
            ->page($data['page'], 25)
            ->select();
        foreach ($list as $k => $v) {
            $list[$k]['complete_time'] = date('Y-m-d', $v['complete_time']);
        }
        $rs['info'] = $list;
        return $this->json_rewrite($rs);
    }

    /**
     * 记录转发
     */
    public function user_forwarded()
    {
        $data = input('param.');
        $check = Db::name('user_forwarded')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $data['id'])->find();
        if ($check) {
            return $this->json_rewrite(['status' => 'success', 'msg' => '']);
        }
        $rs = Db::name('user_forwarded')->insert(['paper_id' => $data['id'], 'user_id' => $this->user_info['id'], 'fulfill_time' => time(), 'much_id' => $data['much_id']]);
        if ($rs) {
            return $this->json_rewrite(['status' => 'success', 'msg' => '']);
        } else {
            return $this->json_rewrite(['status' => 'error', 'msg' => '']);
        }
    }

    /**
     * 头像框
     */
    public function get_avatar_frame()
    {
        $data = input('param.');
        $info = Db::name('avatar_frame')
            ->where('much_id', $data['much_id'])
            ->where('status', 1)
            ->order('scores')
            ->select();
        foreach ($info as $k => $v) {
            $info[$k]['unlock'] = Db::name('user_avatar_frame')
                ->where('much_id', $data['much_id'])
                ->where('af_id', $v['id'])
                ->where('user_id', $this->user_info['id'])
                ->count();
            $info[$k]['wear_af'] = $this->user_info['wear_af'];
        }
        $yu = array('id' => 0, 'adorn_icon' => '', 'adorn_name' => '默认', 'unlock' => 1);
        array_unshift($info, $yu);

        //当前装备的头像框、
        $this_avatar = Db::name('avatar_frame')
            ->where('id', $this->user_info['wear_af'])
            ->where('much_id', $data['much_id'])
            ->find();
        return $this->json_rewrite(['info' => $info, 'this_avatar' => $this_avatar['adorn_icon']]);
    }

    /**
     * 勋章
     */
    public function get_user_medal()
    {
        $data = input('param.');
        $util = new Util();
        $user_info = $this->user_info;
        $user_info['user_nick_name'] = emoji_decode($user_info['user_nick_name']);
        $rs = ['status' => 'success', 'info' => [], 'list' => [], 'this_img' => '', 'user_info' => $user_info];
        $info = Db::name('medal')->where('much_id', $data['much_id'])->where('status', 1)->order('scores')->select();

        $list = Db::name('user_medal')->alias('u')
            ->join('medal m', 'u.medal_id=m.id')
            ->where('u.much_id', $data['much_id'])
            ->where('user_id', $this->user_info['id'])
            ->field('u.*,m.merit_icon,m.merit_name')
            ->order('u.unlock_time desc')
            ->select();
        $list2 = Db::name('user_special_nickname')->alias('u')
            ->join('special_nickname m', 'u.special_id=m.id')
            ->where('u.much_id', $data['much_id'])
            ->where('u.user_id', $this->user_info['id'])
            ->field('u.*,m.special_name')
            ->order('u.unlock_time desc')
            ->select();
        $list3 = Db::name('user_avatar_frame')->alias('u')
            ->join('avatar_frame m', 'u.af_id=m.id')
            ->where('u.much_id', $data['much_id'])
            ->where('u.user_id', $this->user_info['id'])
            ->field('u.*,m.adorn_name')
            ->order('u.unlock_time desc')
            ->select();
        $list_and_list = $util->array_and_time($list, $list2);
        $list_and_list = $util->array_and_time($list_and_list, $list3);
        foreach ($list_and_list as $k => $v) {
            $list_and_list[$k]['unlock_time'] = date('Y-m-d', $v['unlock_time']);
            if ($list_and_list[$k]['special_name']) {
                $list_and_list[$k]['merit_name'] = $v['special_name'];
                $list_and_list[$k]['unlock_outlay'] = $v['unlock_outlay'] . '荣誉点';

            } else if ($list_and_list[$k]['adorn_name']) {
                $list_and_list[$k]['merit_name'] = $v['adorn_name'];
                $list_and_list[$k]['unlock_outlay'] = $v['unlock_outlay'] . $this->design['confer'];
            } else {
                $list_and_list[$k]['unlock_outlay'] = $v['unlock_outlay'] . '荣誉点';
            }

        }

        foreach ($info as $k => $v) {
            $info[$k]['unlock'] = Db::name('user_medal')->where('much_id', $data['much_id'])->where('medal_id', $v['id'])->where('user_id', $this->user_info['id'])->count();
            $info[$k]['wear_merit'] = $this->user_info['wear_merit'];
        }
        $yu = array('id' => 0, 'merit_icon' => '/yl_welore/style/icon/c0h.png', 'merit_name' => '默认', 'unlock' => 1);
        array_unshift($info, $yu);
        $rs['info'] = $info;
        $rs['list'] = $list_and_list;
        if ($this->user_info['wear_merit'] != 0) {
            $this_img = Db::name('medal')->where('much_id', $data['much_id'])->where('id', $this->user_info['wear_merit'])->find();
            $rs['this_img'] = $this_img['merit_icon'];
        }
        if ($this->user_info['wear_special_id'] != 0) {
            $this_img = Db::name('special_nickname')
                ->where('much_id', $data['much_id'])
                ->where('id', $this->user_info['wear_special_id'])
                ->find();
            $rs['this_text_style'] = $this_img['special_style'];
        }
        return $this->json_rewrite($rs);
    }

    /**
     * 解锁头像框
     */
    public function user_do_unlock_avatar()
    {
        $data = input('param.');
        $util = new Util();
        //详情
        $mod_info = Db::name('avatar_frame')->where('much_id', $data['much_id'])->where('id', $data['id'])->find();
        //查询是否够解锁
        if ($mod_info['unlock_fraction'] > $this->user_info['fraction']) {
            return $this->json_rewrite(['status' => 'error', 'msg' => $this->design['confer'] . '不足，解锁失败！']);
        }
        // 启动事务
        Db::startTrans();
        try {
            $old = array();
            //历史数据
            $old['user_id'] = $this->user_info['id'];
            $old['af_id'] = $data['id'];
            $old['unlock_outlay'] = $mod_info['unlock_fraction'];
            $old['unlock_time'] = time();
            $old['much_id'] = $data['much_id'];
            $ins_old = Db::name('user_avatar_frame')->insert($old);
            if (!$ins_old) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，解锁失败！']);
            }
            //用户减去积分
            $up = bcsub($this->user_info['fraction'], $mod_info['unlock_fraction'], 2);
            //明细
            $exp_bol = $util->user_amount($this->user_info['id'], 1, $mod_info['unlock_fraction'], $this->user_info['fraction'], $up, $this->user_info['conch'], $this->user_info['conch'], '1', '解锁头像框<' . $mod_info['adorn_name'] . '>', $data['much_id']);
            if (!$exp_bol) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，解锁失败！']);
            }


            $user_update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['fraction' => $up]);
            if (!$user_update) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，解锁失败！']);
            }
            Db::commit();
            return $this->json_rewrite(['status' => 'success', 'msg' => '解锁成功！']);
        } catch (\Exception $e) {
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，解锁失败！', 'code' => $e->getMessage()]);
        }

    }

    /**
     * 解锁勋章
     */
    public function user_do_unlock()
    {
        $data = input('param.');
        $util = new Util();
        //详情
        $mod_info = Db::name('medal')->where('much_id', $data['much_id'])->where('id', $data['id'])->find();
        //查询是否够解锁
        if ($mod_info['unlock_outlay'] > $this->user_info['honor_point']) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '荣誉点不足，解锁失败！']);
        }
        // 启动事务
        Db::startTrans();
        try {
            $old = array();
            //历史数据
            $old['user_id'] = $this->user_info['id'];
            $old['medal_id'] = $data['id'];
            $old['unlock_outlay'] = $mod_info['unlock_outlay'];
            $old['unlock_time'] = time();
            $old['much_id'] = $data['much_id'];
            $ins_old = Db::name('user_medal')->insert($old);
            if (!$ins_old) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，解锁失败！']);
            }
            //用户减去荣誉点
            $up = bcsub($this->user_info['honor_point'], $mod_info['unlock_outlay'], 0);
            $user_update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['honor_point' => $up]);
            if (!$user_update) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，解锁失败！']);
            }
            //明细
            $exp_bol = $util->user_exp_glory_logger($this->user_info, 1, 1, $mod_info['unlock_outlay'], '解锁勋章<' . $mod_info['merit_name'] . '>', $data['much_id']);
            if (!$exp_bol) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，解锁失败！']);
            }
            Db::commit();
            return $this->json_rewrite(['status' => 'success', 'msg' => '解锁成功！']);
        } catch (\Exception $e) {
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，解锁失败！', 'code' => $e->getMessage()]);
        }

    }

    /**
     * 解锁昵称特效
     */
    public function user_do_text_unlock()
    {
        $data = input('param.');
        $util = new Util();
        //详情
        $mod_info = Db::name('special_nickname')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->find();
        //查询是否够解锁
        if ($mod_info['unlock_outlay'] > $this->user_info['honor_point']) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '荣誉点不足，解锁失败！']);
        }
        // 启动事务
        Db::startTrans();
        try {
            $old = array();
            //历史数据
            $old['user_id'] = $this->user_info['id'];
            $old['special_id'] = $data['id'];
            $old['unlock_outlay'] = $mod_info['unlock_outlay'];
            $old['unlock_time'] = time();
            $old['much_id'] = $data['much_id'];
            $ins_old = Db::name('user_special_nickname')->insert($old);

            if (!$ins_old) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，解锁失败！']);
            }
            //用户减去荣誉点
            $up = bcsub($this->user_info['honor_point'], $mod_info['unlock_outlay'], 0);
            $user_update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['honor_point' => $up]);
            if (!$user_update) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，解锁失败！']);
            }
            //明细
            $exp_bol = $util->user_exp_glory_logger($this->user_info, 1, 1, $mod_info['unlock_outlay'], '解锁装扮<' . $mod_info['special_name'] . '>', $data['much_id']);
            if (!$exp_bol) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，解锁失败！']);
            }
            Db::commit();
            return $this->json_rewrite(['status' => 'success', 'msg' => '解锁成功！']);
        } catch (\Exception $e) {
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，解锁失败！', 'code' => $e->getMessage()]);
        }
    }

    /**
     * 佩戴勋章
     */
    public function user_on_unlock()
    {
        $data = input('param.');
        //更新用户信息
        $user_update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['wear_merit' => $data['id']]);
        if (!$user_update) {
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，佩戴失败！']);
        } else {
            return $this->json_rewrite(['status' => 'success', 'msg' => '佩戴成功！']);
        }
    }

    /**
     * 使用昵称
     */
    public function user_on_unlock_text()
    {
        $data = input('param.');
        //更新用户信息
        $user_update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['wear_special_id' => $data['id']]);
        if (!$user_update) {
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，装扮失败！']);
        } else {
            return $this->json_rewrite(['status' => 'success', 'msg' => '装扮成功！']);
        }
    }

    /**
     * 使用头像框
     */
    public function user_on_unlock_avatar()
    {
        $data = input('param.');
        //更新用户信息
        $user_update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['wear_af' => $data['id']]);
        if (!$user_update) {
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '系统错误，装扮失败！']);
        } else {
            return $this->json_rewrite(['status' => 'success', 'msg' => '装扮成功！']);
        }
    }

    /**
     * 获取所有任务
     */
    public function user_level()
    {
        $data = input('param.');
        //更新用户信息
        $list = Db::name('user_level')->where('much_id', $data['much_id'])->select();
        return $this->json_rewrite($list);
    }

    /**
     * 获取所有样式
     */
    public function get_text_style()
    {
        $data = input('param.');
        $info = Db::name('special_nickname')
            ->where('much_id', $data['much_id'])
            ->where('status', 1)
            ->order('scores')
            ->select();
        foreach ($info as $k => $v) {
            $info[$k]['unlock'] = Db::name('user_special_nickname')
                ->where('much_id', $data['much_id'])
                ->where('special_id', $v['id'])
                ->where('user_id', $this->user_info['id'])
                ->count();
            $info[$k]['wear_special_id'] = $this->user_info['wear_special_id'];
        }
        $yu = array('id' => 0, 'special_style' => 'yl_style0', 'special_name' => '默认', 'unlock' => 1);
        array_unshift($info, $yu);
        return $this->json_rewrite($info);
    }
}