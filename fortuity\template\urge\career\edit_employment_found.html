{extend name="/base"/}
{block name="main"}
<style>.el-input__inner{background:#fff !important}.content-wrapper{width:100%;min-height:100vh;background-color:#f5f7fa;padding:20px;margin-bottom:20px}.tpl-portlet-components{background:#fff;padding:30px;border-radius:8px;box-shadow:0 2px 12px rgba(0,0,0,0.05);max-width:900px;margin:20px auto}.am-form-group{padding:15px 0;border-bottom:1px solid #f5f5f5}.am-form-group:last-child{border-bottom:none}.am-form-label{color:#333;font-weight:500}.am-form input[type="text"],.am-form select{height:36px;border-radius:4px;border:1px solid #dcdfe6;transition:border-color .2s;width:100%;padding:0 12px}.am-form input[type="text"]:focus,.am-form select:focus{border-color:#409eff;outline:none}.am-form small{color:#909399;font-size:12px;margin-top:5px;display:inline-block}.am-btn-primary{background:#409eff;border-color:#409eff;padding:10px 30px;font-size:14px;transition:all .3s}.am-btn-primary:hover{background:#66b1ff;border-color:#66b1ff}.w-e-toolbar{border-color:#dcdfe6 !important;border-radius:4px 4px 0 0}.w-e-text-container{border-color:#dcdfe6 !important;border-radius:0 0 4px 4px;height:400px !important}.w-e-text{height:360px !important}.portlet-title{margin-bottom:30px;padding-bottom:15px;border-bottom:1px solid #ebeef5}.caption{font-size:18px;color:#303133}.caption .am-icon-code{color:#409eff;margin-right:8px}</style>
<div class="content-wrapper">
    <div id="app" class="tpl-portlet-components">
        <div class="portlet-title">
            <div class="caption font-green bold">
                <span class="am-icon-code"></span> 编辑求职招聘信息
            </div>
        </div>
        <div class="tpl-block ">
            <div class="am-g tpl-amazeui-form">
                <div class="am-u-sm-12 am-u-md-12">
                    <div class="am-form am-form-horizontal">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">发布用户UID</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="item.userId" placeholder="请输入发布用户UID" @input="getUserName">
                                <small><span style="margin-left: 10px;" v-html="tempUserName"></span></small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">岗位类型</label>
                            <div class="am-u-sm-9">
                                <select v-model="item.jobType">
                                    <option value="-1">请选择</option>
                                    <option v-for="type in typeOptions" :key="type.id" :value="type.id">{{type.name}}</option>
                                </select>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">发布类型</label>
                            <div class="am-u-sm-9">
                                <select v-model="item.releaseType">
                                    <option value="-1">请选择</option>
                                    <option value="0">招聘</option>
                                    <option value="1">求职</option>
                                    <option value="2">兼职</option>
                                    <option value="3">合伙人</option>
                                </select>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">岗位名称</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="item.jobName" placeholder="请输入岗位名称">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">岗位薪资</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="item.jobSalary" placeholder="请输入岗位薪资">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">岗位描述</label>
                            <div class="am-u-sm-9">
                                <div id="jobDescription"></div>
                                <span id="customizeGallery" style="display:none;" onclick="cuonice();"></span>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">工作地址</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="item.workAddress" placeholder="请输入工作地址">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">联系方式</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="item.contactDetails" placeholder="请输入联系方式">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">审核状态</label>
                            <div class="am-u-sm-9">
                                <select v-model="item.auditStatus">
                                    <option value="0">待审核</option>
                                    <option value="1">已通过</option>
                                    <option value="2">未通过</option>
                                </select>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <div class="am-u-sm-12" style="display: flex;justify-content: center;">
                                <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js?time={:time()}"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css?time={:time()}">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js?time={:time()}"></script>

<script>
    
    var vm = new Vue({
        el: '#app',
        data: {
            onLock: false,
            tempUserName: '',
            typeOptions: [],
            E: [],
            editor: [],
            listData: [],
            item: {
                userId: '',
                jobType: '-1',
                releaseType: '-1',
                jobName: '',
                jobSalary: '',
                jobDescription: '',
                workAddress: '',
                contactDetails: '',
                auditStatus: '1'
            }
        },
        created() {
            this.typeOptions = JSON.parse('{$typeList}');
            this.listData = JSON.parse(decodeURIComponent(atob('{$list}')));
            this.item = Object.assign(this.item, {
                userId: this.listData.user_id,
                jobType: this.listData.job_type,
                releaseType: this.listData.release_type.toString(),
                jobName: this.listData.job_name,
                jobSalary: this.listData.job_salary,
                workAddress: this.listData.work_address,
                contactDetails: this.listData.contact_details,
                auditStatus: this.listData.audit_status.toString()
            });
            this.getUserName();
        },
        mounted() {
            this.E = window.wangEditor;
            this.editor = new this.E('#jobDescription');
            this.editor.customConfig.uploadImgServer = true;
            this.editor.create();
            this.E.fullscreen.init('#jobDescription');
            this.editor.txt.html(this.listData.job_description);
        },
        methods: {
            getUserName() {
                if (this.item.userId) {
                    $.post("{:url('dramas/user_id_to_user_name')}", {uid: this.item.userId}, data => {
                        if (data.code > 0) {
                            this.tempUserName = '<span>用户昵称：' + data.userName + '</span>';
                        } else {
                            this.tempUserName = '<span style="color: red;">用户不存在或UID输入错误</span>';
                        }
                    })
                } else {
                    this.tempUserName = '';
                }
            },
            holdSave() {
                var setData = JSON.parse(JSON.stringify(this.item));
                setData.jobDescription = this.editor.txt.html();
                setData['fid'] = '{$Request.get.fid}';

                if (!setData.userId) {
                    layer.msg('请输入发布用户UID', {icon: 5, time: 2200});
                    return;
                }

                if (setData.jobType == '-1') {
                    layer.msg('请选择岗位类型', {icon: 5, time: 2200});
                    return;
                }

                if (setData.releaseType == '-1') {
                    layer.msg('请选择发布类型', {icon: 5, time: 2200});
                    return;
                }

                if (!setData.jobName) {
                    layer.msg('请输入岗位名称', {icon: 5, time: 2200});
                    return;
                }

                if (!setData.workAddress) {
                    layer.msg('请输入工作地址', {icon: 5, time: 2200});
                    return;
                }

                if (!setData.contactDetails) {
                    layer.msg('请输入联系方式', {icon: 5, time: 2200});
                    return;
                }

                this.onLock = true;
                $.post("{:url('career/edit_employment_found')}", setData, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.href = "{:url('career/employment_found')}";
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2200}, function () {
                            this.onLock = false;
                        });
                    }
                }, 'json');
            }
        }
    })

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0&pictureIndex=-1", 'no']
        });
    }

    var sutake = function (eurl, pictureIndex, type) {
        vm.editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
        layer.closeAll();
    }
</script>
{/block} 