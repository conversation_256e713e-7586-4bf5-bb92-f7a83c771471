<?php

namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

use think\Db;
use think\exception\HttpResponseException;

class Mimetic
{

    private static $miKey;

    private static function ___init()
    {
        self::$miKey = md5('asia' . strtotime(date('Y-m-d H:i')) . 'shanghai');
        return true;
    }

    /**
     * 初始化授权方法
     */
    public static function ___biochemical($key, $muchId)
    {
        if (self::___init() && $key !== self::$miKey) {
            return false;
        }
        $isTableExist = Db::query("SHOW TABLES LIKE 'yl_cloud_mystica';");
        if (!$isTableExist) {
            throw new HttpResponseException(response('初始化数据表缺失，需开发者协助处理！'));
        } else {
            //  查找合法表
            $tradeInfo = Db::table('yl_cloud_mystica')->where('id', 1)->value('trade');
            //  解密合法信息
            $tradeJson = authcode(trim($tradeInfo), 'DECODE', 'YuLuoVerifyTerraceManufacturerData');
            //  解密失败
            if (!$tradeJson) {
                throw new HttpResponseException(response('初始化数据表失败，请返回系统后重新进入-1！'));
            }
            //  授权信息转数组
            $tradeArray = json_decode($tradeJson, true);
            //  合法用户转数组
            $licenseId = json_decode($tradeArray['lawfulSign']['yl_welore']['licenseId'], true);
            //  如果当前时间超过授权时间 或者 域名不等于授权域名 或者 多用户标识不合法
            if ($tradeArray['deadline'] <= time() || request()->host() !== $tradeArray['bound'] || !in_array($muchId, $licenseId)) {
                throw new HttpResponseException(response('初始化数据表失败，请返回系统后重新进入-2！'));
            }
        }
    }

    /**
     * 默认版权设置
     */
    public static function ___defaultAnchoret()
    {
        $getCopyright = cache('globalRecluse');
        if (!$getCopyright) {
            $getCopyright = Db::name('copyright')->where('id', 1)->find();
            if (!$getCopyright) {
                Db::startTrans();
                try {
                    $getCopyright = ['id' => 1, 'hermit' => 0];
                    Db::name('copyright')->insert($getCopyright);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                }
            }
            cache('globalRecluse', $getCopyright);
        }
        return $getCopyright;
    }

    /**
     *  新增一个新的用户信息
     */
    public static function ___defaultAdvertise($muchId)
    {
        //  是否已存在默认流量主
        $getAdver = Db::name('advertise')->where('much_id', $muchId)->find();
        //  新增一个默认流量主
        if (!$getAdver) {
            $getAdver = ['adstory' => 0, 'adsper' => 0, 'pre_post_twig' => 0, 'lattice_twig' => 0, 'isolate' => 20, 'much_id' => $muchId];
            Db::startTrans();
            try {
                $getAdver['id'] = Db::name('advertise')->insertGetId($getAdver);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
        }
        return $getAdver;
    }

    /**
     * 新增一个网盘设置
     */
    public static function ___defaultNetDisc($muchId)
    {
        $ncInfo = Db::name('netdisc_config')->where('much_id', $muchId)->find();
        if (!$ncInfo) {
            $ncInfo = ['disk_size' => 20971520, 'upload_size_limit' => 10485760, 'upload_type_limited' => null, 'rel_paper_icon_hide' => 0, 'use_protocol' => '', 'much_id' => $muchId];
            Db::startTrans();
            try {
                $ncInfo['id'] = Db::name('netdisc_config')->insertGetId($ncInfo);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
        }
        return $ncInfo;
    }

    /**
     * 新增一个公众号配置
     */
    public static function ___defaultWxPopular($muchId)
    {
        $wxpInfo = Db::name('wx_popular')->where('much_id', $muchId)->find();
        if (!$wxpInfo) {
            $wxpInfo = ['much_id' => $muchId];
            Db::startTrans();
            try {
                $wxpInfo['id'] = Db::name('wx_popular')->insertGetId($wxpInfo);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
        }
        return $wxpInfo;
    }

    /**
     * 新增一个默认会员价格
     */
    public static function ___defaultHonorablePrice($muchId)
    {
        //  是否已存在初始会员价格
        $getHonr = Db::name('user_honorary')->where('much_id', $muchId)->find();
        if (!$getHonr) {
            //  新增一个初始会员价格
            $getHonr = [
                'chop_type' => 0,
                'hono_price' => 29.90,
                'first_discount' => 0,
                'discount_scale' => 1.00,
                'much_id' => $muchId
            ];
            Db::startTrans();
            try {
                $getHonr['id'] = Db::name('user_honorary')->insertGetId($getHonr);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
        }
        return $getHonr;
    }

    /**
     * 新增一个默认消息回执
     */
    public static function ___defaultPreCount($muchId)
    {
        $getPreCount = Db::name('prompt_count')->where('much_id', $muchId)->find();
        if (!$getPreCount) {
            $getPreCount = [
                'barg' => 0,
                'much_id' => $muchId
            ];
            Db::startTrans();
            try {
                $getPreCount['id'] = Db::name('prompt_count')->insertGetId($getPreCount);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
            cache('preCount_' . $muchId, $getPreCount);
        }
        return $getPreCount;
    }

    /**
     * 新增一个帖子转发
     */
    public static function ___defaultReissue($muchId)
    {
        $getReissue = Db::name('reissue')->where('much_id', $muchId)->find();
        if (!$getReissue) {
            $getReissue = [
                'whether_open' => 0,
                'title' => '您的好友给您转发了一条信息',
                'much_id' => $muchId
            ];
            Db::startTrans();
            try {
                $getReissue['id'] = Db::name('reissue')->insertGetId($getReissue);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
        }
        return $getReissue;
    }

    /**
     * 新增一个礼物税收
     */
    public static function ___defaultTaxing($muchId)
    {
        $getTaxing = Db::name('tribute_taxation')->where('much_id', $muchId)->find();
        if (!$getTaxing) {
            $getTaxing = [
                'taxing' => '1.00',
                'much_id' => $muchId
            ];
            Db::startTrans();
            try {
                $getTaxing['id'] = Db::name('tribute_taxation')->insertGetId($getTaxing);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
        }
        return $getTaxing;
    }

    /**
     * 新增一个默认积分获得范围
     */
    public static function ___defaultPunch($muchId)
    {
        $punch = Db::name('user_punch_range')->where('much_id', $muchId)->find();
        if (!$punch) {
            $punch = [
                'fraction_scale' => 10,
                'currency_icon' => '',
                'aver_min' => 0.00,
                'aver_max' => 0.00,
                'noble_min' => 0.00,
                'noble_max' => 0.00,
                'invite_min' => 0.00,
                'invite_max' => 0.00,
                'much_id' => $muchId,
            ];
            Db::startTrans();
            try {
                $punch['id'] = Db::name('user_punch_range')->insertGetId($punch);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
        }
        return $punch;
    }

    /**
     * 获取版权信息
     */
    public static function ___getKnight($muchId)
    {
        $knight = Db::name('authority')->where('much_id', $muchId)->find();
        if (!$knight) {
            $domain = explode(':', $_SERVER['HTTP_HOST']);
            $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
            $absRes = "https://{$domain[0]}{$absAddress[0]}static/disappear/icon.png";
            $knight = [
                'title' => '您还没有配置站点名称',
                'sgraph' => $absRes,
                'cust_phone' => '400-00000000',
                'copyright' => '版权信息',
                'much_id' => $muchId
            ];
            Db::startTrans();
            try {
                Db::name('authority')->insertGetId($knight);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
            $knight = Db::name('authority')->where('much_id', $muchId)->find();
        }
        return $knight;
    }


    /**
     * 新增一个帖子设置
     */
    public static function ___defaultPaperSmingle($muchId)
    {
        //  是否已存在初始帖子设置
        $getPaperSmingle = Db::name('paper_smingle')->where('much_id', $muchId)->find();
        if (!$getPaperSmingle) {
            //  新增一个初始帖子设置
            $getPaperSmingle = [
                'auto_review' => 1,
                'number_limit' => 0,
                'custom_hiss_title' => '树洞',
                'buy_paper_auto_review' => 0,
                'buy_paper_number_limit' => 0,
                'buy_paper_taxing' => 0.50,
                'notice' => '',
                'much_id' => $muchId
            ];
            Db::startTrans();
            try {
                $getPaperSmingle['id'] = Db::name('paper_smingle')->insertGetId($getPaperSmingle);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
        }
        return $getPaperSmingle;
    }

    /**
     * 默认底部导航初始化
     * @param $muchId
     */
    public static function ___defaultNavigate($muchId)
    {
        $navigate = cache('design_' . $muchId);
        if (!$navigate) {
            $navigate = db('design')->where('much_id', $muchId)->find();
            if (!$navigate) {
                $domain = explode(':', $_SERVER['HTTP_HOST']);
                $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
                $absRess = "https://{$domain[0]}{$absAddress[0]}static/wechat";
                $navigate = [
                    'confer' => '积分',
                    'currency' => '贝壳',
                    'landgrave' => '圈子',
                    'mall' => '贝壳商城',
                    'home_title' => '首页',
                    'elect_sheathe' => 0,
                    'pattern_data' => json_encode([
                        'style' => [
                            'backcolor' => '#ffffff',
                            'font_color' => '#000000',
                            'font_color_active' => '#ff0000'
                        ], 'home' => [
                            'title' => '首页',
                            'images' => [
                                'img' => "{$absRess}/home.png",
                                'img_active' => "{$absRess}/home_active.png",
                            ]
                        ], 'plaza' => [
                            'title' => '广场',
                            'images' => [
                                'img' => "{$absRess}/plaza.png",
                                'img_active' => "{$absRess}/plaza_active.png",
                            ]
                        ], 'release' => [
                            'title' => '发布',
                            'images' => [
                                'img' => "{$absRess}/release.png",
                            ],
                            'list' => [
                                'writing' => [
                                    'title' => '发图文',
                                    'images' => "{$absRess}/writing.png"
                                ],
                                'audio' => [
                                    'title' => '发语音',
                                    'images' => "{$absRess}/audio.png"
                                ],
                                'graffito' => [
                                    'title' => '发投票',
                                    'images' => "{$absRess}/graffito.png"
                                ],
                                'video' => [
                                    'title' => '发视频',
                                    'images' => "{$absRess}/video.png"
                                ],
                                'brisk' => [
                                    'title' => '发活动',
                                    'images' => "{$absRess}/brisk.png"
                                ]
                            ]
                        ], 'goods' => [
                            'title' => '小商品',
                            'images' => [
                                'img' => "{$absRess}/goods.png",
                                'img_active' => "{$absRess}/goods_active.png",
                            ]
                        ], 'user' => [
                            'title' => '我的',
                            'images' => [
                                'img' => "{$absRess}/user.png",
                                'img_active' => "{$absRess}/user_active.png",
                            ]
                        ]
                    ], 320),
                    'much_id' => $muchId
                ];
                Db::startTrans();
                try {
                    $navigate['id'] = db('design')->insertGetId($navigate);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                }
            }
            cache('design_' . $muchId, $navigate);
        }
        return $navigate;
    }

    /*
     * 校验
     */
    public static function ___outrage($key, $muchId)
    {
        if (self::___init() && $key !== self::$miKey) {
            return false;
        }
        $mercy = Suspense::silence();
        $regret = MagicTrick::pinafore();
        $magicHatOpening = MagicTrick::headpiece();
        $remorse = false;
        if ($regret == $magicHatOpening) {
            PrivateCache::clearProjectCache();
            $remorse = true;
        }
        if ($remorse) {
            $surge = Suspense::silence();
        } else {
            $surge = $mercy;
        }
        if ($mercy !== $surge) {
            $regret = MagicTrick::pinafore();
            Pisces::slothful($muchId);
        }
        $magicHatSecond = MagicTrick::chapeau();
        if ($regret == $magicHatSecond) {
            return true;
        } else {
            return false;
        }
    }

}