<?php

namespace app\api\controller;

class User extends Base
{
    /**
     * 无用方法
     */
    public function book()
    {
        return 1;
    }

    /**
     * Home
     */
    //获取用户信息
    public function get_user_info()
    {
        $Home = new Home();
        return $Home->get_user_info();
    }
    public function get_article_user_info(){
        $Home = new Home();
        return $Home->get_article_user_info();
    }
    public function get_article_authority(){
        $Home = new Home();
        return $Home->get_article_authority();
    }
    //用户签到
    public function add_user_punch()
    {
        $Home = new Home();
        return $Home->add_user_punch();
    }

    //编辑资料
    public function edit_user_info()
    {
        $Home = new Home();
        return $Home->edit_user_info();
    }

    //获取指定会员信息
    public function get_user_info_my()
    {
        $Home = new Home();
        return $Home->get_user_info_my();
    }

    //关注取消 会员
    public function get_user_cancel()
    {
        $Home = new Home();
        return $Home->get_user_cancel();
    }

    //获取会员金币明细
    public function get_user_amount()
    {
        $Home = new Home();
        return $Home->get_user_amount();
    }

    //我收到的礼物
    public function get_my_rec()
    {
        $Home = new Home();
        return $Home->get_my_rec();
    }

    //获取用户的礼物排行榜
    public function get_user_guard()
    {
        $Home = new Home();
        return $Home->get_user_guard();
    }

    //获取最高人数的几个广场
    public function get_all_needle()
    {
        $Home = new Home();
        return $Home->get_all_needle();
    }

    //获取搜索内容
    public function get_search_list()
    {
        $Home = new Home();
        return $Home->get_search_list();
    }

    //获取推荐圈子
    public function get_tj_list()
    {
        $Home = new Home();
        return $Home->get_tj_list();
    }

    //获取关注或者粉丝
    public function get_follow_fansi()
    {
        $Home = new Home();
        return $Home->get_follow_fansi();
    }

    //图片上传
    public function img_upload()
    {
        $Home = new Home();
        return $Home->img_upload();
    }

    //缓存数组排序
    public function set_arr_dx()
    {
        $Home = new Home();
        return $Home->set_arr_dx();
    }

    //base64
    public function base64EncodeImage()
    {
        $Home = new Home();
        return $Home->base64EncodeImage();
    }

    //获取小程序二维码（帖子）
    public function qrcode()
    {
        $Home = new Home();
        return $Home->qrcode();
    }

    //获取小程序二维码（邀请页面）
    public function qrcode_code()
    {
        $Home = new Home();
        return $Home->qrcode_code();
    }

    //保存formID
    public function add_form_id()
    {
        $Home = new Home();
        return $Home->add_form_id();
    }

    //获取用户邀请码
    public function ger_user_code()
    {
        $Home = new Home();
        return $Home->ger_user_code();
    }

    //输入验证码提交
    public function add_user_invitation()
    {
        $Home = new Home();
        return $Home->add_user_invitation();
    }
    /**
     * Home
     */

    /**
     * index
     */
    //获取首页数据
    public function get_index_list()
    {
        $index = new Index();
        return $index->get_index_list();
    }
    //获取首页推荐
    public function get_index_tj_list()
    {
        $index = new Index();
        return $index->get_index_tj_list();
    }

    //获取首页我关注的数据
    public function get_my_index_list()
    {
        $index = new Index();
        return $index->get_my_index_list();
    }

    //获取自己首页数据
    public function get_my_list()
    {
        $index = new Index();
        return $index->get_my_list();
    }

    //获取自己首页数据
    public function get_my_list_sh()
    {
        $index = new Index();
        return $index->get_my_list_sh();
    }

    //获取审核数据
    public function get_index_list_admin(){
        $index = new Index();
        return $index->get_index_list_admin();
    }

    //超管审核帖子
    public function set_paper_status(){
        $index = new Index();
        return $index->set_paper_status();
    }
    //获取帖子详情
    public function get_article_info()
    {
        $index = new Index();
        return $index->get_article_info();
    }

    //回复列表
    public function get_article_huifu()
    {
        $index = new Index();
        return $index->get_article_huifu();
    }

    //回复帖子
    public function add_paper_reply_new()
    {
        $index = new Index();
        return $index->add_paper_reply_new();

    }
    /**
     * index
     */

    /**
     * Message
     */
    //获取疑难解答列表
    public function get_help_info()
    {
        $Message = new Message();
        return $Message->get_help_info();
    }

    //获取疑难解答详情
    public function get_help_info_desc()
    {
        $Message = new Message();
        return $Message->get_help_info_desc();
    }

    //获取站点信息
    public function get_authority()
    {
        $Message = new Message();
        return $Message->get_authority();
    }

    //获取转发详情
    public function get_forward()
    {
        $Message = new Message();
        return $Message->get_forward();
    }

    //获取禁言列表
    public function get_user_banned()
    {
        $Message = new Message();
        return $Message->get_user_banned();
    }

    //申诉禁言
    public function do_user_mutter()
    {
        $Message = new Message();
        return $Message->do_user_mutter();
    }

    //获取发帖须知
    public function get_post_notice()
    {
        $Message = new Message();
        return $Message->get_post_notice();
    }

    //获取站内信消息
    public function get_user_smail()
    {
        $Message = new Message();
        return $Message->get_user_smail();
    }

    //更新为已读
    public function up_user_smail()
    {
        $Message = new Message();
        return $Message->up_user_smail();
    }

    //删除站内信
    public function del_user_smail()
    {
        $Message = new Message();
        return $Message->del_user_smail();
    }

    //站内信全部标为已读
    public function up_user_smail_all()
    {
        $Message = new Message();
        return $Message->up_user_smail_all();
    }

    //获取流量主
    public function get_ad()
    {
        $Message = new Message();
        return $Message->get_ad();
    }

    //获取diy数据
    public function get_diy()
    {
        $Message = new Message();
        return $Message->get_diy();
    }
    /**
     * Message
     */
    /**
     * 发帖
     */
    public function add_circle_new()
    {
        $Artile = new Article();
        return $Artile->add_circle_new();
    }

    //收藏帖子
    public function add_user_collect()
    {
        $Artile = new Article();
        return $Artile->add_user_collect();
    }

    //获取收藏帖子
    public function get_user_collection()
    {
        $Artile = new Article();
        return $Artile->get_user_collection();
    }

    //举报帖子
    public function add_paper_complaint()
    {
        $Artile = new Article();
        return $Artile->add_paper_complaint();
    }

    //回复点赞
    public function add_paper_prely()
    {
        $Artile = new Article();
        return $Artile->add_paper_prely();
    }

    //删除帖子
    public function del_article()
    {

        $Artile = new Article();
        return $Artile->del_article();
    }

    //删除回复
    public function del_article_huifu()
    {
        $Artile = new Article();
        return $Artile->del_article_huifu();
    }

    //赞帖子
    public function add_user_zan()
    {
        $Artile = new Article();
        return $Artile->add_user_zan();
    }

    //帖子置顶
    public function placement()
    {
        $Artile = new Article();
        return $Artile->placement();
    }

    //获取评论详情
    public function get_paper_reply_info()
    {
        $Artile = new Article();
        return $Artile->get_paper_reply_info();
    }

    //发布评论回复
    public function add_paper_reply_duplex()
    {
        $Artile = new Article();
        return $Artile->add_paper_reply_duplex();
    }
    //推荐
    public function set_essence()
    {
        $Artile = new Article();
        return $Artile->set_essence();
    }
    //取消推荐
    public function del_essence()
    {
        $Artile = new Article();
        return $Artile->del_essence();
    }
    /**
     * Article
     */

    /**
     * Circle
     */
    //获取该圈子置顶帖子
    public function get_placement_top()
    {
        $Circle = new Circle();
        return $Circle->get_placement_top();
    }

    //获取全部圈子
    public function get_left_needle()
    {
        $Circle = new Circle();
        return $Circle->get_left_needle();
    }

    //获取右边圈子
    public function get_right_needle()
    {
        $Circle = new Circle();
        return $Circle->get_right_needle();
    }

    //加入圈子
    public function set_user_trailing()
    {
        $Circle = new Circle();
        return $Circle->set_user_trailing();
    }

    //圈子详情
    public function get_tory_info()
    {
        $Circle = new Circle();
        return $Circle->get_tory_info();
    }

    //申请圈子
    public function add_territory_petition()
    {
        $Circle = new Circle();
        return $Circle->add_territory_petition();
    }

    //获取申请加入圈子信息
    public function get_user_territory_interest()
    {
        $Circle = new Circle();
        return $Circle->get_user_territory_interest();
    }

    //同意加入圈子
    public function add_territory_interest()
    {
        $Circle = new Circle();
        return $Circle->add_territory_interest();
    }

    //申请圈主或者管理员
    public function add_territory_learned()
    {
        $Circle = new Circle();
        return $Circle->add_territory_learned();

    }

    //获取圈子资料，圈主，小圈住
    public function get_qq_info()
    {
        $Circle = new Circle();
        return $Circle->get_qq_info();
    }

    //获取我管理的圈子
    public function user_mastert()
    {
        $Circle = new Circle();
        return $Circle->user_mastert();
    }

    //获取圈子申请管理员
    public function get_envite_sulord()
    {
        $Circle = new Circle();
        return $Circle->get_envite_sulord();
    }

    //添加管理员
    public function add_envite_sulord()
    {
        $Circle = new Circle();
        return $Circle->add_envite_sulord();
    }

    //投诉管理员，圈主，圈子，用户
    public function add_tc_submit()
    {
        $Circle = new Circle();
        return $Circle->add_tc_submit();
    }

    //打开或关闭暗号
    public function open_atence()
    {
        $Circle = new Circle();
        return $Circle->open_atence();
    }

    //修改暗号
    public function update_atcipher()
    {
        $Circle = new Circle();
        return $Circle->update_atcipher();
    }
    public function get_ah_random(){
        $Circle = new Circle();
        return $Circle->get_ah_random();
    }
    /**
     * Circle
     */

    /**
     * Order
     */
    //获取商品类型
    public function get_shop_type()
    {
        $Order = new Order();
        return $Order->get_shop_type();
    }

    //获取商品列表
    public function get_shop_list()
    {
        $Order = new Order();
        return $Order->get_shop_list();
    }

    //获取商品详情
    public function get_goods()
    {
        $Order = new Order();
        return $Order->get_goods();
    }

    //兑换物品检测
    public function exchange_goods()
    {
        $Order = new Order();
        return $Order->exchange_goods();
    }

    //兑换物品
    public function exchange_goods_do()
    {
        $Order = new Order();
        return $Order->exchange_goods_do();
    }

    //获取订单列表
    public function get_order_list()
    {
        $Order = new Order();
        return $Order->get_order_list();
    }

    //获取我的订单详情
    public function get_my_order()
    {
        $Order = new Order();
        return $Order->get_my_order();
    }

    //退款
    public function order_refund()
    {
        $Order = new Order();
        return $Order->order_refund();
    }

    //取消退款
    public function refund_del_do()
    {
        $Order = new Order();
        return $Order->refund_del_do();
    }

    //确认收货
    public function ok_mod_do()
    {
        $Order = new Order();
        return $Order->ok_mod_do();
    }
    /**
     * Order
     */

    /**
     * Withdraw
     */
    //提现
    public function withdraw()
    {
        $Withdraw = new Withdraw();
        return $Withdraw->withdraw();
    }

    //获取提现配置
    public function get_raws_setting()
    {
        $Withdraw = new Withdraw();
        return $Withdraw->get_raws_setting();
    }

    //获取提现列表
    public function get_withdraw_list()
    {
        $Withdraw = new Withdraw();
        return $Withdraw->get_withdraw_list();
    }

    //获取礼物
    public function get_liwu()
    {
        $Withdraw = new Withdraw();
        return $Withdraw->get_liwu();
    }

    //获取会员充值
    public function get_user_honorary()
    {
        $Withdraw = new Withdraw();
        return $Withdraw->get_user_honorary();
    }

    //给用户送礼
    public function user_reward()
    {
        $Withdraw = new Withdraw();
        return $Withdraw->user_reward();
    }
    public function user_reward_new()
    {
        $Withdraw = new Withdraw();
        return $Withdraw->user_reward_new();
    }

    //积分兑换贝壳
    public function get_ji_bei()
    {
        $Withdraw = new Withdraw();
        return $Withdraw->get_ji_bei();
    }

    //贝壳兑换积分
    public function add_bei_ji()
    {
        $Withdraw = new Withdraw();
        return $Withdraw->add_bei_ji();
    }
    //支付付费贴
    public function do_paper_money_new()
    {
        $Withdraw = new Withdraw();
        return $Withdraw->do_paper_money_new();
    }


    /**
     * Service
     */
    //禁言
    public function do_banned_user()
    {
        $Service = new Service();
        return $Service->do_banned_user();
    }

    //获取删除帖子/回复/打回
    public function get_user_paper_del()
    {
        $Service = new Service();
        return $Service->get_user_paper_del();
    }

    //申诉帖子/回复
    public function do_paper_mutter()
    {
        $Service = new Service();
        return $Service->do_paper_mutter();
    }

    //获取投诉记录
    public function get_user_report()
    {
        $Service = new Service();
        return $Service->get_user_report();
    }

    //获取禁言列表(取消禁言用)
    public function get_user_banned_qq()
    {
        $Service = new Service();
        return $Service->get_user_banned_qq();
    }

    //解除禁言
    public function jie_user_banned()
    {
        $Service = new Service();
        return $Service->jie_user_banned();
    }

    //关进小黑屋
    public function get_user_status()
    {

        $Service = new Service();
        return $Service->get_user_status();
    }

    //绑定手机号
    public function get_user_phone()
    {
        $Service = new Service();
        return $Service->get_user_phone();
    }
    /**
     * Service
     */

    /**
     * Conversation
     */
    public function get_gambit(){
        $Conversation = new Conversation();
        return $Conversation->get_gambit();
    }
    public function add_gambit(){
        $Conversation = new Conversation();
        return $Conversation->add_gambit();
    }
    public function get_gambit_list(){
        $Conversation = new Conversation();
        return $Conversation->get_gambit_list();
    }
    public function add_private(){
        $Conversation = new Conversation();
        return $Conversation->add_private();
    }
    public function get_private_list(){
        $Conversation = new Conversation();
        return $Conversation->get_private_list();
    }
    public function del_private(){
        $Conversation = new Conversation();
        return $Conversation->del_private();
    }
    public function get_my_private(){
        $Conversation = new Conversation();
        return $Conversation->get_my_private();
    }
    public function del_my_contacts(){
        $Conversation = new Conversation();
        return $Conversation->del_my_contacts();
    }
    public function lahei_do(){
        $Conversation = new Conversation();
        return $Conversation->lahei_do();
    }
    public function add_jubao(){
        $Conversation = new Conversation();
        return $Conversation->add_jubao();
    }
    public function get_black_list(){
        $Conversation = new Conversation();
        return $Conversation->get_black_list();
    }
    public function del_black(){
        $Conversation = new Conversation();
        return $Conversation->del_black();
    }
    /**
     * 报名活动
     */
    public function participation_activities(){
        $Activity = new Activity();
        return $Activity->participation_activities();
    }
    public function get_card_list(){
        $Activity = new Activity();
        return $Activity->get_card_list();
    }
    public function ok_click(){
        $Activity = new Activity();
        return $Activity->ok_click();
    }
    public function get_my_brisk_team(){
        $Activity = new Activity();
        return $Activity->get_my_brisk_team();
    }
    public function get_verified(){
        $Activity = new Activity();
        return $Activity->get_verified();
    }
}