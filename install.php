<?php
$sql = <<<EOT
CREATE TABLE IF NOT EXISTS `yl_welore_advertise` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `adstory` int(11) unsigned NOT NULL DEFAULT '0',
  `incentive_duct` int(11) unsigned NOT NULL DEFAULT '5',
  `adsper` int(11) unsigned NOT NULL DEFAULT '0',
  `pre_post_twig` int(11) unsigned NOT NULL DEFAULT '0',
  `isolate` int(11) unsigned NOT NULL DEFAULT '20',
  `adunit_id` varchar(255) DEFAULT NULL,
  `incentive_id` varchar(255) DEFAULT NULL,
  `pre_post_id` varchar(255) DEFAULT NULL,
  `lattice_twig` int(11) unsigned NOT NULL DEFAULT '0',
  `lattice_id` varchar(255) DEFAULT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_advertise`;
CREATE TABLE IF NOT EXISTS `yl_welore_attest` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `at_name` varchar(255) NOT NULL,
  `at_icon` varchar(2000) NOT NULL,
  `handsel_day` int(11) unsigned NOT NULL DEFAULT '0',
  `custom_form` longtext NOT NULL,
  `introduction` longtext NOT NULL,
  `status` int(11) unsigned NOT NULL,
  `scores` int(11) NOT NULL DEFAULT '0',
  `create_time` int(11) unsigned NOT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_attest`;
CREATE TABLE IF NOT EXISTS `yl_welore_authority` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(500) DEFAULT NULL,
  `sgraph` varchar(500) DEFAULT NULL,
  `cust_phone` varchar(255) DEFAULT NULL,
  `copyright` varchar(500) DEFAULT NULL,
  `prevent_duplication` int(11) unsigned NOT NULL DEFAULT '1',
  `home_random_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `home_release_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `video_auto_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `allow_user_topic` int(11) unsigned NOT NULL DEFAULT '1',
  `home_my_tory_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `tory_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `tory_sort_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `reprint_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `welfare_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `shop_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `noble_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `wallet_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `recharge_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `ios_pay_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `tribute_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `guard_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `buy_paper_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `pre_content_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `speech_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `ensure_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `rel_paper_img_style` int(11) unsigned NOT NULL DEFAULT '0',
  `hair_audio_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `hair_graffiti_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `hair_video_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `hair_brisk_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `hair_vote_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `receipt_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `force_phone_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `re_force_phone_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `overall_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `warrant_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `title_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `title_input_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `video_member` int(11) unsigned NOT NULL DEFAULT '0',
  `voice_member` int(11) unsigned NOT NULL DEFAULT '0',
  `graffiti_member` int(11) unsigned NOT NULL DEFAULT '0',
  `brisk_member` int(11) unsigned NOT NULL DEFAULT '0',
  `vote_member` int(11) unsigned NOT NULL DEFAULT '0',
  `buy_paper_member` int(11) unsigned NOT NULL DEFAULT '0',
  `user_info_update_arbor` int(11) unsigned NOT NULL DEFAULT '1',
  `whisper_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `engrave_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `travel_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `feeling_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `short_drama_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `video_setting` int(11) unsigned NOT NULL DEFAULT '60',
  `video_compression_setting` int(11) unsigned NOT NULL DEFAULT '1',
  `fraction_convert` int(11) unsigned NOT NULL DEFAULT '0',
  `conch_convert` int(11) unsigned NOT NULL DEFAULT '0',
  `paper_browse_num_hide` int(11) unsigned NOT NULL DEFAULT '0',
  `rel_paper_location_hide` int(11) unsigned NOT NULL DEFAULT '0',
  `rel_paper_topicsd_hide` int(11) unsigned NOT NULL DEFAULT '0',
  `rel_paper_image_hide` int(11) unsigned NOT NULL DEFAULT '0',
  `video_download_arbor` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_authority`;
CREATE TABLE IF NOT EXISTS `yl_welore_avatar_frame` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `adorn_icon` varchar(1000) NOT NULL,
  `adorn_name` varchar(1000) NOT NULL,
  `unlock_fraction` decimal(22,2) unsigned NOT NULL,
  `prepare_time` int(11) unsigned NOT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '1',
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `purge_time` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`),
  KEY `scores` (`scores`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_avatar_frame`;
CREATE TABLE IF NOT EXISTS `yl_welore_brisk_team` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) unsigned NOT NULL,
  `is_approve` int(11) unsigned NOT NULL DEFAULT '0',
  `brisk_address` varchar(1000) NOT NULL,
  `brisk_address_latitude` varchar(500) NOT NULL,
  `brisk_address_longitude` varchar(500) NOT NULL,
  `start_time` int(11) unsigned NOT NULL,
  `end_time` int(11) unsigned NOT NULL,
  `number_of_people` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `paper_id` (`paper_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_brisk_team`;
CREATE TABLE IF NOT EXISTS `yl_welore_call_phone_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `force_input_phone` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_call_phone_config`;
CREATE TABLE IF NOT EXISTS `yl_welore_camouflage_card` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `forgery_name` varchar(255) NOT NULL,
  `forgery_head` varchar(2000) NOT NULL,
  `unlock_fraction` decimal(22,2) unsigned NOT NULL,
  `cost_day` int(11) unsigned NOT NULL,
  `scores` int(11) NOT NULL DEFAULT '0',
  `making_time` int(11) unsigned NOT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_camouflage_card`;
CREATE TABLE IF NOT EXISTS `yl_welore_combination` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `home` longtext NOT NULL,
  `plaza` longtext NOT NULL,
  `goods` longtext NOT NULL,
  `user` longtext NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_combination`;
CREATE TABLE IF NOT EXISTS `yl_welore_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_name` text,
  `app_id` text,
  `app_secret` text,
  `app_mchid` text,
  `app_key` text,
  `app_key_v3` text,
  `pay_react` text,
  `certificate_serial_number` text,
  `version_type` int(11) unsigned NOT NULL DEFAULT '0',
  `apiclient_cert` text,
  `apiclient_key` text,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`),
  KEY `id` (`id`),
  KEY `much_id_2` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_config`;
CREATE TABLE IF NOT EXISTS `yl_welore_contrar` (
  `id` int(10) unsigned NOT NULL,
  `rand_code` varchar(1000) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

TRUNCATE TABLE `yl_welore_contrar`;
CREATE TABLE IF NOT EXISTS `yl_welore_copyright` (
  `id` int(11) NOT NULL,
  `hermit` int(11) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `hermit` (`hermit`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

TRUNCATE TABLE `yl_welore_copyright`;
CREATE TABLE IF NOT EXISTS `yl_welore_design` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `confer` varchar(500) DEFAULT NULL,
  `currency` varchar(500) DEFAULT NULL,
  `landgrave` varchar(500) DEFAULT NULL,
  `mall` varchar(500) DEFAULT NULL,
  `elect_sheathe` int(11) unsigned NOT NULL DEFAULT '0',
  `home_title` varchar(500) DEFAULT NULL,
  `pattern_data` longtext,
  `much_id` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_design`;
CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `custom_title` varchar(255) NOT NULL,
  `is_show_btn` int(11) unsigned NOT NULL DEFAULT '0',
  `btn_icon` varchar(2000) NOT NULL,
  `waiter_qrcode` varchar(2000) NOT NULL,
  `precautions` longtext NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_easy_info_config`;
CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_type` int(11) unsigned NOT NULL,
  `merchant_name` varchar(500) NOT NULL,
  `merchant_icon_carousel` text NOT NULL,
  `address_name` varchar(500) NOT NULL,
  `address_longitude` varchar(255) NOT NULL,
  `address_latitude` varchar(255) NOT NULL,
  `merchant_phone` varchar(255) NOT NULL,
  `merchant_introduce` longtext NOT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '1',
  `create_time` int(11) unsigned NOT NULL,
  `sort` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_easy_info_list`;
CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_shop_assistant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `eil_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '1',
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_easy_info_shop_assistant`;
CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_shop_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `product_id` int(11) unsigned NOT NULL,
  `eil_id` int(11) unsigned NOT NULL,
  `so_id` int(11) unsigned NOT NULL,
  `redemption_code` varchar(255) NOT NULL,
  `use_status` int(11) unsigned NOT NULL DEFAULT '0',
  `order_status` int(11) unsigned NOT NULL DEFAULT '1',
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_easy_info_shop_order`;
CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_shop_order_verify_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `eiso_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `eil_id` int(11) unsigned NOT NULL,
  `so_id` int(11) unsigned NOT NULL,
  `verify_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_easy_info_shop_order_verify_log`;
CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_shop_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) unsigned NOT NULL,
  `eil_id` int(11) unsigned NOT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_easy_info_shop_products`;
CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `icon` varchar(2000) NOT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '1',
  `sort` int(11) NOT NULL DEFAULT '0',
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_easy_info_type`;
CREATE TABLE IF NOT EXISTS `yl_welore_employment_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `job_type` int(11) unsigned NOT NULL,
  `release_type` int(11) unsigned NOT NULL,
  `job_name` varchar(500) NOT NULL,
  `job_salary` varchar(255) DEFAULT NULL,
  `job_description` longtext NOT NULL,
  `work_address` varchar(1000) NOT NULL,
  `contact_details` varchar(500) NOT NULL,
  `top_time` int(11) unsigned NOT NULL DEFAULT '0',
  `audit_status` int(11) unsigned NOT NULL DEFAULT '0',
  `audit_reason` varchar(500) DEFAULT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_employment_item`;
CREATE TABLE IF NOT EXISTS `yl_welore_employment_item_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `custom_title` varchar(255) NOT NULL,
  `is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
  `reply_is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
  `top_twig` int(11) unsigned NOT NULL DEFAULT '0',
  `price_type` int(11) unsigned NOT NULL,
  `top_price` decimal(22,2) unsigned NOT NULL,
  `help_document` longtext NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_employment_item_config`;
CREATE TABLE IF NOT EXISTS `yl_welore_employment_item_top` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usl_id` int(11) unsigned NOT NULL DEFAULT '0',
  `ei_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `top_day` int(11) unsigned NOT NULL,
  `pay_type` int(11) unsigned NOT NULL,
  `pay_price` decimal(22,2) unsigned NOT NULL,
  `add_time` int(11) unsigned NOT NULL,
  `is_pay` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_employment_item_top`;
CREATE TABLE IF NOT EXISTS `yl_welore_employment_item_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '1',
  `sort` int(11) NOT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_employment_item_type`;
CREATE TABLE IF NOT EXISTS `yl_welore_employment_user_attention` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `ei_id` int(11) unsigned NOT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_employment_user_attention`;
CREATE TABLE IF NOT EXISTS `yl_welore_event_raffle` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `er_name` varchar(500) NOT NULL,
  `start_time` int(11) unsigned NOT NULL,
  `end_time` int(11) unsigned NOT NULL,
  `deplete_type` int(11) unsigned NOT NULL,
  `deplete_score` decimal(18,0) unsigned NOT NULL,
  `illustrate` longtext NOT NULL,
  `prize_content` text NOT NULL,
  `turning_speed` int(11) unsigned NOT NULL,
  `turntable_style` int(11) unsigned NOT NULL,
  `free_chance` int(11) unsigned NOT NULL,
  `free_ad_valve` int(11) unsigned NOT NULL DEFAULT '0',
  `draw_restrictions` int(11) unsigned NOT NULL,
  `define_time` int(11) unsigned NOT NULL,
  `status` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_event_raffle`;
CREATE TABLE IF NOT EXISTS `yl_welore_feeling` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `contact_person` varchar(255) NOT NULL,
  `age` int(11) unsigned NOT NULL,
  `gender` int(11) unsigned NOT NULL,
  `constellation` int(11) unsigned NOT NULL,
  `remain_city` varchar(255) NOT NULL,
  `restrict_city` varchar(255) NOT NULL DEFAULT '0',
  `hedge_content` longtext NOT NULL,
  `longevity` int(11) unsigned NOT NULL DEFAULT '0',
  `create_time` int(11) unsigned NOT NULL,
  `check_status` int(11) unsigned NOT NULL DEFAULT '0',
  `check_time` int(11) unsigned NOT NULL DEFAULT '0',
  `check_opinion` varchar(500) DEFAULT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_feeling`;
CREATE TABLE IF NOT EXISTS `yl_welore_feeling_pay` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(255) NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `fg_id` int(11) unsigned NOT NULL,
  `pay_type` int(11) unsigned NOT NULL,
  `pay_status` int(11) unsigned NOT NULL DEFAULT '0',
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_feeling_pay`;
CREATE TABLE IF NOT EXISTS `yl_welore_feeling_stipulate` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `custom_title` varchar(255) DEFAULT NULL,
  `bare_location` int(11) unsigned NOT NULL DEFAULT '0',
  `bare_direction` int(11) unsigned NOT NULL DEFAULT '1',
  `direction_bottom` int(11) unsigned NOT NULL DEFAULT '20',
  `bare_img_url` varchar(2000) NOT NULL,
  `auto_careful` int(11) unsigned NOT NULL DEFAULT '0',
  `throw_price_male` decimal(18,2) unsigned NOT NULL,
  `throw_price_female` decimal(18,2) unsigned NOT NULL,
  `pick_price_male` decimal(18,2) unsigned NOT NULL,
  `pick_price_female` decimal(18,2) unsigned NOT NULL,
  `pay_type` int(11) unsigned NOT NULL,
  `throw_limit` int(11) unsigned NOT NULL DEFAULT '0',
  `pick_limit` int(11) unsigned NOT NULL DEFAULT '0',
  `ten_local_key` varchar(255) NOT NULL,
  `notice` longtext NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_feeling_stipulate`;
CREATE TABLE IF NOT EXISTS `yl_welore_gallery` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `classify_id` int(11) unsigned DEFAULT NULL,
  `img_title` varchar(1000) DEFAULT NULL,
  `img_url` varchar(1000) DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `classify_id` (`classify_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_gallery`;
CREATE TABLE IF NOT EXISTS `yl_welore_gallery_classify` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(500) NOT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '1',
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`),
  KEY `scores` (`scores`),
  KEY `status` (`status`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_gallery_classify`;
CREATE TABLE IF NOT EXISTS `yl_welore_gambit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `gambit_name` varchar(500) NOT NULL,
  `add_time` int(11) unsigned NOT NULL,
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_gambit`;
CREATE TABLE IF NOT EXISTS `yl_welore_help` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trouble` varchar(1000) DEFAULT NULL,
  `answer` longtext,
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `time` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_help`;
CREATE TABLE IF NOT EXISTS `yl_welore_home_topping` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) unsigned DEFAULT NULL,
  `style_type` int(11) unsigned NOT NULL DEFAULT '0',
  `top_time` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `paper_id` (`paper_id`),
  KEY `top_time` (`top_time`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_home_topping`;
CREATE TABLE IF NOT EXISTS `yl_welore_lament` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `proof_id` int(11) unsigned DEFAULT NULL,
  `mopt_id` int(11) unsigned DEFAULT NULL,
  `ment_type` int(11) unsigned DEFAULT NULL,
  `tory_id` int(11) unsigned DEFAULT NULL,
  `labor` int(11) DEFAULT NULL,
  `user_id` int(11) unsigned DEFAULT NULL,
  `ment_caption` longtext,
  `status` int(11) NOT NULL DEFAULT '0',
  `ment_time` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ment_type` (`ment_type`),
  KEY `much_id` (`much_id`),
  KEY `user_id` (`user_id`),
  KEY `labor` (`labor`),
  KEY `status` (`status`),
  KEY `mopt_id` (`mopt_id`),
  KEY `tory_id` (`tory_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_lament`;
CREATE TABLE IF NOT EXISTS `yl_welore_login_checking` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_name` varchar(500) NOT NULL,
  `role` varchar(500) NOT NULL,
  `access_type` tinyint(1) NOT NULL DEFAULT '0',
  `session_token` varchar(128) NOT NULL,
  `previous_session_token` varchar(128) DEFAULT NULL,
  `token_rotated_at` int(11) unsigned DEFAULT NULL,
  `token_last_refreshed_at` int(10) NOT NULL DEFAULT '0',
  `session_fingerprint` varchar(255) NOT NULL,
  `login_ip` varchar(500) NOT NULL,
  `login_time` int(11) unsigned NOT NULL,
  `uniacid` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_session` (`user_name`(255),`role`(255),`uniacid`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_login_checking`;
CREATE TABLE IF NOT EXISTS `yl_welore_lost_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `item_type` int(11) unsigned NOT NULL,
  `release_type` int(11) unsigned NOT NULL,
  `item_name` varchar(500) NOT NULL,
  `item_detail` longtext NOT NULL,
  `lost_address` varchar(500) NOT NULL,
  `lost_time` int(11) unsigned NOT NULL,
  `contact_details` varchar(500) NOT NULL,
  `item_status` int(11) NOT NULL DEFAULT '1',
  `top_time` int(11) unsigned NOT NULL DEFAULT '0',
  `audit_status` int(11) unsigned NOT NULL DEFAULT '0',
  `audit_reason` varchar(500) DEFAULT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_lost_item`;
CREATE TABLE IF NOT EXISTS `yl_welore_lost_item_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
  `reply_is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
  `top_twig` int(11) unsigned NOT NULL DEFAULT '0',
  `price_type` int(11) unsigned NOT NULL,
  `top_price` decimal(22,2) unsigned NOT NULL,
  `help_document` longtext NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_lost_item_config`;
CREATE TABLE IF NOT EXISTS `yl_welore_lost_item_reply` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `li_id` int(11) unsigned NOT NULL,
  `content` longtext NOT NULL,
  `is_secrecy` int(11) unsigned NOT NULL,
  `reply_time` int(11) unsigned NOT NULL,
  `audit_status` int(11) unsigned NOT NULL DEFAULT '0',
  `audit_reason` varchar(500) DEFAULT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_lost_item_reply`;
CREATE TABLE IF NOT EXISTS `yl_welore_lost_item_top` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usl_id` int(11) unsigned NOT NULL DEFAULT '0',
  `li_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `top_day` int(11) unsigned NOT NULL,
  `pay_type` int(11) unsigned NOT NULL,
  `pay_price` decimal(22,2) unsigned NOT NULL,
  `add_time` int(11) unsigned NOT NULL,
  `is_pay` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `usl_id` (`usl_id`),
  KEY `li_id` (`li_id`),
  KEY `user_id` (`user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_lost_item_top`;
CREATE TABLE IF NOT EXISTS `yl_welore_lost_item_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '1',
  `sort` int(11) NOT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_lost_item_type`;
CREATE TABLE IF NOT EXISTS `yl_welore_medal` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `merit_icon` varchar(1000) NOT NULL,
  `merit_name` varchar(1000) NOT NULL,
  `merit_annotate` text NOT NULL,
  `unlock_outlay` decimal(22,0) unsigned NOT NULL,
  `prepare_time` int(11) unsigned NOT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '1',
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `purge_time` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`),
  KEY `scores` (`scores`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_medal`;
CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `custom_title` varchar(255) NOT NULL,
  `is_vip_free_look` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `is_allow_user_upload` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `is_info_auto_review` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `is_content_auto_review` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `is_comment_auto_review` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `is_require_user_copyright` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `is_allow_user_charge` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `min_amount_charged` decimal(32,2) unsigned NOT NULL DEFAULT '0.00',
  `max_amount_charged` decimal(32,2) unsigned NOT NULL DEFAULT '0.00',
  `every_day_free_look_num` int(11) unsigned NOT NULL DEFAULT '0',
  `is_enabled_look_ads_unlock_paid_content` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `look_ads_unlock_paid_content_max_num` int(11) unsigned NOT NULL DEFAULT '0',
  `charged_profit_rake_ratio` decimal(4,2) unsigned NOT NULL DEFAULT '0.50',
  `user_agreement` longtext NOT NULL,
  `disclaimer_warranties` longtext NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_micro_series_config`;
CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_content_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `msi_id` int(11) unsigned NOT NULL,
  `upload_user_id` int(11) unsigned DEFAULT '0',
  `msi_episode_number` varchar(255) NOT NULL,
  `msi_episode_url` text NOT NULL,
  `is_allow_only_vip` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `paid_unlocking_type` tinyint(2) unsigned NOT NULL DEFAULT '0',
  `paid_unlocking_price` decimal(32,2) NOT NULL DEFAULT '0.00',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `audit_reason` text,
  `display_status` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `sort` int(11) NOT NULL DEFAULT '0',
  `create_time` int(11) unsigned NOT NULL,
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `msi_id` (`msi_id`),
  KEY `is_allow_only_vip` (`is_allow_only_vip`),
  KEY `paid_unlocking_type` (`paid_unlocking_type`),
  KEY `sort` (`sort`),
  KEY `create_time` (`create_time`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_micro_series_content_list`;
CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_info_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `upload_user_id` int(11) unsigned NOT NULL DEFAULT '0',
  `title` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `poster_url` text NOT NULL,
  `director` varchar(255) NOT NULL,
  `screenwriter` varchar(255) NOT NULL,
  `lead_actors` varchar(255) NOT NULL,
  `production_country` varchar(100) NOT NULL,
  `language` varchar(100) NOT NULL,
  `release_date` varchar(255) NOT NULL,
  `duration_minutes` int(11) NOT NULL,
  `alias` varchar(100) DEFAULT NULL,
  `plot_summary` longtext,
  `total_episodes` varchar(255) NOT NULL,
  `user_copyright_img` text,
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `audit_reason` varchar(500) DEFAULT NULL,
  `display_status` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `create_time` int(11) unsigned NOT NULL,
  `update_time` int(11) unsigned NOT NULL,
  `is_del` tinyint(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=7 ;

TRUNCATE TABLE `yl_welore_micro_series_info_list`;
CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_info_review` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `msi_id` int(11) unsigned NOT NULL,
  `msc_id` int(11) unsigned NOT NULL DEFAULT '0',
  `user_id` int(11) unsigned NOT NULL,
  `comment` longtext NOT NULL,
  `rid` int(11) NOT NULL DEFAULT '0',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `audit_reason` text,
  `create_time` int(11) unsigned NOT NULL,
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `msi_id` (`msi_id`),
  KEY `msc_id` (`msc_id`),
  KEY `user_id` (`user_id`),
  KEY `rid` (`rid`),
  KEY `is_del` (`is_del`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_micro_series_info_review`;
CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `sort` int(11) NOT NULL DEFAULT '0',
  `create_time` int(11) unsigned NOT NULL,
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_micro_series_type`;
CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_unlock_paid_content_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `msi_id` int(11) unsigned NOT NULL,
  `msc_id` int(11) unsigned NOT NULL,
  `unlock_type` tinyint(1) NOT NULL,
  `unlock_price` decimal(32,2) NOT NULL DEFAULT '0.00',
  `charged_profit_rake_ratio` decimal(4,2) NOT NULL DEFAULT '0.50',
  `unlock_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `msi_id` (`msi_id`),
  KEY `msc_id` (`msc_id`),
  KEY `unlock_type` (`unlock_type`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_micro_series_unlock_paid_content_list`;
CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_user_like` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `msi_id` int(11) unsigned NOT NULL,
  `msc_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `like_type` tinyint(1) unsigned NOT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_micro_series_user_like`;
CREATE TABLE IF NOT EXISTS `yl_welore_motion` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mot_name` varchar(300) DEFAULT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `mot_url` varchar(300) DEFAULT NULL,
  `pid` int(11) unsigned DEFAULT NULL,
  `sort` int(11) unsigned DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `id` (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=162 ;
TRUNCATE TABLE `yl_welore_motion`;
INSERT INTO `yl_welore_motion` VALUES(1, '首页', 'am-icon-home', 'index/index', 0, 0);
INSERT INTO `yl_welore_motion` VALUES(2, '广场管理', 'am-icon-map-signs', NULL, 0, 1);
INSERT INTO `yl_welore_motion` VALUES(3, '广场列表', NULL, 'compass/nav', 2, 0);
INSERT INTO `yl_welore_motion` VALUES(4, '用户管理', 'am-icon-users', NULL, 0, 7);
INSERT INTO `yl_welore_motion` VALUES(5, '圈子列表', NULL, 'compass/fence', 2, 1);
INSERT INTO `yl_welore_motion` VALUES(6, '圈子审核', NULL, 'compass/solicit', 2, 3);
INSERT INTO `yl_welore_motion` VALUES(7, '帖子管理', 'am-icon-file-text', NULL, 0, 2);
INSERT INTO `yl_welore_motion` VALUES(8, '帖子信息', NULL, 'essay/index', 125, 0);
INSERT INTO `yl_welore_motion` VALUES(9, '帖子回复', NULL, 'essay/reply', 125, 2);
INSERT INTO `yl_welore_motion` VALUES(10, '帖子设置', NULL, 'essay/ritual', 125, 4);
INSERT INTO `yl_welore_motion` VALUES(11, '举报反馈', 'am-icon-shield', NULL, 0, 3);
INSERT INTO `yl_welore_motion` VALUES(12, '帖子举报', NULL, 'journal/report', 11, 0);
INSERT INTO `yl_welore_motion` VALUES(13, '帖子申诉', NULL, 'journal/appeal', 11, 1);
INSERT INTO `yl_welore_motion` VALUES(14, '广告列表', NULL, 'systems/symbol', 42, 1);
INSERT INTO `yl_welore_motion` VALUES(15, '系统管理', 'am-icon-cogs', NULL, 0, 9);
INSERT INTO `yl_welore_motion` VALUES(16, '远程附件', NULL, 'systems/annex', 15, 7);
INSERT INTO `yl_welore_motion` VALUES(17, '营销管理', 'am-icon-magic', NULL, 0, 5);
INSERT INTO `yl_welore_motion` VALUES(18, '礼物管理', NULL, NULL, 17, 4);
INSERT INTO `yl_welore_motion` VALUES(19, '站点设置', NULL, 'systems/warrior', 15, 6);
INSERT INTO `yl_welore_motion` VALUES(20, '疑难解答', NULL, 'systems/help', 15, 2);
INSERT INTO `yl_welore_motion` VALUES(21, '小程序设置', NULL, 'systems/applets', 42, 8);
INSERT INTO `yl_welore_motion` VALUES(22, '会员设置', NULL, 'marketing/fabulous', 17, 1);
INSERT INTO `yl_welore_motion` VALUES(23, '用户信息', NULL, NULL, 4, 0);
INSERT INTO `yl_welore_motion` VALUES(24, '超管列表', NULL, 'user/inspect', 23, 2);
INSERT INTO `yl_welore_motion` VALUES(25, '商品列表', NULL, 'marketing/shop', 76, 3);
INSERT INTO `yl_welore_motion` VALUES(26, '圈子投诉', NULL, 'journal/spread', 11, 2);
INSERT INTO `yl_welore_motion` VALUES(27, '管理投诉', NULL, 'journal/safety', 11, 3);
INSERT INTO `yl_welore_motion` VALUES(28, '用户投诉', NULL, 'journal/usmur', 11, 4);
INSERT INTO `yl_welore_motion` VALUES(29, '货币设置', NULL, 'systems/punch', 37, 0);
INSERT INTO `yl_welore_motion` VALUES(30, '自定义转发', NULL, 'systems/partake', 42, 10);
INSERT INTO `yl_welore_motion` VALUES(31, '流量主', NULL, 'systems/proclaim', 42, 0);
INSERT INTO `yl_welore_motion` VALUES(32, '礼物税率', NULL, 'marketing/taxing', 18, 1);
INSERT INTO `yl_welore_motion` VALUES(33, '商品分类', NULL, 'marketing/stype', 76, 0);
INSERT INTO `yl_welore_motion` VALUES(34, '商品订单', NULL, 'marketing/sorder', 76, 4);
INSERT INTO `yl_welore_motion` VALUES(35, '图片库', NULL, 'images/index', 15, 0);
INSERT INTO `yl_welore_motion` VALUES(36, '自定义设置', NULL, 'systems/navigate', 42, 9);
INSERT INTO `yl_welore_motion` VALUES(37, '钱包管理', NULL, NULL, 4, 1);
INSERT INTO `yl_welore_motion` VALUES(38, '提现设置', NULL, 'rawls/setting', 37, 2);
INSERT INTO `yl_welore_motion` VALUES(39, '提现列表', NULL, 'rawls/stand', 37, 3);
INSERT INTO `yl_welore_motion` VALUES(40, '模板消息', NULL, NULL, 42, 5);
INSERT INTO `yl_welore_motion` VALUES(41, '虚拟用户', NULL, 'user/theoretic', 23, 3);
INSERT INTO `yl_welore_motion` VALUES(42, '小程序管理', 'am-icon-stumbleupon', NULL, 0, 8);
INSERT INTO `yl_welore_motion` VALUES(43, '审核设置', NULL, 'systems/audit', 42, 4);
INSERT INTO `yl_welore_motion` VALUES(45, '充值列表', NULL, 'user/water', 37, 1);
INSERT INTO `yl_welore_motion` VALUES(46, '邀请列表', NULL, 'unlawful/salute', 23, 6);
INSERT INTO `yl_welore_motion` VALUES(47, '视频设置', NULL, 'systems/video', 42, 3);
INSERT INTO `yl_welore_motion` VALUES(48, '娱乐系统', 'am-icon-tags', NULL, 0, 4);
INSERT INTO `yl_welore_motion` VALUES(49, '任务系统', NULL, NULL, 48, 0);
INSERT INTO `yl_welore_motion` VALUES(50, '首页置顶', NULL, 'essay/home_topping', 125, 1);
INSERT INTO `yl_welore_motion` VALUES(51, '功能开关', NULL, 'systems/switch_control', 15, 5);
INSERT INTO `yl_welore_motion` VALUES(52, '勋章列表', NULL, 'manual/decorate', 48, 2);
INSERT INTO `yl_welore_motion` VALUES(53, '安全防护', NULL, NULL, 15, 4);
INSERT INTO `yl_welore_motion` VALUES(54, '模板市场', NULL, 'sketchpad/market', 42, 6);
INSERT INTO `yl_welore_motion` VALUES(55, '话题列表', NULL, 'compass/theme', 2, 2);
INSERT INTO `yl_welore_motion` VALUES(56, '游客列表', NULL, 'user/traveler', 23, 1);
INSERT INTO `yl_welore_motion` VALUES(57, '等级列表', NULL, 'manual/level', 48, 3);
INSERT INTO `yl_welore_motion` VALUES(58, '首页导航', NULL, 'leading/traction', 42, 2);
INSERT INTO `yl_welore_motion` VALUES(59, '抽奖列表', NULL, NULL, 17, 3);
INSERT INTO `yl_welore_motion` VALUES(60, '装扮列表', NULL, 'manual/deck', 48, 4);
INSERT INTO `yl_welore_motion` VALUES(61, '小秘密列表', NULL, 'stealth/softly', 75, 0);
INSERT INTO `yl_welore_motion` VALUES(62, '小秘密回复', NULL, 'stealth/janitor', 75, 1);
INSERT INTO `yl_welore_motion` VALUES(63, '像框列表', NULL, 'manual/adhesion', 48, 1);
INSERT INTO `yl_welore_motion` VALUES(64, '身份铭牌', NULL, 'shield/disguise', 48, 5);
INSERT INTO `yl_welore_motion` VALUES(65, '榜单排行', NULL, 'unlawful/annunciation', 17, 2);
INSERT INTO `yl_welore_motion` VALUES(66, '用户认证', NULL, NULL, 4, 2);
INSERT INTO `yl_welore_motion` VALUES(67, '认证表单', NULL, 'depend/provision', 66, 0);
INSERT INTO `yl_welore_motion` VALUES(68, '认证列表', NULL, 'depend/acquire', 66, 1);
INSERT INTO `yl_welore_motion` VALUES(69, '纸条管理', NULL, NULL, 7, 3);
INSERT INTO `yl_welore_motion` VALUES(70, '小纸条列表', NULL, 'tissue/wedge', 69, 0);
INSERT INTO `yl_welore_motion` VALUES(71, '小纸条设置', NULL, 'tissue/choked', 69, 1);
INSERT INTO `yl_welore_motion` VALUES(72, '新人营销', NULL, 'unlawful/attract', 17, 0);
INSERT INTO `yl_welore_motion` VALUES(73, '评论回复', NULL, 'tissue/discuss', 125, 3);
INSERT INTO `yl_welore_motion` VALUES(74, '插件列表', NULL, 'sketchpad/plugin', 81, 0);
INSERT INTO `yl_welore_motion` VALUES(75, '树洞管理', NULL, NULL, 7, 2);
INSERT INTO `yl_welore_motion` VALUES(76, '商品管理', NULL, NULL, 17, 5);
INSERT INTO `yl_welore_motion` VALUES(77, '卡密列表', NULL, NULL, 81, 2);
INSERT INTO `yl_welore_motion` VALUES(78, '商品卡密', NULL, 'cammy/shopAnomaly', 77, 0);
INSERT INTO `yl_welore_motion` VALUES(79, '货币卡密', NULL, 'cammy/bankAnomaly', 77, 1);
INSERT INTO `yl_welore_motion` VALUES(80, '兑换记录', NULL, 'cammy/exchangeAnomaly', 77, 2);
INSERT INTO `yl_welore_motion` VALUES(81, '插件管理', 'am-icon-slack', NULL, 0, 6);
INSERT INTO `yl_welore_motion` VALUES(82, '网盘列表', NULL, NULL, 81, 3);
INSERT INTO `yl_welore_motion` VALUES(83, '文件列表', NULL, 'cloud/files', 82, 0);
INSERT INTO `yl_welore_motion` VALUES(84, '用户网盘', NULL, 'cloud/personal', 82, 1);
INSERT INTO `yl_welore_motion` VALUES(85, '网盘设置', NULL, 'cloud/dictate', 82, 2);
INSERT INTO `yl_welore_motion` VALUES(86, '抽奖活动', NULL, 'unlawful/lottery', 59, 0);
INSERT INTO `yl_welore_motion` VALUES(87, '抽奖记录', NULL, 'unlawful/cheer', 59, 1);
INSERT INTO `yl_welore_motion` VALUES(88, '任务列表', NULL, 'manual/propagate', 49, 0);
INSERT INTO `yl_welore_motion` VALUES(89, '完成排行', NULL, 'manual/taskleaderboard', 49, 1);
INSERT INTO `yl_welore_motion` VALUES(90, '礼物列表', NULL, 'marketing/friendly', 18, 0);
INSERT INTO `yl_welore_motion` VALUES(91, '排行列表', NULL, NULL, 4, 3);
INSERT INTO `yl_welore_motion` VALUES(92, '发帖排行', NULL, 'user/speak_ranking', 91, 0);
INSERT INTO `yl_welore_motion` VALUES(93, '积分排行', NULL, 'user/fraction_ranking', 91, 1);
INSERT INTO `yl_welore_motion` VALUES(94, '贝壳排行', NULL, 'user/conch_ranking', 91, 2);
INSERT INTO `yl_welore_motion` VALUES(95, '赠礼排行', NULL, 'user/gift_ranking', 91, 3);
INSERT INTO `yl_welore_motion` VALUES(96, '收礼排行', NULL, 'user/receiving_ranking', 91, 4);
INSERT INTO `yl_welore_motion` VALUES(97, '邀请排行', NULL, 'user/engage', 91, 5);
INSERT INTO `yl_welore_motion` VALUES(98, '用户列表', NULL, 'user/index', 23, 0);
INSERT INTO `yl_welore_motion` VALUES(99, '订阅消息', NULL, 'systems/inform', 40, 0);
INSERT INTO `yl_welore_motion` VALUES(100, '推送消息', NULL, 'unlawful/messagePush', 40, 1);
INSERT INTO `yl_welore_motion` VALUES(101, '存储设置', NULL, 'cloud/storage', 82, 3);
INSERT INTO `yl_welore_motion` VALUES(102, '公众通知', NULL, NULL, 81, 4);
INSERT INTO `yl_welore_motion` VALUES(103, '模板消息', NULL, 'people/mention', 102, 0);
INSERT INTO `yl_welore_motion` VALUES(104, '微信配置', NULL, 'people/config', 102, 1);
INSERT INTO `yl_welore_motion` VALUES(105, '私信列表', NULL, 'tissue/privateLetter', 23, 4);
INSERT INTO `yl_welore_motion` VALUES(106, '截屏记录', NULL, 'tissue/peeping', 23, 5);
INSERT INTO `yl_welore_motion` VALUES(107, '内容安全', NULL, 'unlawful/words', 53, 0);
INSERT INTO `yl_welore_motion` VALUES(108, '拦截手机', NULL, 'unlawful/blockingPhone', 53, 1);
INSERT INTO `yl_welore_motion` VALUES(109, '一键拨号', NULL, 'people/dial', 81, 1);
INSERT INTO `yl_welore_motion` VALUES(110, '内容点评', NULL, NULL, 81, 5);
INSERT INTO `yl_welore_motion` VALUES(111, '点评列表', NULL, 'people/correct', 110, 0);
INSERT INTO `yl_welore_motion` VALUES(112, '常用语句', NULL, 'people/often', 110, 1);
INSERT INTO `yl_welore_motion` VALUES(113, '点评设置', NULL, 'people/setup', 110, 2);
INSERT INTO `yl_welore_motion` VALUES(114, '失物招领', NULL, NULL, 81, 6);
INSERT INTO `yl_welore_motion` VALUES(115, '物品分类', NULL, 'people/lost_type', 114, 0);
INSERT INTO `yl_welore_motion` VALUES(116, '招领列表', NULL, 'people/lost_found', 114, 1);
INSERT INTO `yl_welore_motion` VALUES(117, '招领回复', NULL, 'people/lost_reply', 114, 2);
INSERT INTO `yl_welore_motion` VALUES(118, '置顶列表', NULL, 'people/lost_top', 114, 3);
INSERT INTO `yl_welore_motion` VALUES(119, '招领配置', NULL, 'people/lost_config', 114, 4);
INSERT INTO `yl_welore_motion` VALUES(120, '同城信息', NULL, NULL, 81, 7);
INSERT INTO `yl_welore_motion` VALUES(121, '商家分类', NULL, 'pluto/merchant_type', 120, 0);
INSERT INTO `yl_welore_motion` VALUES(122, '商家列表', NULL, 'pluto/merchant_list', 120, 1);
INSERT INTO `yl_welore_motion` VALUES(123, '信息设置', NULL, 'pluto/merchant_config', 120, 6);
INSERT INTO `yl_welore_motion` VALUES(124, '小秘密设置', NULL, 'stealth/janitor_setting', 75, 2);
INSERT INTO `yl_welore_motion` VALUES(125, '帖子列表', NULL, NULL, 7, 0);
INSERT INTO `yl_welore_motion` VALUES(126, '店员列表', NULL, 'pluto/merchant_sales_clerk', 120, 2);
INSERT INTO `yl_welore_motion` VALUES(127, '商品列表', NULL, 'pluto/merchant_commodity', 120, 3);
INSERT INTO `yl_welore_motion` VALUES(128, '订单列表', NULL, 'pluto/merchant_commodity_order', 120, 4);
INSERT INTO `yl_welore_motion` VALUES(129, '核销列表', NULL, 'pluto/merchant_commodity_order_redeem', 120, 5);
INSERT INTO `yl_welore_motion` VALUES(130, '商品规格', NULL, 'tedious/shop_specs', 76, 1);
INSERT INTO `yl_welore_motion` VALUES(131, '视频解析', NULL, NULL, 81, 8);
INSERT INTO `yl_welore_motion` VALUES(132, '解析列表', NULL, 'resolve/parse_list', 131, 0);
INSERT INTO `yl_welore_motion` VALUES(133, '适配列表', NULL, 'resolve/adaptation_parse_list', 131, 1);
INSERT INTO `yl_welore_motion` VALUES(134, '二手交易', NULL, NULL, 81, 9);
INSERT INTO `yl_welore_motion` VALUES(135, '分类目录', NULL, 'resale/used_goods_type', 134, 0);
INSERT INTO `yl_welore_motion` VALUES(136, '发布信息', NULL, 'resale/used_goods_found', 134, 1);
INSERT INTO `yl_welore_motion` VALUES(137, '交流互动', NULL, 'resale/used_goods_reply', 134, 2);
INSERT INTO `yl_welore_motion` VALUES(138, '置顶列表', NULL, 'resale/used_goods_top', 134, 3);
INSERT INTO `yl_welore_motion` VALUES(139, '配置选项', NULL, 'resale/used_goods_config', 134, 4);
INSERT INTO `yl_welore_motion` VALUES(140, '求职招聘', NULL, NULL, 81, 10);
INSERT INTO `yl_welore_motion` VALUES(141, '岗位类型', NULL, 'career/employment_type', 140, 0);
INSERT INTO `yl_welore_motion` VALUES(142, '招聘列表', NULL, 'career/employment_found', 140, 1);
INSERT INTO `yl_welore_motion` VALUES(143, '置顶列表', NULL, 'career/employment_top', 140, 2);
INSERT INTO `yl_welore_motion` VALUES(144, '招聘配置', NULL, 'career/employment_config', 140, 3);
INSERT INTO `yl_welore_motion` VALUES(145, '热帖管理', NULL, NULL, 7, 1);
INSERT INTO `yl_welore_motion` VALUES(146, '横幅广告', NULL, 'shield/thread_banner_promotions', 145, 0);
INSERT INTO `yl_welore_motion` VALUES(147, '热帖置顶', NULL, 'shield/thread_popularity', 145, 1);
INSERT INTO `yl_welore_motion` VALUES(148, '热帖配置', NULL, 'shield/thread_config', 145, 2);
INSERT INTO `yl_welore_motion` VALUES(149, '幸运抽奖', NULL, NULL, 81, 11);
INSERT INTO `yl_welore_motion` VALUES(150, '抽奖列表', NULL, 'tedious/contest_list', 149, 0);
INSERT INTO `yl_welore_motion` VALUES(151, '参与列表', NULL, 'tedious/involved_contest', 149, 1);
INSERT INTO `yl_welore_motion` VALUES(152, '抽奖配置', NULL, 'tedious/contest_config', 149, 2);
INSERT INTO `yl_welore_motion` VALUES(153, '短剧视频', NULL, NULL, 48, 5);
INSERT INTO `yl_welore_motion` VALUES(154, '短剧类型', NULL, 'dramas/micro_series_type', 153, 0);
INSERT INTO `yl_welore_motion` VALUES(155, '短剧信息', NULL, 'dramas/micro_series_info', 153, 1);
INSERT INTO `yl_welore_motion` VALUES(156, '短剧视频', NULL, 'dramas/micro_series_content', 153, 2);
INSERT INTO `yl_welore_motion` VALUES(157, '短剧付费', NULL, 'dramas/micro_series_content_unlock', 153, 3);
INSERT INTO `yl_welore_motion` VALUES(158, '短剧评论', NULL, 'dramas/micro_series_review', 153, 4);
INSERT INTO `yl_welore_motion` VALUES(159, '短剧配置', NULL, 'dramas/micro_series_config', 153, 5);
INSERT INTO `yl_welore_motion` VALUES(160, '通用存储', NULL, 'leading/compliant', 81, 12);
INSERT INTO `yl_welore_motion` VALUES(161, '123云盘', NULL, 'leading/pan123', 81, 13);

CREATE TABLE IF NOT EXISTS `yl_welore_much_admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `much_name` varchar(255) DEFAULT NULL,
  `much_cipher` varchar(255) DEFAULT NULL,
  `much_uniacid` int(11) DEFAULT NULL,
  `much_purview` varchar(1000) DEFAULT NULL,
  `much_maturity` int(11) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_much_admin`;
CREATE TABLE IF NOT EXISTS `yl_welore_mucilage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `card_code` varchar(255) NOT NULL,
  `card_type` int(11) unsigned NOT NULL,
  `financial_type` int(11) unsigned NOT NULL,
  `face_value` decimal(18,2) unsigned NOT NULL,
  `is_sell` int(11) unsigned NOT NULL DEFAULT '0',
  `is_use` int(11) unsigned NOT NULL DEFAULT '0',
  `status` int(11) unsigned NOT NULL,
  `shop_id` int(11) NOT NULL DEFAULT '0',
  `create_time` int(11) unsigned NOT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `card_type` (`card_type`),
  KEY `financial_type` (`financial_type`),
  KEY `shop_id` (`shop_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_mucilage`;
CREATE TABLE IF NOT EXISTS `yl_welore_mucilage_use_annaly` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mu_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `use_ip` varchar(255) NOT NULL,
  `use_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `mu_id` (`mu_id`),
  KEY `user_id` (`user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_mucilage_use_annaly`;
CREATE TABLE IF NOT EXISTS `yl_welore_needle` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `icon` varchar(500) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `keyword` varchar(255) DEFAULT NULL,
  `status` int(11) unsigned DEFAULT '1',
  `scores` int(11) unsigned DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id` (`id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_needle`;
CREATE TABLE IF NOT EXISTS `yl_welore_netdisc` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `file_type` int(11) unsigned NOT NULL,
  `file_md5` varchar(255) NOT NULL,
  `file_suffix` varchar(255) NOT NULL,
  `file_size` decimal(32,0) NOT NULL,
  `file_address` varchar(2000) NOT NULL,
  `file_status` int(11) unsigned NOT NULL DEFAULT '1',
  `up_user_id` int(11) unsigned NOT NULL,
  `up_user_ip` varchar(255) NOT NULL,
  `add_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_netdisc`;
CREATE TABLE IF NOT EXISTS `yl_welore_netdisc_belong` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nc_id` int(11) unsigned NOT NULL DEFAULT '0',
  `user_id` int(11) unsigned NOT NULL,
  `file_name` varchar(500) NOT NULL,
  `parent_path_id` int(11) unsigned NOT NULL DEFAULT '0',
  `is_dir` int(11) unsigned NOT NULL DEFAULT '0',
  `add_time` int(11) unsigned NOT NULL,
  `is_sell` int(11) unsigned NOT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_netdisc_belong`;
CREATE TABLE IF NOT EXISTS `yl_welore_netdisc_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `disk_size` decimal(32,0) unsigned NOT NULL,
  `upload_size_limit` decimal(32,0) unsigned NOT NULL DEFAULT '10485760',
  `upload_type_limited` varchar(1000) DEFAULT NULL,
  `rel_paper_icon_hide` int(11) unsigned NOT NULL DEFAULT '0',
  `use_protocol` longtext NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`),
  KEY `rel_paper_icon_hide` (`rel_paper_icon_hide`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_netdisc_config`;
CREATE TABLE IF NOT EXISTS `yl_welore_netdisc_sell` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pa_id` int(11) unsigned NOT NULL,
  `nc_id` int(11) unsigned NOT NULL,
  `nb_id` int(11) unsigned NOT NULL,
  `is_sell` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pa_id` (`pa_id`),
  KEY `nc_id` (`nc_id`),
  KEY `nb_id` (`nb_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_netdisc_sell`;
CREATE TABLE IF NOT EXISTS `yl_welore_netdisc_storage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quicken_type` int(11) NOT NULL DEFAULT '-1',
  `oss_follow` varchar(1000) DEFAULT NULL,
  `qiniu_follow` varchar(1000) DEFAULT NULL,
  `cos_follow` varchar(1000) DEFAULT NULL,
  `upyun_follow` varchar(1000) DEFAULT NULL,
  `ftp_follow` varchar(1000) DEFAULT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_netdisc_storage`;
CREATE TABLE IF NOT EXISTS `yl_welore_netdisc_user_volume` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `quota_size` decimal(32,0) unsigned NOT NULL,
  `exp_time` int(11) unsigned NOT NULL DEFAULT '0',
  `use_status` int(11) unsigned NOT NULL DEFAULT '1',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_netdisc_user_volume`;
CREATE TABLE IF NOT EXISTS `yl_welore_new_user_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reward_status` int(11) unsigned NOT NULL DEFAULT '0',
  `reg_less_day` int(11) unsigned NOT NULL DEFAULT '0',
  `reward_type` int(11) unsigned NOT NULL,
  `reward_code` decimal(18,2) unsigned NOT NULL,
  `reward_count` int(11) unsigned NOT NULL DEFAULT '1',
  `tory_ids` varchar(500) NOT NULL,
  `paper_ids` varchar(500) NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_new_user_task`;
CREATE TABLE IF NOT EXISTS `yl_welore_new_user_task_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `tory_id` int(11) unsigned NOT NULL,
  `paper_id` int(11) unsigned NOT NULL,
  `reward_type` int(11) unsigned NOT NULL,
  `reward_code` decimal(18,2) unsigned NOT NULL,
  `reward_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_new_user_task_record`;
CREATE TABLE IF NOT EXISTS `yl_welore_outlying` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quicken_type` int(11) unsigned NOT NULL DEFAULT '0',
  `oss_follow` varchar(1000) DEFAULT NULL,
  `qiniu_follow` varchar(1000) DEFAULT NULL,
  `cos_follow` varchar(1000) DEFAULT NULL,
  `upyun_follow` varchar(1000) DEFAULT NULL,
  `ftp_follow` varchar(1000) DEFAULT NULL,
  `aws_follow` varchar(1000) DEFAULT NULL,
  `pan123_follow` varchar(1000) DEFAULT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_outlying`;
CREATE TABLE IF NOT EXISTS `yl_welore_outlying_allude` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(500) DEFAULT NULL,
  `value` varchar(500) DEFAULT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '1',
  `type` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=68 ;

TRUNCATE TABLE `yl_welore_outlying_allude`;
INSERT INTO `yl_welore_outlying_allude` VALUES(1, '华东1 ( 杭州 ) ', 'oss-cn-hangzhou.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(2, '华东1 ( 杭州 内网 ) ', 'oss-cn-hangzhou-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(3, '华东2 ( 上海 ) ', 'oss-cn-shanghai.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(4, '华东2 ( 上海 内网 ) ', 'oss-cn-shanghai-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(5, '华北1 ( 青岛 ) ', 'oss-cn-qingdao.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(6, '华北1 ( 青岛 内网 ) ', 'oss-cn-qingdao-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(7, '华北2 ( 北京 ) ', 'oss-cn-beijing.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(8, '华北2 ( 北京 内网 ) ', 'oss-cn-beijing-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(9, '华北3 ( 张家口 ) ', 'oss-cn-zhangjiakou.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(10, '华北3 ( 张家口 内网 ) ', 'oss-cn-zhangjiakou-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(11, '华北5 ( 呼和浩特 ) ', 'oss-cn-huhehaote.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(12, '华北5 ( 呼和浩特 内网 ) ', 'oss-cn-huhehaote-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(13, '华北6 ( 乌兰察布 ) ', 'oss-cn-wulanchabu.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(14, '华北6 ( 乌兰察布 内网 ) ', 'oss-cn-wulanchabu-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(15, '华南1 ( 深圳 ) ', 'oss-cn-shenzhen.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(16, '华南1 ( 深圳 内网 ) ', 'oss-cn-shenzhen-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(17, '华南2 ( 河源 ) ', 'oss-cn-heyuan.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(18, '华南2 ( 河源 内网 ) ', 'oss-cn-heyuan-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(19, '华南3 ( 广州 ) ', 'oss-cn-guangzhou.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(20, '华南3 ( 广州 内网 ) ', 'oss-cn-guangzhou-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(21, '西南1 ( 成都 ) ', 'oss-cn-chengdu.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(22, '西南1 ( 成都 内网 ) ', 'oss-cn-chengdu-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(23, '中国 ( 香港 ) ', 'oss-cn-hongkong.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(24, '中国 ( 香港 内网 ) ', 'oss-cn-hongkong-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(25, '美国西部1 ( 硅谷 ) ', 'oss-us-west-1.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(26, '美国西部1 ( 硅谷 内网 ) ', 'oss-us-west-1-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(27, '美国东部1 ( 弗吉尼亚 ) ', 'oss-us-east-1.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(28, '美国东部1 ( 弗吉尼亚 内网 ) ', 'oss-us-east-1-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(29, '亚太东南1 ( 新加坡 ) ', 'oss-ap-southeast-1.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(30, '亚太东南1 ( 新加坡 内网 ) ', 'oss-ap-southeast-1-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(31, '亚太东南2 ( 悉尼 ) ', 'oss-ap-southeast-2.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(32, '亚太东南2 ( 悉尼 内网 ) ', 'oss-ap-southeast-2-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(33, '亚太东南3 ( 吉隆坡 ) ', 'oss-ap-southeast-3.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(34, '亚太东南3 ( 吉隆坡 内网 ) ', 'oss-ap-southeast-3-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(35, '亚太东南5 ( 雅加达 ) ', 'oss-ap-southeast-5.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(36, '亚太东南5 ( 雅加达 内网 ) ', 'oss-ap-southeast-5-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(37, '亚太东北1 ( 日本 ) ', 'oss-ap-northeast-1.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(38, '亚太东北1 ( 日本 内网 ) ', 'oss-ap-northeast-1-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(39, '亚太南部1 ( 孟买 ) ', 'oss-ap-south-1.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(40, '亚太南部1 ( 孟买 内网 ) ', 'oss-ap-south-1-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(41, '欧洲中部1 ( 法兰克福 ) ', 'oss-eu-central-1.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(42, '欧洲中部1 ( 法兰克福 内网 ) ', 'oss-eu-central-1-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(43, '英国 ( 伦敦 ) ', 'oss-eu-west-1.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(44, '英国 ( 伦敦 内网 ) ', 'oss-eu-west-1-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(45, '中东东部1 ( 迪拜 ) ', 'oss-me-east-1.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(46, '中东东部1 ( 迪拜 内网 ) ', 'oss-me-east-1-internal.aliyuncs.com', 1, 0);
INSERT INTO `yl_welore_outlying_allude` VALUES(47, '北京一区', 'ap-beijing-1', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(48, '北京', 'ap-beijing', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(49, '南京', 'ap-nanjing', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(50, '上海', 'ap-shanghai', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(51, '广州', 'ap-guangzhou', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(52, '成都', 'ap-chengdu', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(53, '重庆', 'ap-chongqing', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(54, '深圳金融', 'ap-shenzhen-fsi', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(55, '上海金融', 'ap-shanghai-fsi', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(56, '北京金融', 'ap-beijing-fsi', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(57, '中国香港', 'ap-hongkong', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(58, '新加坡', 'ap-singapore', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(59, '孟买', 'ap-mumbai', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(60, '首尔', 'ap-seoul', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(61, '曼谷', 'ap-bangkok', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(62, '东京', 'ap-tokyo', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(63, '硅谷 ( 美西 )', 'na-siliconvalley', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(64, '弗吉尼亚 ( 美东 )', 'na-ashburn', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(65, '多伦多', 'na-toronto', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(66, '法兰克福', 'eu-frankfurt', 1, 1);
INSERT INTO `yl_welore_outlying_allude` VALUES(67, '莫斯科', 'eu-moscow', 1, 1);

CREATE TABLE IF NOT EXISTS `yl_welore_paper` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned DEFAULT NULL,
  `tory_id` int(11) unsigned DEFAULT NULL,
  `tg_id` int(11) unsigned NOT NULL DEFAULT '0',
  `uccid` int(11) unsigned NOT NULL DEFAULT '0',
  `img_show_type` int(11) unsigned NOT NULL DEFAULT '0',
  `study_title` varchar(500) DEFAULT NULL,
  `study_title_color` varchar(500) DEFAULT NULL,
  `study_content` longtext,
  `study_type` int(11) unsigned DEFAULT '0',
  `video_type` int(11) unsigned NOT NULL DEFAULT '0',
  `third_part_vid` varchar(1000) DEFAULT NULL,
  `address_name` varchar(500) DEFAULT NULL,
  `address_details` varchar(1000) DEFAULT NULL,
  `address_latitude` varchar(500) DEFAULT NULL,
  `address_longitude` varchar(500) DEFAULT NULL,
  `call_phone` varchar(255) DEFAULT NULL,
  `is_buy` int(11) unsigned NOT NULL DEFAULT '0',
  `buy_price_type` int(11) unsigned NOT NULL DEFAULT '1',
  `buy_price` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `essence_time` int(11) unsigned NOT NULL DEFAULT '0',
  `topping_time` int(11) unsigned NOT NULL DEFAULT '0',
  `vote_deadline` int(11) NOT NULL DEFAULT '0',
  `image_part` longtext,
  `study_voice` longtext,
  `study_voice_time` int(11) unsigned NOT NULL DEFAULT '0',
  `study_video` longtext,
  `study_video_bulk` varchar(255) NOT NULL DEFAULT '0,0',
  `study_heat` int(11) unsigned NOT NULL DEFAULT '0',
  `study_laud` int(11) unsigned NOT NULL DEFAULT '0',
  `study_repount` int(11) unsigned NOT NULL DEFAULT '0',
  `adapter_time` int(11) unsigned DEFAULT NULL,
  `prove_time` int(11) unsigned DEFAULT NULL,
  `study_status` int(11) unsigned NOT NULL DEFAULT '0',
  `reject_reason` varchar(500) DEFAULT NULL,
  `whether_type` int(11) unsigned NOT NULL DEFAULT '0',
  `whether_reason` varchar(500) DEFAULT NULL,
  `whether_delete` int(11) unsigned NOT NULL DEFAULT '0',
  `whetd_time` int(11) unsigned DEFAULT NULL,
  `is_open` int(11) unsigned NOT NULL DEFAULT '1',
  `token` varchar(500) DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `tory_id` (`tory_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_buy_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `buy_type` int(11) unsigned NOT NULL DEFAULT '1',
  `buy_price` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `buy_taxing` decimal(18,2) NOT NULL,
  `buy_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_buy_user`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_complaint` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned DEFAULT NULL,
  `tale_type` int(11) unsigned DEFAULT NULL,
  `paper_id` int(11) unsigned DEFAULT NULL,
  `prely_id` int(11) unsigned DEFAULT NULL,
  `tory_id` int(11) unsigned DEFAULT NULL,
  `tale_content` longtext,
  `acceptance_status` int(11) unsigned NOT NULL DEFAULT '0',
  `petition_time` int(11) unsigned DEFAULT '0',
  `transact_time` int(11) unsigned DEFAULT '0',
  `is_strike` int(11) unsigned NOT NULL DEFAULT '0',
  `tale_instruct` longtext,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id` (`id`),
  KEY `user_id` (`user_id`),
  KEY `tale_type` (`tale_type`),
  KEY `paper_id` (`paper_id`),
  KEY `prely_id` (`prely_id`),
  KEY `tory_id` (`tory_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_complaint`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_heat_banner_ads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `img_url` text NOT NULL,
  `jump_type` tinyint(1) NOT NULL,
  `appid` varchar(255) DEFAULT NULL,
  `url` text,
  `sort` int(11) NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_heat_banner_ads`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_heat_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `custom_title` varchar(255) NOT NULL,
  `style_type` tinyint(1) NOT NULL DEFAULT '0',
  `custom_head_img` varchar(2000) NOT NULL,
  `statistics_time` tinyint(1) NOT NULL DEFAULT '0',
  `custom_sort_condition` text NOT NULL,
  `display_switch` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_heat_config`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_heat_put_top` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) unsigned NOT NULL,
  `put_top_start_time` int(11) unsigned NOT NULL,
  `put_top_end_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_heat_put_top`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_red_packet` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) unsigned DEFAULT NULL,
  `initial_type` int(11) unsigned NOT NULL DEFAULT '1',
  `initial_fraction` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `surplus_fraction` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `initial_conch` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `surplus_conch` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `initial_quantity` int(11) unsigned NOT NULL DEFAULT '0',
  `surplus_quantity` int(11) unsigned NOT NULL DEFAULT '0',
  `red_type` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `paper_id` (`paper_id`),
  KEY `red_type` (`red_type`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_red_packet`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_reply` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) unsigned DEFAULT NULL,
  `uccid` int(111) unsigned NOT NULL DEFAULT '0',
  `user_id` int(11) unsigned DEFAULT NULL,
  `reply_type` int(11) unsigned DEFAULT NULL,
  `is_gift` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `phase` int(11) unsigned DEFAULT NULL,
  `reply_content` longtext,
  `image_part` longtext,
  `reply_voice` longtext,
  `reply_voice_time` int(11) unsigned NOT NULL DEFAULT '0',
  `apter_time` int(11) unsigned DEFAULT NULL,
  `prove_time` int(11) unsigned NOT NULL DEFAULT '0',
  `reply_status` int(11) unsigned NOT NULL DEFAULT '1',
  `whetd_time` int(11) unsigned DEFAULT NULL,
  `whether_delete` int(11) unsigned DEFAULT '0',
  `whether_type` int(11) unsigned DEFAULT '0',
  `whether_reason` varchar(500) DEFAULT NULL,
  `token` varchar(500) DEFAULT NULL,
  `praise` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id` (`id`),
  KEY `paper_id` (`paper_id`),
  KEY `user_id` (`user_id`),
  KEY `reply_type` (`reply_type`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_reply`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_reply_duplex` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uccid` int(11) unsigned NOT NULL DEFAULT '0',
  `user_id` int(11) unsigned DEFAULT NULL,
  `reply_id` int(11) unsigned DEFAULT NULL,
  `re_uccid` int(11) unsigned NOT NULL DEFAULT '0',
  `reply_user_id` int(11) unsigned DEFAULT NULL,
  `duplex_content` longtext,
  `duplex_time` int(11) unsigned DEFAULT NULL,
  `duplex_status` int(11) unsigned NOT NULL DEFAULT '1',
  `check_opinion` varchar(255) DEFAULT NULL,
  `whetd_time` int(11) unsigned DEFAULT NULL,
  `whether_delete` int(11) unsigned NOT NULL DEFAULT '0',
  `whether_reason` varchar(500) DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `reply_id` (`reply_id`),
  KEY `duplex_time` (`duplex_time`),
  KEY `much_id` (`much_id`),
  KEY `reply_user_id` (`reply_user_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_reply_duplex`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_review_common_terms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `common_content` longtext NOT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_review_common_terms`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_review_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
  `is_all_review` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_review_config`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_review_score` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ne_id` int(11) unsigned NOT NULL,
  `tory_id` int(11) unsigned NOT NULL,
  `pa_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `assess_score` int(11) unsigned NOT NULL,
  `assess_content` longtext NOT NULL,
  `assess_time` int(11) unsigned NOT NULL,
  `is_show` int(11) unsigned NOT NULL,
  `audit_status` int(11) unsigned NOT NULL DEFAULT '0',
  `audit_reason` varchar(500) DEFAULT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_review_score`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_smingle` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `auto_review` int(11) unsigned DEFAULT '0',
  `number_limit` int(11) unsigned NOT NULL DEFAULT '0',
  `reply_auto_review` int(11) unsigned NOT NULL DEFAULT '1',
  `reply_number_limit` int(11) unsigned NOT NULL DEFAULT '0',
  `discuss_auto_review` int(11) unsigned NOT NULL DEFAULT '0',
  `discuss_number_limit` int(11) unsigned NOT NULL DEFAULT '0',
  `custom_hiss_title` varchar(500) DEFAULT NULL,
  `auto_hiss` int(11) unsigned NOT NULL DEFAULT '0',
  `hiss_limit` int(11) unsigned NOT NULL DEFAULT '0',
  `reply_auto_hiss` int(11) unsigned NOT NULL DEFAULT '0',
  `reply_hiss_limit` int(11) unsigned NOT NULL DEFAULT '0',
  `buy_paper_auto_review` int(11) unsigned NOT NULL DEFAULT '0',
  `buy_paper_number_limit` int(11) unsigned NOT NULL DEFAULT '0',
  `buy_paper_taxing` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `tractate_font_size` int(11) unsigned NOT NULL DEFAULT '14',
  `is_show_forum_declaration` int(11) unsigned NOT NULL DEFAULT '0',
  `forum_declaration` longtext,
  `notice` longtext,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_smingle`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_vote` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) unsigned NOT NULL,
  `ballot_name` varchar(500) NOT NULL,
  `cheat_ballot` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_vote`;
CREATE TABLE IF NOT EXISTS `yl_welore_paper_wechat_channel_video` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) unsigned NOT NULL,
  `feed_token` varchar(255) NOT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `paper_id` (`paper_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_paper_wechat_channel_video`;
CREATE TABLE IF NOT EXISTS `yl_welore_phone_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `block_type` int(11) unsigned NOT NULL,
  `block_rule` varchar(1000) NOT NULL,
  `intercept_type` int(11) unsigned NOT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_phone_blacklist`;
CREATE TABLE IF NOT EXISTS `yl_welore_polling` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `playbill_name` varchar(500) DEFAULT NULL,
  `playbill_url` varchar(1000) DEFAULT NULL,
  `ad_type` int(11) unsigned NOT NULL DEFAULT '0',
  `practice_type` int(11) unsigned DEFAULT NULL,
  `url` varchar(1000) DEFAULT NULL,
  `wx_app_url` varchar(1000) DEFAULT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_polling`;
CREATE TABLE IF NOT EXISTS `yl_welore_prompt_count` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `barg` int(11) unsigned DEFAULT '0',
  `much_id` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_prompt_count`;
CREATE TABLE IF NOT EXISTS `yl_welore_prompt_msg` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) unsigned DEFAULT NULL,
  `retter` varchar(1000) DEFAULT NULL,
  `capriole` int(11) unsigned NOT NULL DEFAULT '0',
  `tyid` int(11) unsigned NOT NULL DEFAULT '0',
  `msg_time` int(11) unsigned DEFAULT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_prompt_msg`;
CREATE TABLE IF NOT EXISTS `yl_welore_raws_setting` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `open_withdrawals` int(11) unsigned NOT NULL DEFAULT '0',
  `open_offline_payment` int(11) unsigned NOT NULL DEFAULT '0',
  `auto_review_payment` int(11) unsigned NOT NULL DEFAULT '0',
  `lowest_money` decimal(18,2) unsigned NOT NULL DEFAULT '1.00',
  `payment_tariff` decimal(19,3) unsigned NOT NULL DEFAULT '0.000',
  `notice` longtext,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`),
  KEY `open_withdrawals` (`open_withdrawals`),
  KEY `open_offline_payment` (`open_offline_payment`),
  KEY `auto_review_payment` (`auto_review_payment`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_raws_setting`;
CREATE TABLE IF NOT EXISTS `yl_welore_reissue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `whether_open` int(11) NOT NULL DEFAULT '0',
  `title` varchar(500) DEFAULT NULL,
  `reis_img` varchar(1000) DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_reissue`;
CREATE TABLE IF NOT EXISTS `yl_welore_shop` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_name` varchar(500) DEFAULT NULL,
  `product_type` int(11) unsigned DEFAULT NULL,
  `is_offline` int(11) unsigned NOT NULL DEFAULT '0',
  `product_synopsis` longtext,
  `product_detail` longtext,
  `product_img` longtext,
  `product_inventory` int(11) unsigned NOT NULL DEFAULT '0',
  `product_restrict` int(11) unsigned NOT NULL DEFAULT '0',
  `product_price` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `pay_type` int(11) unsigned NOT NULL DEFAULT '0',
  `noble_exclusive` int(11) unsigned NOT NULL DEFAULT '0',
  `open_discount` int(11) unsigned NOT NULL DEFAULT '0',
  `noble_discount` decimal(18,2) unsigned NOT NULL DEFAULT '1.00',
  `noble_rebate` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `sales_volume` int(11) unsigned NOT NULL DEFAULT '0',
  `auto_delivery` int(11) unsigned NOT NULL DEFAULT '0',
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `trash` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`),
  KEY `trash` (`trash`),
  KEY `scores` (`scores`),
  KEY `status` (`status`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_shop`;
CREATE TABLE IF NOT EXISTS `yl_welore_shop_attribute` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `at_name` varchar(255) NOT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_shop_attribute`;
CREATE TABLE IF NOT EXISTS `yl_welore_shop_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_number` varchar(500) DEFAULT NULL,
  `user_id` int(11) unsigned DEFAULT NULL,
  `is_offline` int(11) unsigned NOT NULL DEFAULT '0',
  `product_id` int(11) unsigned DEFAULT NULL,
  `vested_attribute` text,
  `product_img` varchar(1000) DEFAULT NULL,
  `product_name` varchar(1000) DEFAULT NULL,
  `product_price` decimal(18,2) unsigned DEFAULT NULL,
  `actual_price` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `pay_type` int(11) unsigned NOT NULL DEFAULT '0',
  `pay_status` int(11) unsigned NOT NULL DEFAULT '0',
  `remark` text,
  `buy_time` int(11) unsigned DEFAULT NULL,
  `shipment` varchar(1000) DEFAULT NULL,
  `buyer_name` varchar(50) DEFAULT NULL,
  `buyer_phone` varchar(50) DEFAULT NULL,
  `buyer_address` longtext,
  `is_noble` int(11) unsigned DEFAULT NULL,
  `product_discount` decimal(18,2) unsigned NOT NULL DEFAULT '1.00',
  `product_rebate` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `ship_time` int(11) unsigned DEFAULT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `reason_refund` longtext,
  `refund_count` int(11) unsigned NOT NULL DEFAULT '0',
  `trash` int(10) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`),
  KEY `trash` (`trash`),
  KEY `is_noble` (`is_noble`),
  KEY `status` (`status`),
  KEY `user_id` (`user_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_shop_order`;
CREATE TABLE IF NOT EXISTS `yl_welore_shop_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(500) DEFAULT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `stype_time` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`),
  KEY `scores` (`scores`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_shop_type`;
CREATE TABLE IF NOT EXISTS `yl_welore_shop_vested` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sp_id` int(11) unsigned NOT NULL,
  `sa_name` varchar(500) DEFAULT NULL,
  `sa_list` longtext,
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sp_id` (`sp_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_shop_vested`;
CREATE TABLE IF NOT EXISTS `yl_welore_special_nickname` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `special_name` varchar(500) NOT NULL,
  `special_style` varchar(500) NOT NULL,
  `unlock_outlay` decimal(22,0) unsigned NOT NULL,
  `prepare_time` int(11) unsigned NOT NULL,
  `status` int(11) NOT NULL DEFAULT '1',
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `purge_time` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_special_nickname`;
CREATE TABLE IF NOT EXISTS `yl_welore_sprout` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `templet_id` varchar(500) NOT NULL,
  `temp_name` varchar(255) DEFAULT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `at_user_id` int(11) unsigned NOT NULL DEFAULT '0',
  `content` varchar(2000) NOT NULL,
  `praise_number` int(11) unsigned NOT NULL DEFAULT '0',
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `send_time` int(11) unsigned NOT NULL,
  `check_time` int(11) unsigned NOT NULL DEFAULT '0',
  `reject_reason` varchar(500) DEFAULT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `at_user_id` (`at_user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_sprout`;
CREATE TABLE IF NOT EXISTS `yl_welore_sprout_reply` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sp_id` int(11) unsigned NOT NULL,
  `temp_name` varchar(255) DEFAULT NULL,
  `re_user_id` int(11) unsigned NOT NULL,
  `re_content` varchar(2000) NOT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `reply_time` int(11) unsigned NOT NULL,
  `check_time` int(11) unsigned NOT NULL DEFAULT '0',
  `reject_reason` varchar(500) DEFAULT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sp_id` (`sp_id`),
  KEY `re_user_id` (`re_user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_sprout_reply`;
CREATE TABLE IF NOT EXISTS `yl_welore_subscribe` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parallelism_data` longtext,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_subscribe`;
CREATE TABLE IF NOT EXISTS `yl_welore_sweepstake_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `custom_title` varchar(255) NOT NULL,
  `ad_1` varchar(255) NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_sweepstake_config`;
CREATE TABLE IF NOT EXISTS `yl_welore_sweepstake_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lottery_name` varchar(255) NOT NULL,
  `prize_list` longtext NOT NULL,
  `free_entry_count` int(11) unsigned NOT NULL,
  `video_entry_count` int(11) unsigned NOT NULL,
  `participant_num_limit` int(11) unsigned NOT NULL DEFAULT '0',
  `is_group` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `extract_type` int(11) unsigned NOT NULL,
  `random_extract_range_start` bigint(30) unsigned NOT NULL,
  `random_extract_range_end` bigint(30) unsigned NOT NULL,
  `start_time` int(11) unsigned NOT NULL,
  `end_time` int(11) unsigned NOT NULL,
  `draw_time` int(11) unsigned NOT NULL,
  `is_winning` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `campaign_desc` longtext,
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_sweepstake_list`;
CREATE TABLE IF NOT EXISTS `yl_welore_sweepstake_participate` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `sp_id` int(11) unsigned NOT NULL,
  `lucky_number` varchar(50) NOT NULL,
  `award_number_type` int(11) unsigned NOT NULL,
  `award_categories` int(11) unsigned NOT NULL,
  `award_level` int(11) unsigned NOT NULL,
  `award_type` int(11) unsigned NOT NULL,
  `is_award` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `is_payout_prizes` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `memo` varchar(500) DEFAULT NULL,
  `create_time` int(1) unsigned NOT NULL,
  `update_time` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_sweepstake_participate`;
CREATE TABLE IF NOT EXISTS `yl_welore_sweepstake_winning` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sp_id` int(11) unsigned NOT NULL,
  `prize_outcome` longtext NOT NULL,
  `prize_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_sweepstake_winning`;
CREATE TABLE IF NOT EXISTS `yl_welore_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_name` varchar(500) NOT NULL,
  `task_type` int(11) unsigned NOT NULL,
  `tory_id` int(11) unsigned NOT NULL DEFAULT '0',
  `task_cycle` int(11) unsigned NOT NULL,
  `task_reward_type` int(11) unsigned NOT NULL,
  `task_frequency` int(11) unsigned NOT NULL,
  `poor_task_salary` decimal(22,0) unsigned NOT NULL,
  `rich_task_salary` decimal(22,0) unsigned NOT NULL DEFAULT '0',
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `channel_time` int(11) unsigned NOT NULL,
  `is_being` int(11) unsigned NOT NULL DEFAULT '0',
  `death_time` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`),
  KEY `scores` (`scores`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_task`;
CREATE TABLE IF NOT EXISTS `yl_welore_task_logger` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `is_rich_person` int(11) unsigned NOT NULL,
  `task_salary` decimal(22,0) unsigned NOT NULL,
  `complete_description` varchar(1000) NOT NULL,
  `complete_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `task_id` (`task_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_task_logger`;
CREATE TABLE IF NOT EXISTS `yl_welore_template_slot` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `apollo` text NOT NULL,
  `apollo_history` text,
  `prometheus` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_template_slot`;
CREATE TABLE IF NOT EXISTS `yl_welore_template_slot_valve` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `petal_data` text NOT NULL,
  `is_reversal` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_template_slot_valve`;
CREATE TABLE IF NOT EXISTS `yl_welore_territory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `realm_icon` varchar(1000) DEFAULT NULL,
  `realm_name` varchar(255) DEFAULT NULL,
  `realm_back_img` varchar(2000) DEFAULT NULL,
  `needle_id` int(11) unsigned DEFAULT NULL,
  `realm_synopsis` longtext,
  `group_qrcode` varchar(1000) DEFAULT NULL,
  `release_count` int(11) unsigned NOT NULL DEFAULT '0',
  `release_level` int(11) unsigned NOT NULL DEFAULT '0',
  `visit_level` int(11) unsigned NOT NULL DEFAULT '0',
  `attention` int(11) unsigned NOT NULL DEFAULT '0',
  `atence` int(11) NOT NULL DEFAULT '0',
  `atcipher` longtext,
  `status` int(11) unsigned DEFAULT '0',
  `concern` int(11) unsigned NOT NULL DEFAULT '0',
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `rising_time` int(11) unsigned DEFAULT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `needle_id` (`needle_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_territory`;
CREATE TABLE IF NOT EXISTS `yl_welore_territory_interest` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned DEFAULT NULL,
  `tory_id` int(11) unsigned DEFAULT NULL,
  `reason` longtext,
  `sult_time` int(11) unsigned DEFAULT NULL,
  `rest_time` int(11) unsigned DEFAULT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `tory_id` (`tory_id`),
  KEY `sult_time` (`sult_time`),
  KEY `rest_time` (`rest_time`),
  KEY `status` (`status`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_territory_interest`;
CREATE TABLE IF NOT EXISTS `yl_welore_territory_learned` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tory_id` int(11) unsigned DEFAULT NULL,
  `snvite_bulord` text,
  `bulord` text,
  `envite_sulord` text,
  `sulord` text,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tory_id` (`tory_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_territory_learned`;
CREATE TABLE IF NOT EXISTS `yl_welore_territory_petition` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `realm_icon` varchar(255) DEFAULT NULL,
  `realm_name` varchar(255) DEFAULT NULL,
  `needle_id` int(11) unsigned DEFAULT NULL,
  `realm_synopsis` longtext,
  `solicit_origin` longtext,
  `user_id` int(11) unsigned DEFAULT NULL,
  `is_gnaw_qulord` int(11) unsigned DEFAULT NULL,
  `attention` int(11) unsigned NOT NULL DEFAULT '0',
  `solicit_rate` int(11) unsigned NOT NULL DEFAULT '1',
  `realm_status` int(11) unsigned NOT NULL DEFAULT '0',
  `denial_reason` longtext,
  `found_lasting` int(11) unsigned DEFAULT NULL,
  `review_lasting` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id` (`id`),
  KEY `needle_id` (`needle_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_territory_petition`;
CREATE TABLE IF NOT EXISTS `yl_welore_tribute` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tr_icon` varchar(500) DEFAULT NULL,
  `tr_name` varchar(500) DEFAULT NULL,
  `tr_conch` decimal(18,2) unsigned DEFAULT NULL,
  `status` int(11) unsigned DEFAULT '0',
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_tribute`;
CREATE TABLE IF NOT EXISTS `yl_welore_tribute_taxation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `taxing` decimal(18,2) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_tribute_taxation`;
CREATE TABLE IF NOT EXISTS `yl_welore_used_goods_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `item_type` int(11) unsigned NOT NULL,
  `release_type` int(11) unsigned NOT NULL,
  `item_name` varchar(500) NOT NULL,
  `item_price` varchar(255) DEFAULT NULL,
  `item_detail` longtext NOT NULL,
  `secondhand_address` varchar(500) NOT NULL,
  `contact_details` varchar(500) NOT NULL,
  `item_status` int(11) NOT NULL DEFAULT '1',
  `top_time` int(11) unsigned NOT NULL DEFAULT '0',
  `audit_status` int(11) unsigned NOT NULL DEFAULT '0',
  `audit_reason` varchar(500) DEFAULT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  FULLTEXT KEY `idx_search` (`item_name`,`item_detail`),
  FULLTEXT KEY `item_name` (`item_name`),
  FULLTEXT KEY `item_detail` (`item_detail`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_used_goods_item`;
CREATE TABLE IF NOT EXISTS `yl_welore_used_goods_item_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `custom_title` varchar(255) NOT NULL,
  `is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
  `reply_is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
  `top_twig` int(11) unsigned NOT NULL DEFAULT '0',
  `price_type` int(11) unsigned NOT NULL,
  `top_price` decimal(22,2) unsigned NOT NULL,
  `help_document` longtext NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_used_goods_item_config`;
CREATE TABLE IF NOT EXISTS `yl_welore_used_goods_item_reply` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `ugi_id` int(11) unsigned NOT NULL,
  `content` longtext NOT NULL,
  `is_secrecy` int(11) unsigned NOT NULL,
  `reply_time` int(11) unsigned NOT NULL,
  `audit_status` int(11) unsigned NOT NULL DEFAULT '0',
  `audit_reason` varchar(500) DEFAULT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_used_goods_item_reply`;
CREATE TABLE IF NOT EXISTS `yl_welore_used_goods_item_top` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usl_id` int(11) unsigned NOT NULL DEFAULT '0',
  `ugi_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `top_day` int(11) unsigned NOT NULL,
  `pay_type` int(11) unsigned NOT NULL,
  `pay_price` decimal(22,2) unsigned NOT NULL,
  `add_time` int(11) unsigned NOT NULL,
  `is_pay` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_used_goods_item_top`;
CREATE TABLE IF NOT EXISTS `yl_welore_used_goods_item_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '1',
  `sort` int(11) NOT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `is_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_used_goods_item_type`;
CREATE TABLE IF NOT EXISTS `yl_welore_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_head_sculpture` varchar(1000) DEFAULT NULL,
  `user_nick_name` varchar(255) DEFAULT NULL,
  `user_phone` varchar(500) DEFAULT NULL,
  `user_wechat_open_id` varchar(255) DEFAULT NULL,
  `user_wechat_union_id` varchar(255) DEFAULT NULL,
  `user_open_type` int(11) unsigned NOT NULL DEFAULT '0',
  `user_access_ip` varchar(500) DEFAULT NULL,
  `tourist` int(11) unsigned NOT NULL DEFAULT '0',
  `uvirtual` int(11) unsigned NOT NULL DEFAULT '0',
  `token` varchar(255) DEFAULT NULL,
  `token_impede` int(11) unsigned NOT NULL DEFAULT '0',
  `user_home_access_status` int(11) unsigned NOT NULL DEFAULT '1',
  `gender` int(11) unsigned NOT NULL DEFAULT '0',
  `level` int(11) unsigned NOT NULL DEFAULT '0',
  `fraction` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `conch` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `experience` decimal(22,0) unsigned NOT NULL DEFAULT '0',
  `honor_point` decimal(22,0) unsigned NOT NULL DEFAULT '0',
  `wear_af` int(11) unsigned NOT NULL DEFAULT '0',
  `wear_merit` varchar(1000) NOT NULL DEFAULT '0',
  `wear_special_id` int(11) unsigned NOT NULL DEFAULT '0',
  `autograph` longtext,
  `buyer_name` varchar(50) DEFAULT NULL,
  `buyer_phone` varchar(50) DEFAULT NULL,
  `buyer_address` varchar(500) DEFAULT NULL,
  `virtual_fans_num` int(11) unsigned NOT NULL DEFAULT '0',
  `is_enable_fans_privacy` int(11) unsigned NOT NULL DEFAULT '0',
  `is_enable_concern_privacy` int(11) unsigned NOT NULL DEFAULT '0',
  `vip_end_time` int(11) unsigned NOT NULL DEFAULT '0',
  `nick_name_time` int(11) unsigned NOT NULL DEFAULT '0',
  `forbid_prompt` text,
  `user_reg_time` int(11) unsigned DEFAULT NULL,
  `user_last_time` int(11) unsigned DEFAULT NULL,
  `status` int(11) unsigned DEFAULT '1',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_wechat_open_id` (`user_wechat_open_id`),
  UNIQUE KEY `user_wechat_open_id_2` (`user_wechat_open_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_amount` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `serial_id` varchar(255) DEFAULT NULL,
  `user_id` int(11) unsigned DEFAULT NULL,
  `category` int(11) unsigned DEFAULT NULL,
  `finance` decimal(18,2) DEFAULT NULL,
  `poem_fraction` decimal(18,2) unsigned DEFAULT '0.00',
  `poem_conch` decimal(18,2) unsigned DEFAULT '0.00',
  `surplus_fraction` decimal(18,2) unsigned DEFAULT '0.00',
  `surplus_conch` decimal(18,2) unsigned DEFAULT '0.00',
  `ruins_time` int(11) unsigned DEFAULT NULL,
  `solution` varchar(500) DEFAULT NULL,
  `evaluate` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `serial_id` (`serial_id`),
  KEY `user_id` (`user_id`),
  KEY `category` (`category`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_amount`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_applaud` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) unsigned DEFAULT NULL,
  `user_id` int(11) unsigned DEFAULT NULL,
  `applaud_type` int(11) unsigned DEFAULT '0',
  `laud_time` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id` (`id`),
  KEY `paper_id` (`paper_id`),
  KEY `user_id` (`user_id`),
  KEY `applaud_type` (`applaud_type`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_applaud`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_attest` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `at_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `postback_data` longtext NOT NULL,
  `adopt_status` int(11) unsigned NOT NULL DEFAULT '0',
  `refer_time` int(11) unsigned NOT NULL,
  `refuse_time` int(11) NOT NULL DEFAULT '0',
  `ut_inject` varchar(500) DEFAULT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_attest`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_avatar_frame` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `af_id` int(11) unsigned NOT NULL,
  `unlock_outlay` decimal(22,0) unsigned NOT NULL,
  `unlock_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `medal_id` (`af_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_avatar_frame`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_banned` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tory_id` int(11) unsigned DEFAULT NULL,
  `refer_id` int(11) unsigned DEFAULT NULL,
  `refer_type` int(11) unsigned DEFAULT NULL,
  `user_id` int(11) unsigned DEFAULT NULL,
  `beget` longtext,
  `refer_time` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tory_id` (`tory_id`),
  KEY `refer_id` (`refer_id`),
  KEY `refer_type` (`refer_type`),
  KEY `user_id` (`user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_banned`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_better_logger` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `duration` int(11) unsigned NOT NULL,
  `buy_price` decimal(18,2) unsigned NOT NULL,
  `buy_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_better_logger`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `da_user_id` int(11) unsigned NOT NULL,
  `pu_user_id` int(11) unsigned NOT NULL,
  `bl_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `da_user_id` (`da_user_id`),
  KEY `pu_user_id` (`pu_user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

CREATE TABLE IF NOT EXISTS `yl_welore_user_brisk_team` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) unsigned NOT NULL,
  `brisk_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `rand_captcha` varchar(500) NOT NULL,
  `partake_time` int(11) unsigned NOT NULL,
  `is_write_off` int(11) unsigned NOT NULL DEFAULT '0',
  `write_off_time` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_brisk_team`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_camouflage_card` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ccid` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `expired_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_camouflage_card`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_collect` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned DEFAULT NULL,
  `paper_id` int(11) unsigned DEFAULT NULL,
  `create_time` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`),
  KEY `user_id` (`user_id`),
  KEY `paper_id` (`paper_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_collect`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_currency_conversion` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `conver_type` int(11) unsigned NOT NULL,
  `conver_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_currency_conversion`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_exp_glory_logger` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `type` int(11) unsigned NOT NULL,
  `cypher` int(11) unsigned NOT NULL,
  `dot_before` decimal(22,0) unsigned NOT NULL,
  `points` decimal(22,0) unsigned NOT NULL,
  `dot_after` decimal(22,0) unsigned NOT NULL,
  `dot_cap` varchar(1000) NOT NULL,
  `receive_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_exp_glory_logger`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_feeling` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `fg_id` int(11) unsigned NOT NULL,
  `pull_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_feeling`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_forwarded` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paper_id` int(11) unsigned NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `fulfill_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `paper_id` (`paper_id`),
  KEY `user_id` (`user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_forwarded`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_honorary` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chop_type` int(11) unsigned NOT NULL DEFAULT '0',
  `hono_price` decimal(18,2) unsigned DEFAULT NULL,
  `first_discount` int(11) unsigned NOT NULL DEFAULT '0',
  `discount_scale` decimal(18,2) unsigned DEFAULT NULL,
  `define_price` varchar(5000) DEFAULT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`),
  KEY `much_id_2` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_honorary`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_invitation_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned DEFAULT NULL,
  `code` varchar(1000) DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_invitation_code`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_leaderboard` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ranking_name` varchar(255) NOT NULL,
  `wont_sort` text NOT NULL,
  `status` int(11) unsigned NOT NULL,
  `scores` int(11) NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_leaderboard`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_leave_word` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `se_user_id` int(11) unsigned NOT NULL,
  `re_user_id` int(11) unsigned NOT NULL,
  `le_content` longtext NOT NULL,
  `le_type` int(11) unsigned NOT NULL DEFAULT '0',
  `le_read_status` int(11) unsigned NOT NULL DEFAULT '1',
  `se_user_del` int(11) unsigned NOT NULL DEFAULT '0',
  `re_user_del` int(11) unsigned NOT NULL DEFAULT '0',
  `le_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `se_user_id` (`se_user_id`),
  KEY `re_user_id` (`re_user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_leave_word`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_level` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level_hierarchy` int(11) unsigned NOT NULL,
  `level_name` varchar(500) NOT NULL,
  `level_icon` varchar(1000) NOT NULL,
  `need_experience` decimal(22,0) unsigned NOT NULL,
  `honor_point` decimal(22,0) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_level`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_maker` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_open_id` varchar(500) DEFAULT NULL,
  `found_time` int(11) unsigned DEFAULT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `scores` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_maker`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_medal` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `medal_id` int(11) unsigned NOT NULL,
  `unlock_outlay` decimal(22,0) unsigned NOT NULL,
  `unlock_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `medal_id` (`medal_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_medal`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_mutter` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned DEFAULT NULL,
  `tory_id` int(11) unsigned DEFAULT NULL,
  `ban_id` int(11) unsigned DEFAULT NULL,
  `beget` longtext,
  `mute_time` int(11) unsigned DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT '0',
  `mute_type` int(11) NOT NULL DEFAULT '0',
  `reason_refusal` text,
  `much_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `tory_id` (`tory_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_mutter`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_punch` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned DEFAULT NULL,
  `fraction` decimal(18,2) unsigned DEFAULT NULL,
  `punch_time` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_punch`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_punch_range` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `currency_redemption_channel` int(11) unsigned NOT NULL DEFAULT '0',
  `fraction_redemption_channel` int(11) unsigned NOT NULL DEFAULT '1',
  `fraction_scale` int(11) unsigned NOT NULL DEFAULT '10',
  `currency_icon` varchar(1000) NOT NULL,
  `aver_min` decimal(18,2) unsigned DEFAULT '0.00',
  `aver_max` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `noble_min` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `noble_max` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `invite_min` decimal(18,2) NOT NULL DEFAULT '0.00',
  `invite_max` decimal(18,2) NOT NULL DEFAULT '0.00',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_punch_range`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_raffle_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `record_number` varchar(500) NOT NULL,
  `user_id` int(11) unsigned NOT NULL,
  `er_id` int(11) unsigned NOT NULL,
  `join_time` int(11) unsigned NOT NULL,
  `win_type` int(11) unsigned NOT NULL,
  `prize_name` varchar(500) NOT NULL,
  `reward_score` decimal(18,0) unsigned NOT NULL DEFAULT '0',
  `address_details` varchar(1000) DEFAULT NULL,
  `courier_convert` varchar(500) DEFAULT NULL,
  `delivery_status` int(11) unsigned NOT NULL DEFAULT '0',
  `see_time` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_raffle_records`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_recent_contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chat_user_id` int(11) unsigned NOT NULL,
  `recent_user_id` int(11) unsigned NOT NULL,
  `blatter_time` int(11) unsigned NOT NULL,
  `last_msg` longtext NOT NULL,
  `blatter_del` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `chat_user_id` (`chat_user_id`),
  KEY `recent_user_id` (`recent_user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_recent_contacts`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_red_packet` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned DEFAULT NULL,
  `reply_id` int(11) unsigned NOT NULL DEFAULT '0',
  `red_packet_id` int(11) unsigned DEFAULT NULL,
  `obtain_fraction` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `obtain_conch` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `obtain_time` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `red_packet_id` (`red_packet_id`),
  KEY `obtain_time` (`obtain_time`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_red_packet`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_respond_invitation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned DEFAULT NULL,
  `re_code` varchar(1000) DEFAULT NULL,
  `in_us_reward` decimal(18,2) NOT NULL DEFAULT '0.00',
  `re_us_reward` decimal(18,2) NOT NULL DEFAULT '0.00',
  `re_time` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_respond_invitation`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_screenshot` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `scene_name` varchar(255) NOT NULL,
  `scene_path` varchar(500) NOT NULL,
  `location_ip` varchar(500) NOT NULL,
  `add_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_screenshot`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_serial` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `single_mark` varchar(500) DEFAULT NULL,
  `status` int(11) unsigned DEFAULT '0',
  `user_id` int(11) unsigned NOT NULL,
  `add_time` int(11) unsigned NOT NULL DEFAULT '0',
  `money` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `pay_money` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_serial`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_smail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `skip_type` int(11) unsigned NOT NULL DEFAULT '0',
  `paper_id` varchar(255) DEFAULT NULL,
  `maring` longtext,
  `clue_time` int(11) unsigned DEFAULT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_smail`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_special_nickname` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `special_id` int(11) unsigned NOT NULL,
  `unlock_outlay` decimal(22,0) unsigned NOT NULL,
  `unlock_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `medal_id` (`special_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_special_nickname`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_subsidy` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `con_user_id` int(11) unsigned DEFAULT NULL,
  `sel_user_id` int(11) unsigned DEFAULT NULL,
  `bute_name` varchar(500) DEFAULT NULL,
  `bute_price` decimal(18,2) DEFAULT NULL,
  `exchange_rate` int(11) unsigned NOT NULL DEFAULT '10',
  `allow_scale` decimal(18,2) unsigned DEFAULT NULL,
  `bute_time` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sel_user_id` (`sel_user_id`),
  KEY `con_user_id` (`con_user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_subsidy`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_track` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `at_user_id` int(10) unsigned DEFAULT NULL,
  `qu_user_id` int(10) unsigned DEFAULT NULL,
  `fo_time` int(11) unsigned DEFAULT NULL,
  `much_id` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `at_user_id` (`at_user_id`),
  KEY `qu_user_id` (`qu_user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_track`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_trailing` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned DEFAULT NULL,
  `tory_id` int(11) unsigned DEFAULT NULL,
  `ling_time` int(11) unsigned DEFAULT NULL,
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `tory_id` (`tory_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_trailing`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_violation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `open_network_images_offend` int(11) unsigned NOT NULL DEFAULT '1',
  `nickname_offend` longtext,
  `open_network_nickname_offend` int(11) unsigned NOT NULL DEFAULT '1',
  `content_offend` longtext,
  `open_network_content_offend` int(11) unsigned NOT NULL DEFAULT '1',
  `tencent_cloud_content_security_config` text,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_violation`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_vote` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `paper_id` int(11) unsigned NOT NULL,
  `pv_id` int(11) unsigned NOT NULL,
  `decide_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_vote`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_watch_ads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `ad_type` int(11) unsigned NOT NULL,
  `fulfill_type` int(11) unsigned NOT NULL,
  `fulfill_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_watch_ads`;
CREATE TABLE IF NOT EXISTS `yl_welore_user_withdraw_money` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned DEFAULT NULL,
  `user_account` varchar(500) DEFAULT NULL,
  `display_money` decimal(18,2) NOT NULL DEFAULT '0.00',
  `tariff` decimal(19,3) unsigned NOT NULL DEFAULT '0.000',
  `actual_amount` decimal(18,2) NOT NULL DEFAULT '0.00',
  `withdraw_type` int(11) unsigned NOT NULL DEFAULT '0',
  `seek_time` int(11) unsigned DEFAULT NULL,
  `verify_time` int(11) unsigned DEFAULT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `much_id` (`much_id`),
  KEY `user_id` (`user_id`),
  KEY `withdraw_type` (`withdraw_type`),
  KEY `seek_time` (`seek_time`),
  KEY `verify_time` (`verify_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_user_withdraw_money`;
CREATE TABLE IF NOT EXISTS `yl_welore_version` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sign_code` varchar(500) DEFAULT NULL,
  `status` int(11) unsigned NOT NULL DEFAULT '0',
  `much_id` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_version`;
CREATE TABLE IF NOT EXISTS `yl_welore_version_overdue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sign_code` varchar(255) NOT NULL,
  `update_time` int(11) NOT NULL,
  `most_above_time` int(11) NOT NULL DEFAULT '0',
  `much_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_version_overdue`;
CREATE TABLE IF NOT EXISTS `yl_welore_video_analysis_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `parse_name` varchar(255) NOT NULL,
  `interface_type` int(11) unsigned NOT NULL DEFAULT '0',
  `adaptation_domain` text,
  `parse_url` text NOT NULL,
  `req_method` int(11) unsigned NOT NULL,
  `req_type` int(11) unsigned NOT NULL,
  `req_params` text NOT NULL,
  `res_params` text NOT NULL,
  `default_video_cover` text,
  `is_default` int(11) unsigned NOT NULL DEFAULT '0',
  `app_remark` text NOT NULL,
  `create_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_video_analysis_config`;
CREATE TABLE IF NOT EXISTS `yl_welore_wx_popular` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `wx_app_qrcode` varchar(2000) DEFAULT NULL,
  `wx_app_name` varchar(255) DEFAULT NULL,
  `wx_app_id` varchar(1000) DEFAULT NULL,
  `wx_app_secret` varchar(1000) DEFAULT NULL,
  `access_token` varchar(1000) DEFAULT NULL,
  `token_exp_time` int(11) unsigned NOT NULL DEFAULT '0',
  `imagine_data` text,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `much_id` (`much_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_wx_popular`;
CREATE TABLE IF NOT EXISTS `yl_welore_wx_popular_bind_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL,
  `open_id` varchar(500) NOT NULL,
  `union_id` varchar(500) DEFAULT NULL,
  `bind_time` int(11) unsigned NOT NULL,
  `much_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;

TRUNCATE TABLE `yl_welore_wx_popular_bind_user`;
EOT;
pdo_run($sql);