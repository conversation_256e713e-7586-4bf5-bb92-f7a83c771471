<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="referrer" content="never">
    <title>图片管理</title>
</head>
<link rel="stylesheet" href="assets/css/bootstrap.min.css?v=1.0">
<link rel="stylesheet" href="assets/css/amazeui.min.css?v=1.0"/>
<style>a,a:hover{text-decoration:none;}.pagination{font-size:12px;}.left-li{color:#ffffff;text-align:center;padding:12px 0px;font-size:14px;cursor:pointer;}.right-img{float:left;}::-webkit-scrollbar{width:3px;height:3px;background-color:#F5F5F5;}::-webkit-scrollbar-track{-webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0.3);border-radius:10px;background-color:#F5F5F5;}::-webkit-scrollbar-thumb{border-radius:10px;-webkit-box-shadow:inset 0 0 6px rgba(0,0,0,.3);background-color:#555;}.select_group{background-image:linear-gradient(120deg,#89f7fe 0%,#66a6ff 100%);}.right-x{position:absolute;right:10px;top:-14px;height:30px;width:30px;background-color:rgba(255,0,51,0.7);border-radius:50%;text-align:center;line-height:30px;cursor:pointer;}.right-img-li{display:flex;justify-content:center;align-items:center;cursor:pointer;width:120px;height:120px;box-shadow:10px 5px 10px 0px rgb(204,204,204);border-radius:5px;}.img-select{box-shadow:0px 0px 5px 5px rgb(102,153,255) !important;}.jq-ui-button{cursor:pointer;border:0;outline:0;font-size:14px;padding:4px 18px;display:inline-block;vertical-align:middle;overflow-wrap:break-word;border-radius:4px;color:#fff;box-sizing:border-box;cursor:pointer;}.jq-ui-button-blue{background:#57799c;}.jq-ui-button-red{background:#cb6262;}</style>
<body style="width:100%;height:100%;">
<div class="am-g">
    <div class="am-u-sm-2" style="padding:0px;">
        <div style="min-height:600px;;background-color: #212121">
            <div style="color: #fff;text-align: center;padding: 30px 0px 15px 0px;;font-size: 20px;font-weight: 700;">
                图库编辑 <span class="am-icon-picture-o"></span>
            </div>
            <div style="cursor:pointer;margin: 30px auto;color:#ffffff;background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;" onclick="sngchoice();">
                <span class="am-icon-cloud-upload"></span> 批量上传
            </div>
            <div id="custom-network-image" style="cursor:pointer;margin: 30px auto;color:#ffffff;background-image: linear-gradient(-60deg, #ff5858 0%, #f09819 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;">
                <span class="am-icon-cloud"></span> 网络图片
            </div>
            <form id="snup" style="display: none;">
                <input type="file" id="sngpic" name="sngpic" accept="image/*" multiple onchange="snuload();">
            </form>
            <div style="cursor:pointer;margin: 30px auto;color:#ffffff;background-image: linear-gradient(to right, #ff758c 0%, #ff7eb3 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;" onclick="unImgsBatch();">
                <span class="am-icon-times"></span> 批量删除
            </div>
            <div id="umoving" data-am-modal="{target: '#plectron', closeViaDimmer: 0, width: 400, height: 225}" style="cursor:pointer;margin: 30px auto;color:#ffffff;background-image: linear-gradient(-20deg, #00cdac 0%, #8ddad5 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;">
                <span class="am-icon-exchange"></span> 移动图片
            </div>
        </div>
    </div>
    <div class="am-u-sm-10" style="padding:0px;">
        <div  style="width: 100%;min-height:600px;box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 1);">
            <div style="cursor:pointer;margin-left: 20px;float:left;margin-top: 25px;color:#ffffff;background-image: linear-gradient(to top, #1e3c72 0%, #1e3c72 1%, #2a5298 100%);height: 30px;width: 90px;font-size: 10px;text-align: center;line-height: 30px;border-radius: 20px;" onclick="uselectAll();">
                <span class="am-icon-check-square-o"></span>全部选择
            </div>
            <div style="clear:both;height:0"></div>
            <div class="test-5" style="padding: 20px;height: 436px;width: 100%;margin-top:10px;overflow: auto;">
                <ul class="am-avg-sm-5 am-thumbnails">
                    {volist name="gallery" id="vo"}
                    <li style="position: relative;text-align: center;">
                        <div class="right-img-li" onclick="spicking(this);" data-urid="{$vo.id}">
                            <img src="{$vo.img_url}" 
                                 data-src="{$vo.img_url}"
                                 alt="{$vo.img_title}" 
                                 title="{$vo.img_title}" 
                                 style="max-height:100%; max-width:100%;object-fit: fill;" 
                                 onerror="this.src='static/wechat/image_vip_top.jpg'"/>
                        </div>
                    </li>
                    {/volist}
                </ul>
            </div>
            <div style="text-align: center;">
                {$gallery->render()}
            </div>
        </div>
    </div>
</div>
<div class="am-modal am-modal-no-btn" tabindex="-1" id="plectron">
    <div class="am-modal-dialog" style="background: #ffffff;">
        <div class="am-modal-hd" style="margin-top: 30px;">选择目标图库
            <a href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
        </div>
        <div class="am-form">
            <div class="am-modal-bd">
                <div class="am-form-group">
                    <select id="gclasid">
                        {volist name="gclassify" id="vo"}
                        <option value="{$vo.id}">{$vo.name}</option>
                        {/volist}
                    </select>
                    <span class="am-form-caret"></span>
                </div>
            </div>
        </div>
        <div style="cursor:pointer;margin: 0px auto;color:#ffffff;background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;" onclick="galleryStir();">
            确定
        </div>
    </div>
</div>
<div id="custom-modal-backdrop" style="display:none;width:100%;height:100%;position:absolute;top:0;left:0;background:#0a0a0a;z-index:999;opacity:0.4;"></div>
<div id="custom-modal-reveal" style="display:none;width:60%;height:30%;position:absolute;top:30%;left:22%;margin:0 auto;background:#f8f8f8;z-index:1000;text-align:center;">
    <div style="width:100%;color:#333;font-weight:bold;font-size:2.2rem;margin-top:2.0rem;">
        网络图片地址
    </div>
    <div id="imageAppendReset" style="width:100%;">
        <div id="imageAppend">
            <div class="image-append-offspring" style="clear:both;">
                <div style="width:72%;float:left;text-align:right;margin-top:15px;">
                    <input class="network-image-address" type="text" style="height:28px;width:80%;">
                </div>
                <div style="width:28%;float:left;text-align:left;font-weight:500;font-size:18px;margin-top:15px;">
                <span style="color:#333;margin-left:10px;">
                    <button class="jq-ui-button jq-ui-button-blue" onclick="increasedNetworkImageAddress();">增加地址栏</button>
                </span>
                </div>
            </div>
        </div>
    </div>
    <div style="width:100%;position:absolute;bottom:0px;color:#0e90d2;font-size:1.6rem;">
        <div style="float:left;width:50%;border-top:1px solid #dedede;border-right:1px solid #dedede;padding:10px 0;cursor:pointer;" onclick="increaseWebImage();">确定</div>
        <div id="custom-modal-close" style="float:left;width:50%;border-top:1px solid #dedede;padding:10px 0;cursor:pointer;">取消</div>
    </div>
</div>
</body>
<script src="assets/js/jquery.min.js?v=1.0"></script>
<script src="assets/js/amazeui.min.js?v=1.0"></script>
<script src="assets/js/bootstrap.min.js?v=1.0"></script>
<script>

    !function () {
        $('#umoving').click(function () {
            var i = 0;
            $('.right-img-li').each(function () {
                if ($(this).hasClass('img-select')) {
                    i++;
                    return false;
                }
            });
            if (i == 0) {
                parent.layer.msg('请选择要移动的图片');
                return false;
            }
        });
        $('#custom-network-image').click(function () {
            $('#network-image-address').val('');
            $('#custom-modal-backdrop').show('slow');
            var resetHtml = '<div id="imageAppend">\n' +
                '            <div class="image-append-offspring" style="clear:both;">\n' +
                '                <div style="width:72%;float:left;text-align:right;margin-top:15px;">\n' +
                '                    <input class="network-image-address" type="text" style="height:28px;width:80%;">\n' +
                '                </div>\n' +
                '                <div style="width:28%;float:left;text-align:left;font-weight:500;font-size:18px;margin-top:15px;">\n' +
                '                <span style="color:#333;margin-left:10px;">\n' +
                '                    <button class="jq-ui-button jq-ui-button-blue" onclick="increasedNetworkImageAddress();">增加地址栏</button>\n' +
                '                </span>\n' +
                '                </div>\n' +
                '            </div>\n' +
                '        </div>';
            $('#imageAppendReset').html(resetHtml);
            $('#custom-modal-reveal').css({'height': '30%', 'top': '30%'});
            $('#custom-modal-reveal').show('slow');
        });
        $('#custom-modal-close').click(function () {
            $('#network-image-address').val('');
            $('#custom-modal-backdrop').hide('slow');
            $('#custom-modal-reveal').hide('slow');
        });
    }();

    var sngchoice = function () {
        $('#sngpic').click();
    }

    var snuload = function () {
        var imgQualified = true;
        $($('#sngpic').get(0).files).each(function (i) {
            var suffix = $('#sngpic').get(0).files[i].name.lastIndexOf('.');
            var ext = $('#sngpic').get(0).files[i].name.substring(suffix, $('#sngpic').get(0).files[i].name.length).toLowerCase();
            if (ext != '.png' && ext != '.gif' && ext != '.jpg' && ext != '.jpeg' && ext != '.bmp') {
                parent.layer.msg('批量选择文件类型错误,请上传图片类型');
                $('#sngpic').val('');
                imgQualified = false;
                return false;
            }
        });
        if (imgQualified) {
            parent.layer.load();
            $($('#sngpic').get(0).files).each(function (i) {
                var formData = new FormData();
                formData.append('sngpic', $('#sngpic').get(0).files[i]);
                $.ajax({
                    type: "post",
                    url: "{:url('upload/operate')}&gclasid={$gclasid}&picture=library",
                    async: false,
                    data: formData,
                    processData: false,
                    contentType: false,
                    dataType: 'json',
                    success: function (data) {
                        setTimeout(function () {
                            parent.layer.closeAll('loading');
                            if (data.status == 'success') {
                                if (($('#sngpic').get(0).files.length - 1) == i) {
                                    handleHttpImages();
                                    location.reload();
                                }
                            } else {
                                parent.layer.msg('上传失败，请检查上传配置');
                                $('#sngpic').val('');
                                return false;
                            }
                        }, 1600);
                    }
                });
            });
        }
    }

    var unImgsBatch = function () {
        var selectedImages = $('.img-select');
        if (selectedImages.length === 0) {
            parent.layer.msg('请选择要删除的图片');
            return;
        }

        selectedImages.each(function() {
            var euid = $(this).attr('data-urid');
            $.ajax({
                type: "post",
                url: "{:url('images/unimgs')}",
                async: false,
                data: {'euid': parseInt(euid)},
                dataType: 'json',
                success: function(data) {
                    if (data.code > 0) {
                        parent.layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        parent.layer.msg(data.msg, {icon: 5, time: 1000});
                    }
                }
            });
        });
    }

    var increasedNetworkImageAddress = function () {
        var cmrHeight = $('#custom-modal-reveal').height();
        if (cmrHeight < 450) {
            var cmrOffset = $('#custom-modal-reveal').offset();
            $('#custom-modal-reveal').height(cmrHeight + 45 + 'px');
            if (cmrOffset.top > 80) {
                $('#custom-modal-reveal').css('top', cmrOffset.top - 20 + 'px');
            }
        } else {
            $('#imageAppend').css({'height': '315px', 'overflow-y': 'auto'});
        }
        var html = '<div class="image-append-offspring" style="clear:both;">\n' +
            '                <div style="width:72%;float:left;text-align:right;margin-top:15px;">\n' +
            '                    <input class="network-image-address" type="text" style="height:28px;width:80%;">\n' +
            '                </div>\n' +
            '                <div style="width:28%;float:left;text-align:left;font-weight:500;font-size:18px;margin-top:15px;">\n' +
            '                <span style="color:#333;margin-left:10px;">\n' +
            '                    <button class="jq-ui-button jq-ui-button-red" onclick="removeNetworkImageAddress(this);">移除地址栏</button>\n' +
            '                </span>\n' +
            '                </div>\n' +
            '            </div>';
        $('#imageAppend').append(html);
    }

    var removeNetworkImageAddress = function (obj) {
        $(obj).parent().parent().parent().remove();
        var offspringHeight = 0;
        $('.image-append-offspring').each(function () {
            offspringHeight += 45;
        });
        if (offspringHeight < 315) {
            var cmrHeight = $('#custom-modal-reveal').height();
            var cmrOffset = $('#custom-modal-reveal').offset();
            $('#custom-modal-reveal').height(cmrHeight - 45 + 'px');
            $('#custom-modal-reveal').css('top', cmrOffset.top + 20 + 'px');
            $('#imageAppend').css({'height': 'auto', 'overflow-y': 'visible'});
        }
    }

    var increaseWebImage = function () {
        var i = 0;
        var j = 0;
        $('.network-image-address').each(function () {
            i++;
            var webImg = $.trim($(this).val());
            $.ajaxSettings.async = false;
            $.post("{:url('images/pullNetworkPicture')}", {
                'gclasid': '{$gclasid}', 
                'webImg': webImg
            }, function (data) {
                if (data.code > 0) {
                    j++;
                }
            });
            $.ajaxSettings.async = true;
        });
        if (i == j) {
            parent.layer.msg('添加成功', {icon: 1, time: 1600}, function () {
                location.reload();
            });
        } else {
            parent.layer.msg('未知错误', {icon: 5, time: 2000});
        }
    }

    var galleryStir = function () {
        var j = 0;
        $('.img-select').each(function () {
            var euid = $(this).attr('data-urid');
            var gclasid = $('#gclasid').val();
            $.ajax({
                type: "post",
                url: "{:url('images/stirGallery')}",
                async: false,
                data: {'euid': parseInt(euid), 'gclasid': gclasid},
                dataType: 'json',
                success: function (data) {
                    j++;
                    if (j == $('.img-select').length) {
                        if (data.code > 0) {
                            parent.layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            parent.layer.msg(data.msg, {icon: 5, time: 1000});
                        }
                    }
                }
            });
        });
    }

    var spicking = function (obj) {
        if ($(obj).hasClass('img-select')) {
            $(obj).removeClass('img-select');
        } else {
            $(obj).addClass('img-select');
        }
    }
    var uselectAll = function () {
        var i = 0;
        var j = 0;
        $('.right-img-li').each(function () {
            if ($(this).hasClass('img-select')) {
                i++;
            }
            j++;
        });
        if (i == j) {
            var hasClassImgSelect = false;
        } else {
            var hasClassImgSelect = true;
        }
        if (hasClassImgSelect) {
            $('.right-img-li').addClass('img-select');
        } else {
            $('.right-img-li').removeClass('img-select');
        }
    }

    function handleHttpImages() {
        if (window.location.protocol === 'https:') {
            document.querySelectorAll('img[src^="http://"]').forEach(function(img) {
                var originalSrc = img.getAttribute('data-src');
                var proxyUrl = "{:url('urge/proxy/proxy_resource')}&url=" + encodeURIComponent(originalSrc);
                
                var tmpImg = new Image();
                tmpImg.onload = function() {
                    // 原始图片可访问，不做处理
                }
                tmpImg.onerror = function() {
                    img.src = proxyUrl;
                }
                tmpImg.src = originalSrc;
            });
        }
    }

    $(document).ready(function() {
        handleHttpImages();
    });

    var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                handleHttpImages();
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
</script>
</html>
