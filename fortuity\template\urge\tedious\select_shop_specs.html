<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>选择商品规格</title>
    <link rel="stylesheet" href="./assets/css/app.css">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css">
    <style>
        div.am-cf.text-center > span{
            top: 26px !important;
            right: -10px !important;
        }

        .select-transfer {
            cursor: pointer;
        }

        .select-transfer:hover {
            color: red;
        }
    </style>
</head>
<body>
<div class="am-g">
    <div style="display: flex;margin: 5px 0 10px 0;">
        <div style="width: 50%;">
            <button class="search-wide-pitch search-btn" style="margin-left: 5px !important;" data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 400, height: 200}">
                <i class="am-icon-code"></i> 新增规格
            </button>
        </div>
        <div id="search-list" style="display: flex;flex-wrap: wrap;width: 50%;min-height: 38px;">
            <div class="search-wide-pitch search-inline">
                <label class="search-label">规格名称</label>
                <div class="search-input-inline">
                    <input type="text" name="hazy_name" value="{$hazy_name}" class="search-input">
                </div>
            </div>
            <button class="search-wide-pitch search-btn" onclick="turtle();">
                <i class="am-icon-search"></i> 搜 索
            </button>
        </div>
    </div>
    <div class="am-form" style="min-height: 485px;">
        <table class="am-table am-table-striped am-table-hover table-main">
            <tr>
                <th class="text-center" style="width:25%;">
                    ID
                </th>
                <th class="text-center" style="width:25%;">
                    规格名称
                </th>
                <th class="text-center" style="width:25%;">
                    创建时间
                </th>
                <th class="text-center" style="width:25%;">
                    操作
                </th>
            </tr>
            {volist name="list" id="vo"}
            <tr>
                <td class="text-center" style="line-height: 70px;">
                    {$vo.id}
                </td>
                <td class="text-center" style="line-height: 70px;">
                    <span title="{$vo.at_name}">
                    {$vo.at_name}
                    </span>
                </td>
                <td class="text-center" style="line-height: 70px;">
                    {:date('Y-m-d H:i:s',$vo.create_time)}
                </td>
                <td class="text-center" style="line-height: 70px;">
                    <span class="select-transfer" onclick="transferAttribute('{$vo.id}','{$vo.at_name}');">
                        选择
                    </span>
                </td>
            </tr>
            {/volist}
        </table>
    </div>
</div>
<div class="am-cf text-center" style="height: 91px;">
    {$list->render()}
</div>
<div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
    <div class="am-modal-dialog" style="background:#fefefe;">
        <div class="am-modal-hd">
            <span class="am-modal-custom-name" style="font-size: 14px;position: absolute;left:12px;top:7px;">新增规格</span>
            <a id="euModalClose" href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
        </div>
        <div class="am-modal-bd am-form tpl-form-line-form">
            <div class="am-form-group" style="margin-top:35px;">
                <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">规格名称</label>
                <div class="am-u-sm-8">
                    <input type="hidden" name="fid" value="0">
                    <input type="text" name="atName" class="tpl-form-input" style="margin:3px 0 0 -20px;" placeholder="请输入规格名称">
                </div>
            </div>
            <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top:10px;">
                <button type="button" class="am-btn am-btn-sm hold-save" style="border:1px solid #ccc;" onclick="sendGifts(0);">
                    确定保存
                </button>
            </div>
        </div>
    </div>
</div>
</body>
<script src="assets/js/jquery.min.js"></script>
<script src="assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<script src="./static/layer/layer.js"></script>
<script>

    function turtle() {
        var searchInput = $('#search-list .search-input');
        var n = 0;
        for (var i = 0; i < searchInput.length; i++) {
            if (searchInput[i].value.trim() !== '') {
                n++;
            }
        }
        if (n > 0) {
            location.href = `{:url('tedious/select_shop_specs')}&${searchInput.serialize()}&page={$page}`;
        } else {
            location.href = "{:url('tedious/select_shop_specs')}&page={$page}";
        }
    }

    var isLock = false;
    var sendGifts = function (type) {
        if (!isLock) {
            isLock = true;
            var postUrl = "{:url('tedious/add_shop_specs')}";
            var setData = {};
            if (type === 1) {
                postUrl = "{:url('tedious/update_shop_specs')}";
                setData['fid'] = $('.am-modal-dialog [name=\'fid\']').val();
            }
            setData['atName'] = $('.am-modal-dialog [name=\'atName\']').val();
            $.post(postUrl, setData, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        isLock = false;
                    });
                }
            }, 'json');
        }
    }


    var transferAttribute = function (sid, atName) {
        parent.layer.confirm('您确定要选择当前规格吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            parent.AttributeRefrain(Number(sid), atName);
        }, function (index) {
            parent.layer.close(index);
        });
    }
</script>
</html>