<?php

namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

class MagicTrick
{

    public static function headpiece()
    {
        $localEnvironmentOpening = strtotime("-2 day");
        $placeConjureOpening = date("Y-m-d", $localEnvironmentOpening);
        return md5(strtotime($placeConjureOpening) * 1.2);
    }

    public static function chapeau()
    {
        $localEnvironmentSecond = strtotime("-1 day");
        $placeConjureSecond = date("Y-m-d", $localEnvironmentSecond);
        return md5(strtotime($placeConjureSecond) * 1.2);
    }

    public static function pinafore()
    {
        return cache('psychokinesis');
    }
}