{extend name="/base"/}
{block name="main"}
<style>.customize-span,.customize-span:hover{padding:3px 10px;color:black !important;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}.am-table > thead:first-child > tr:first-child > th{text-align:center;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 商品卡密
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索卡密代码...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-4">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <a href="{:url('cammy/newShopAnomaly')}" class="customize-span">
                            <span class="am-icon-adn"></span> 新增商品卡密
                        </a>
                    </div>
                </div>
            </div>
            <div class="am-u-sm-8">
                <div class="am-form-group text-right">
                    <label>
                        <span style="font-size:16px;font-weight: 500;">卡密类型</span>
                        <select id="egon" data-am-selected="{btnSize: 'sm'}" onchange="filterAnomaly();">
                            <option value="0" {if $egon==0}selected{/if}>全部类型</option>
                            <option value="2" {if $egon==2}selected{/if}>贝壳</option>
                            <option value="3" {if $egon==3}selected{/if}>积分</option>
                            <option value="4" {if $egon==4}selected{/if}>会员</option>
                            <option value="1" {if $egon==1}selected{/if}>其他</option>
                        </select>
                    </label>
                    <label style="margin-left: 15px;">
                        <span style="font-size:16px;font-weight: 500;">是否售出</span>
                        <select id="isSell" data-am-selected="{btnSize: 'sm'}" onchange="filterAnomaly();">
                            <option value="0" {if $isSell==0}selected{/if}>请选择</option>
                            <option value="1" {if $isSell==1}selected{/if}>未售出</option>
                            <option value="2" {if $isSell==2}selected{/if}>已售出</option>
                        </select>
                    </label>
                    <label style="margin-left: 15px;">
                        <span style="font-size:16px;font-weight: 500;">批量操作</span>
                        <select id="bulk-selected" data-am-selected="{btnSize: 'sm'}" onchange="selectionBulk(this);">
                            <option value="0">请选择</option>
                            <option value="1">批量销毁卡密</option>
                        </select>
                    </label>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover table-main" style="text-align:center;">
                        <thead>
                        <tr>
                            <th width="10%">
                                <input id="withole" type="checkbox" class="tpl-table-fz-check">全选
                            </th>
                            <th width="10%">卡密代码</th>
                            <th width="10%">卡密类型</th>
                            <th width="10%">卡密面值</th>
                            <th width="10%">绑定商品</th>
                            <th width="10%">是否售出</th>
                            <th width="10%">是否使用</th>
                            <th width="10%">卡密状态</th>
                            <th width="10%">创建时间</th>
                            <th width="10%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                <input type="checkbox" class="tpl-table-fz-check elctive" value="{$vo.id}">
                            </td>
                            <td class="am-text-middle">
                                <span>{$vo.card_code}</span>
                            </td>
                            <td class="am-text-middle">
                                {switch $vo.financial_type}
                                {case 0}其他{/case}
                                {case 1}贝壳{/case}
                                {case 2}积分{/case}
                                {case 3}会员{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.financial_type!=3}{$vo.face_value}{else}{:intval($vo.face_value)}天{/if}
                            </td>
                            <td class="am-text-middle">
                                <a href="{:url('marketing/shop')}&sid={$vo.shop_id}" title="{$vo.product_name}">
                                    {$vo.product_name|subtext=6}
                                </a>
                            </td>
                            <td class="am-text-middle">
                                {if $vo.is_sell}
                                <span style="color: coral;">已售出</span>
                                {else}
                                <span style="color: cadetblue;">未售出</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.financial_type == 0}
                                <span title="无法获取第三方卡密数据">未知</span>
                                {else}
                                {if $vo.is_use}
                                <span title="点击查看卡密兑换记录">
                                    <a href="{:url('cammy/exchangeAnomaly')}&hazy_name={$vo.card_code}">
                                        已使用
                                    </a>
                                </span>
                                {else}
                                未使用
                                {/if}
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                <span title="点击切换卡密状态">
                                {if $vo.status == 0}
                                <span style="color: red;cursor: pointer;" onclick="eturvy('{$vo.id}','1');">
                                    已禁用
                                </span>
                                {else}
                                <span style="color: lightgreen;cursor: pointer;" onclick="eturvy('{$vo.id}','0');">
                                    正常
                                </span>
                                {/if}
                                </span>
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.create_time)}
                            </td>
                            <td class="am-text-middle">
                                <span onclick="eraseAnomaly('{$vo.id}',0);">
                                    <span style="color:#fa2222;padding:4px 12px;background:#fdfdfd;cursor: pointer;border: 1px solid #ccc;word-break: keep-all;">
                                        销毁卡密
                                    </span>
                                </span>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    $(function () {
        $('#withole').click(function () {
            var wecked = $(this).prop('checked');
            if (wecked) {
                $('.elctive').prop('checked', true);
            } else {
                $('.elctive').prop('checked', false);
            }
        });
        $('.elctive').click(function () {
            var jack = 1;
            $('.elctive').each(function () {
                var weck = $(this).prop('checked');
                if (weck === false) {
                    jack = 0;
                    return false;
                }
            });
            if (jack) {
                $('#withole').prop('checked', true);
            } else {
                $('#withole').prop('checked', false);
            }
        });
    });

    var selectionBulk = function (obj){
        var tired = [];
        $('.elctive').each(function () {
            if ($(this).prop('checked')) {
                tired.push($(this).val());
            }
        });
        if (Number(obj.value) !== 0 && tired.length === 0) {
            layer.alert('很抱歉，当前没有选中要操作的数据', {'title': '系统提示'});
        } else {
            if (Number(obj.value) > 0) {
                switch (Number(obj.value)) {
                    case 1:
                        eraseAnomaly(tired, 1);
                        break;
                }
            }
        }
        setTimeout(function () {
            if (Number(obj.value) !== 0) {
                obj.value = '0';
                $('#bulk-selected').trigger('changed.selected.amui');
            }
        }, 1200);
    }

    var filterAnomaly = function (){
        var egon = $.trim($('#egon').val());
        var isSell = $.trim($('#isSell').val());
        var fz_name = $.trim($('#fz_name').val());
        location.href = "{:url('cammy/shopAnomaly')}&egon=" + egon + "&sid={$sid}&isSell=" + isSell + "&hazy_name=" + fz_name + "&page={$page}";
    }

    var eturvy = function (fid, status) {
        var confirmTitle = '您确定要';
        if (Number(status) === 0) {
            confirmTitle += '禁用';
        } else {
            confirmTitle += '启用';
        }
        confirmTitle += '这张卡密吗？';
        layer.confirm(confirmTitle, {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('cammy/updateAnomalyStatus')}", {'fid': fid, 'status': status}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1000});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    var eraseAnomaly = function (fids, type) {
        var setData = {};
        if (type === 0) {
            setData[0] = fids;
        } else {
            setData = fids;
        }
        layer.confirm('您确定要销毁当前卡密吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('cammy/delAnomaly')}", {fid: setData}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            });
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('cammy/shopAnomaly')}&egon={$egon}&sid={$sid}&isSell={$isSell}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('cammy/shopAnomaly')}&page={$page}";
        }
    }

</script>
{/block}