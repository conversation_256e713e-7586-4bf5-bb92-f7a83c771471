{extend name="/base"/}
{block name="main"}
<link rel="stylesheet" href="assets/css/colorful-font.css?v={:time()}">
<style>.am-table > tbody > tr > td{font-size:14px;margin-top:8px;}.am-table > tbody > tr > td:nth-child(1){text-align:right;color:#000000;padding-right:20px;}.am-table > tbody > tr > td:nth-child(2){padding-left:20px;color:#999;}a:hover{color:black!important;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-user"></span> 用户资料
        </div>
        <div class="tpl-portlet-input tpl-fz-ml right">
            <a href="{:url('user/editMaterial')}&uid={$userInfo.id}" style="font-size:12px;color:black;font-weight: bold;border: 1px solid;padding:3px 6px;">
                <span class="am-icon-edit"></span>编辑用户资料
            </a>
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-form am-form-horizontal" style="width:700px;margin:0 auto;padding:25px 3% 10px 3%;overflow:hidden;border:solid 1px #cccccc;box-shadow:0px 0px 10px 0px rgba(0, 0, 0, 1);">
                    <table class="am-table am-table-bordered">
                        <tr>
                            <td class="am-text-middle" style="width:35%;">用户头像</td>
                            <td class="am-text-middle" style="width:65%;">
                                {if $userAF !=''}
                                <div style="background:url('{$userAF.adorn_icon}') no-repeat top center / cover;height:75px;width:75px;display:flex;justify-content:center;align-items:center;border-radius:50%;">
                                    {if $userInfo.user_head_sculpture && $userInfo.user_head_sculpture !='/yl_welore/style/icon/default.png'}
                                    <img src="{$userInfo.user_head_sculpture}" style="height: 50px;width: 50px;border-radius: 50%;">
                                    {else}
                                    <img src="{:urlBridging('static/disappear/tourist.png')}" style="height: 50px;width: 50px;border-radius: 50%;">
                                    {/if}
                                </div>
                                {else}
                                {if $userInfo.user_head_sculpture && $userInfo.user_head_sculpture !='/yl_welore/style/icon/default.png'}
                                <img src="{$userInfo.user_head_sculpture}" style="height: 50px;width: 50px;border-radius: 50%;">
                                {else}
                                <img src="{:urlBridging('static/disappear/tourist.png')}" style="height: 50px;width: 50px;border-radius: 50%;">
                                {/if}
                                {/if}
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">用户昵称</td>
                            <td class="am-text-middle" style="display:flex;">
                                <div class="{$userNameStyle}">
                                    <span title="{$userInfo.user_nick_name|emoji_decode}">
                                    {$userInfo.user_nick_name|emoji_decode}
                                    </span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">用户等级</td>
                            <td class="am-text-middle" style="width:65%;">
                                <?php $tempLevelcount = 0; ?>
                                {volist name="userLevel" id="vo"}
                                {if $vo.level_hierarchy==$userInfo.level}
                                <span title="Lv.{$vo.level_hierarchy} - {$vo.level_name}" style="margin-left: 5px;">
                                <img src="{$vo.level_icon}" style="width: auto;height: 18px;margin-top: -5px;">
                                </span>
                                {else}
                                <?php $tempLevelcount++; ?>
                                {/if}
                                {/volist}
                                {if $tempLevelcount==count($userLevel)}
                                <?php $levelbuoy = count($userLevel) - 1;?>
                                <span title="Lv.{$userLevel[$levelbuoy]['level_hierarchy']} - {$userLevel[$levelbuoy]['level_name']}" style="margin-left: 5px;">
                                <img src="{$userLevel[$levelbuoy]['level_icon']}" style="width: auto;height: 18px;margin-top: -5px;">
                                </span>
                                {/if}
                                <span style="margin-left:5px;" title="当前经验值：{$userInfo.experience}，下一阶段升级所需经验值：{$nextExperience}">
                                ( {$userInfo.experience} / {$nextExperience} )
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">用户认证</td>
                            <td class="am-text-middle" style="width:65%;">
                                {if $uatInfo == 1}
                                <a href="{:url('depend/acquire')}&hazy_name={$userInfo.user_wechat_open_id}&page=1" target="_blank" title="用户已通过认证">
                                    已认证
                                </a>
                                {else}
                                未认证
                                {/if}
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">用户性别</td>
                            <td class="am-text-middle" style="width:65%;">
                                {if $userInfo.gender==0}未知{elseif $userInfo.gender==1}男{elseif $userInfo.gender==2}女{/if}
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">佩戴勋章</td>
                            <td class="am-text-middle" style="width:65%;">
                                {if $userMedal}
                                <span title="{$userMedal.merit_name} - {$userMedal.merit_annotate}">
                                    <img src="{$userMedal.merit_icon}" style="width:auto;height:22px;">
                                </span>
                                {else}
                                <span title="当前未佩戴勋章" style="color:#8da9b9;">
                                    当前未佩戴勋章
                                </span>
                                {/if}
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">手机号</td>
                            <td class="am-text-middle" style="width:65%;">
                                {if $userInfo.user_phone}{$userInfo.user_phone}{else}未绑定{/if}
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">openid</td>
                            <td class="am-text-middle" style="width:65%;">{$userInfo.user_wechat_open_id}</td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">剩余{$defaultNavigate.currency}</td>
                            <td class="am-text-middle" style="width:65%;">{$userInfo.conch}</td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">剩余{$defaultNavigate.confer}</td>
                            <td class="am-text-middle" style="width:65%;">{$userInfo.fraction}</td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">荣誉点</td>
                            <td class="am-text-middle" style="width:65%;">{$userInfo.honor_point}</td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">邀请码</td>
                            <td class="am-text-middle" style="width:65%;">{$userInfo.code}</td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">状态</td>
                            <td class="am-text-middle" style="width:65%;">
                                {if $userInfo.status==0}
                                <span style="color:red;">禁止</span>
                                {else}
                                <span style="color:green;">正常</span>
                                {/if}
                            </td>
                        </tr>
                        {if $userInfo.status==0}
                        <tr>
                            <td class="am-text-middle" style="width:35%;">封禁原因</td>
                            <td class="am-text-middle" style="width:65%;">{$userInfo.forbid_prompt}</td>
                        </tr>
                        {/if}
                        <tr>
                            <td class="am-text-middle" style="width:35%;">会员到期时间</td>
                            <td class="am-text-middle" style="width:65%;">
                                {if $userInfo.vip_end_time>time()}
                                {:date('Y-m-d',$userInfo.vip_end_time)}
                                {elseif $userInfo.vip_end_time!='0'}
                                此用户会员已过期
                                {else}
                                此用户没有开通过会员
                                {/if}
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">访问IP地址</td>
                            <td class="am-text-middle" style="width:65%;">{$userInfo.user_access_ip}</td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">注册时间</td>
                            <td class="am-text-middle" style="width:65%;">{:date('Y-m-d H:i:s',$userInfo.user_reg_time)}</td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">最后访问时间</td>
                            <td class="am-text-middle" style="width:65%;">
                                {if $userInfo.user_last_time}{:date('Y-m-d H:i:s',$userInfo.user_last_time)}{else}已经很长时间没有来过了{/if}
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle" style="width:35%;">个性签名</td>
                            <td class="am-text-middle" style="width:65%;">
                                {if $userInfo.autograph}{$userInfo.autograph|emoji_decode}{else}无{/if}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}