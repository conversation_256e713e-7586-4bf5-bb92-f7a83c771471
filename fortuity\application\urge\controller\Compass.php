<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\TmplService;
use app\common\Playful;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

#圈子管理
class Compass extends Base
{

    //广场列表
    public function nav()
    {
        $url = $this->defaultQuery();
        $hazy_name = trim(request()->get('hazy_name', ''));
        $list = Db::name('needle')
            ->where('name', 'like', "{$hazy_name}%")
            ->where('much_id', $this->much_id)
            ->order('scores', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //更改广场排序
    public function slue()
    {
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $scores = request()->post('dalue');
            $result = Db::name('needle')
                ->where('id', $syid)
                ->where('much_id', $this->much_id)
                ->update(['scores' => $scores]);
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '保存成功']);
            } else {
                return json(['code' => 0, 'msg' => '保存失败']);
            }
        }
    }

    //新增广场
    public function rulnav()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            $navData = Db::name('needle')
                ->where('name', $data['name'])
                ->where('much_id', $this->much_id)
                ->find();
            if (empty($navData) || !isset($navData)) {
                $data['much_id'] = $this->much_id;
                $result = Db::name('needle')->insert($data);
                if ($result !== false) {
                    return json(['code' => 1, 'msg' => '保存成功']);
                } else {
                    return json(['code' => 0, 'msg' => '保存失败']);
                }
            } else {
                return json(['code' => 0, 'msg' => '保存失败，广场名已存在']);
            }
        }
        return $this->fetch();
    }

    //编辑广场
    public function uplnav()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            $suplid = $data['uplid'];
            $navData = Db::name('needle')
                ->where('name', $data['name'])
                ->where('id', '<>', $suplid)
                ->where('much_id', $this->much_id)
                ->find();
            if (empty($navData) || !isset($navData)) {
                unset($data['uplid']);
                $result = Db::name('needle')
                    ->where('id', $suplid)
                    ->where('much_id', $this->much_id)
                    ->update($data);
                if ($result !== false) {
                    return json(['code' => 1, 'msg' => '保存成功']);
                } else {
                    return json(['code' => 0, 'msg' => '保存失败']);
                }
            } else {
                return json(['code' => 0, 'msg' => '保存失败，广场名已存在']);
            }
        }
        $uplid = request()->get('uplid', '');
        if ($uplid) {
            $navList = Db::name('needle')
                ->where('id', $uplid)
                ->where('much_id', $this->much_id)
                ->find();
            if ($navList) {
                $this->assign('list', $navList);
                return $this->fetch();
            } else {
                $this->redirect('compass/nav');
            }
        } else {
            $this->redirect('compass/nav');
        }
    }

    //删除广场
    public function navlint()
    {
        if (request()->isPost() && request()->isAjax()) {
            $ecid = request()->post('ecid');
            $tory = Db::name('territory')->where('needle_id', $ecid)->where('much_id', $this->much_id)->find();
            if ($tory) {
                return json(['code' => 0, 'msg' => '删除失败，当前广场下存在绑定的圈子']);
            }
            Db::startTrans();
            try {
                Db::name('needle')->where('id', $ecid)->where('much_id', $this->much_id)->delete();
                Db::name('territory_petition')->where('needle_id', $ecid)->where('much_id', $this->much_id)->delete();
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '删除成功']);
            }
        }
    }

    //圈子列表
    public function fence()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $nid = intval(request()->get('nid', 0));
        $egon = request()->get('egon', 0);
        switch ($egon) {
            case 0:
                $where['tory.is_del'] = 0;
                break;
            case 1:
                $where['tory.is_del'] = 1;
                break;
        }
        $nid !== 0 && $where['tory.needle_id'] = $nid;
        $list = Db::name('territory')
            ->alias('tory')
            ->join('needle ne', 'tory.needle_id=ne.id', 'left')
            ->where('tory.realm_name|ne.name', 'like', "%{$hazy_name}%")
            ->where($where)
            ->where('tory.much_id', $this->much_id)
            ->order('tory.scores asc,tory.id desc')
            ->field('tory.id,tory.realm_icon,tory.realm_name,tory.attention,ne.name,tory.concern,tory.release_level,tory.visit_level,tory.status,tory.scores,tory.rising_time,ne.id as neid')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $egon, 'nid' => $nid, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('egon', $egon);
        $this->assign('nid', $nid);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //更改圈子排序
    public function utsun()
    {
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $scores = request()->post('dalue');
            Db::startTrans();
            try {
                Db::name('territory')->where('id', $syid)->where('much_id', $this->much_id)->update(['scores' => $scores]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '保存成功']);
            }
        }
    }

    //新增圈子
    public function rulfence()
    {
        if (request()->isPost() && request()->isAjax()) {
            $recapData = request()->post();
            $data['realm_name'] = emoji_encode($recapData['name']);
            $fenceInfo = Db::name('territory')->where('realm_name', $data['realm_name'])->where('much_id', $this->much_id)->find();
            if (!$fenceInfo) {
                $data['realm_icon'] = $recapData['circle_img'];
                if (trim($recapData['circle_back_img']) !== '') {
                    $data['realm_back_img'] = $recapData['circle_back_img'];
                } else {
                    $data['realm_back_img'] = null;
                }
                if (trim($recapData['group_qrcode']) !== '') {
                    $data['group_qrcode'] = $recapData['group_qrcode'];
                } else {
                    $data['group_qrcode'] = null;
                }
                $data['needle_id'] = $recapData['needle_id'];
                $data['realm_synopsis'] = $recapData['intro'];
                $data['attention'] = $recapData['attention'];
                $data['atence'] = $recapData['atence'];
                $data['atcipher'] = $recapData['atcipher'];
                $data['release_level'] = $recapData['release_level'];
                $data['visit_level'] = $recapData['visit_level'];
                $data['release_count'] = intval($recapData['release_count']);
                $data['status'] = $recapData['status'];
                $data['concern'] = $recapData['concern'];
                $data['scores'] = $recapData['scores'];
                $data['rising_time'] = time();
                $data['much_id'] = $this->much_id;
                Db::startTrans();
                try {
                    Db::name('territory')->insert($data);
                    $result = true;
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
                if ($result) {
                    return json(['code' => 1, 'msg' => '保存成功']);
                }
            } else {
                return json(['code' => 0, 'msg' => '保存失败，圈子名称已存在']);
            }

        }
        $needleList = Db::name('needle')
            ->where('much_id', $this->much_id)
            ->order('scores')
            ->select();
        $this->assign('needleList', $needleList);
        return $this->fetch();
    }

    //编辑圈子
    public function uplfence()
    {
        if (request()->isPost() && request()->isAjax()) {
            $recapData = request()->post();
            $suplid = $recapData['uplid'];
            $data['realm_name'] = emoji_encode($recapData['name']);
            $data['realm_icon'] = $recapData['circle_img'];
            if (trim($recapData['circle_back_img']) !== '') {
                $data['realm_back_img'] = $recapData['circle_back_img'];
            } else {
                $data['realm_back_img'] = null;
            }
            if (trim($recapData['group_qrcode']) !== '') {
                $data['group_qrcode'] = $recapData['group_qrcode'];
            } else {
                $data['group_qrcode'] = null;
            }
            $data['needle_id'] = $recapData['needle_id'];
            $data['realm_synopsis'] = $recapData['intro'];
            $data['attention'] = $recapData['attention'];
            $data['atence'] = $recapData['atence'];
            $data['atcipher'] = $recapData['atcipher'];
            $data['release_level'] = $recapData['release_level'];
            $data['visit_level'] = $recapData['visit_level'];
            $data['release_count'] = intval($recapData['release_count']);
            $data['status'] = $recapData['status'];
            $data['concern'] = $recapData['concern'];
            $data['scores'] = $recapData['scores'];
            $toryInfo = db('territory')->where('id', '<>', $suplid)->where('realm_name', $data['realm_name'])->where('much_id', $this->much_id)->find();
            if ($toryInfo) {
                return json(['code' => 0, 'msg' => '保存失败，圈子名称已存在']);
            }
            Db::startTrans();
            try {
                Db::name('territory')->where('id', $suplid)->where('much_id', $this->much_id)->update($data);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '保存成功']);
            }
        }
        $uplid = request()->get('uplid', '');
        if ($uplid) {
            $toryList = Db::name('territory')->where('id', $uplid)->where('much_id', $this->much_id)->find();
            if ($toryList) {
                $needleList = Db::name('needle')->where('much_id', $this->much_id)->order('scores')->select();
                $this->assign('list', $toryList);
                $this->assign('needleList', $needleList);
                return $this->fetch();
            } else {
                $this->redirect('fence');
            }
        } else {
            $this->redirect('fence');
        }
    }


    //删除圈子
    public function delLogicFlock()
    {
        if (request()->isPost() && request()->isAjax()) {
            $tyid = intval(request()->post('tyid'));
            $restore = intval(request()->post('restore'));
            Db::startTrans();
            try {
                if ($restore == 0) {
                    Db::name('territory')->where('id', $tyid)->where('much_id', $this->much_id)->update(['status' => 0, 'is_del' => 1]);
                } elseif ($restore == 1) {
                    Db::name('territory')->where('id', $tyid)->where('much_id', $this->much_id)->update(['status' => 1, 'is_del' => 0]);
                } elseif ($restore == 2) {
                    Db::name('territory')->where('id', $tyid)->where('much_id', $this->much_id)->delete();
                    Db::name('paper')->where('tory_id', $tyid)->where('much_id', $this->much_id)->delete();
                }
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                if ($restore == 0) {
                    return json(['code' => 1, 'msg' => '删除成功']);
                } elseif ($restore == 1) {
                    return json(['code' => 1, 'msg' => '恢复成功']);
                } elseif ($restore == 2) {
                    return json(['code' => 1, 'msg' => '彻底删除圈子成功']);
                }
            }
        } else {
            $this->error('参数错误', 'compass/fence');
        }
    }

    //查询用户信息
    public function getOpenId()
    {
        if (request()->isGet() && request()->isAjax()) {
            $openid = request()->get('openid');
            $virtual = request()->get('virtual', 0);
            $where = [
                'tourist' => 0,
                'user_wechat_open_id' => $openid,
                'much_id' => $this->much_id
            ];
            if ($virtual == 0) {
                $where['uvirtual'] = 0;
            }
            $data = Db::name('user')
                ->where($where)
                ->field('user_nick_name,user_head_sculpture')
                ->find();
            if ($data) {
                return json(['name' => emoji_decode($data['user_nick_name']), 'userhead' => $data['user_head_sculpture']]);
            } else {
                return json(['name' => '']);
            }
        }
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    //圈子申请列表
    public function solicit()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $hazy_egon = request()->get('egon', 0);
        $where = [
            'tep.realm_name|us.user_nick_name' => ['like', "%$hazy_name%"],
            'tep.realm_status' => $hazy_egon == 0 ? null : ($hazy_egon - 1),
            'tep.much_id' => $this->much_id,
        ];
        $inquire = ['s' => $url, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name];
        if (!$hazy_name) {
            unset($where["tep.realm_name|us.user_nick_name"]);
            unset($inquire['hazy_name']);
        }
        if (!is_numeric($where["tep.realm_status"])) {
            unset($where["tep.realm_status"]);
            unset($inquire['egon']);
        }
        $list = Db::name('territory_petition')
            ->alias('tep')
            ->join('needle ne', 'tep.needle_id=ne.id')
            ->join('user us', 'tep.user_id=us.id', 'left')
            ->where($where)
            ->field('tep.id,tep.realm_name,ne.name,us.user_nick_name,us.user_wechat_open_id,tep.solicit_rate,tep.realm_status,tep.found_lasting,tep.review_lasting')
            ->order(['tep.realm_status' => 'asc', 'tep.found_lasting' => 'asc'])
            ->paginate(10, false, ['query' => $inquire]);
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //查看圈子申请详情
    public function aspsolicit()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['uplid'] = request()->post('uplid');
            $data['pical'] = request()->post('pical');
            if ($data['pical'] == 1) {
                $topn = Db::name('territory_petition')->where('id', $data['uplid'])->where('much_id', $this->much_id)->find();
                $setory = Db::name('territory')->where('realm_name', $topn['realm_name'])->where('much_id', $this->much_id)->find();
                if ($setory) {
                    Db::startTrans();
                    try {
                        Db::name('territory_petition')->where('id', $data['uplid'])->where('much_id', $this->much_id)->update(['realm_status' => 4, 'review_lasting' => time()]);
                        Db::name('user_smail')->insert(['user_id' => $topn['user_id'], 'maring' => "很抱歉，圈子{$topn['realm_name']}已存在！", 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                        Db::commit();
                        return json(['code' => 0, 'msg' => '数据重复，该圈子已经存在']);
                    } catch (\Exception $e) {
                        Db::rollback();
                        return json(['code' => 0, 'msg' => 'error ,' . $e->getMessage()]);
                    }
                }
                if ($topn['is_gnaw_qulord'] == 1) {
                    $user = Db::name('user')->where('uvirtual', 0)->where('id', $topn['user_id'])->where('much_id', $this->much_id)->find();
                    $getLear['bulord'] = "[\"{$user['user_wechat_open_id']}\"]";
                }
                $tory['realm_icon'] = $topn['realm_icon'];
                $tory['realm_name'] = $topn['realm_name'];
                $tory['needle_id'] = $topn['needle_id'];
                $tory['realm_synopsis'] = $topn['realm_synopsis'];
                $tory['attention'] = $topn['attention'];
                $tory['status'] = 0;
                $tory['concern'] = 0;
                $tory['scores'] = 0;
                $tory['rising_time'] = time();
                $tory['much_id'] = $this->much_id;
                Db::startTrans();
                try {
                    $estory = Db::name('territory')->insertGetId($tory);
                    if ($topn['is_gnaw_qulord'] == 1) {
                        $getLear['tory_id'] = $estory;
                        $getLear['much_id'] = $this->much_id;
                        Db::name('territory_learned')->insert($getLear);
                        Db::name('user_trailing')->insert(['user_id' => $user['id'], 'tory_id' => $estory, 'ling_time' => time(), 'much_id' => $this->much_id]);
                        Db::name('territory')->where('id', $estory)->where('much_id', $this->much_id)->setInc('concern', 1);
                    }
                    Db::name('territory_petition')->where('id', $data['uplid'])->where('much_id', $this->much_id)->update(['realm_status' => $data['pical'], 'review_lasting' => time()]);

                    $defaultNavigate = $this->defaultNavigate();

                    $msgContent = "恭喜您，{$defaultNavigate['landgrave']}{$topn['realm_name']}申请已经通过审核！";
                    Db::name('user_smail')->insert([
                        'user_id' => $topn['user_id'],
                        'maring' => $msgContent,
                        'clue_time' => time(),
                        'status' => 0,
                        'much_id' => $this->much_id
                    ]);
                    $tmplService = new TmplService();
                    $circleData = [
                        'much_id' => $this->much_id,
                        'at_id' => 'YL0008',
                        'user_id' => $topn['user_id'],
                        'page' => 'yl_welore/pages/user_smail/index',
                        'keyword1' => $msgContent,
                        'keyword2' => '申请成功',
                        'keyword3' => date('Y年m月d日 H:i:s', time())
                    ];
                    $tmplService->add_template($circleData);
                    //是否申请圈主
                    if ($topn['is_gnaw_qulord'] == 1) {
                        $fristName = mb_substr($defaultNavigate['landgrave'], 0, 1, 'utf-8');
                        $appointContent = "您已被系统管理员任命为<{$topn['realm_name']}>{$fristName}主！";
                        Db::name('user_smail')->insert([
                            'user_id' => $topn['user_id'],
                            'maring' => $appointContent,
                            'clue_time' => time() + 1,
                            'status' => 0,
                            'much_id' => $this->much_id
                        ]);
                        $jobData = [
                            'much_id' => $this->much_id,
                            'at_id' => 'YL0010',
                            'user_id' => $topn['user_id'],
                            'page' => 'yl_welore/pages/user_smail/index',
                            'keyword1' => $appointContent,
                            'keyword2' => '职位任命',
                        ];
                        $tmplService->add_template($jobData);
                    }
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
                return json(['code' => 1, 'msg' => '操作成功']);
            } else {
                Db::startTrans();
                try {
                    Db::name('territory_petition')->where('id', $data['uplid'])->where('much_id', $this->much_id)->update(['realm_status' => $data['pical'], 'review_lasting' => time()]);
                    $toryTion = Db::name('territory_petition')->where('id', $data['uplid'])->where('much_id', $this->much_id)->find();
                    Db::name('user_smail')->insert([
                        'user_id' => $toryTion['user_id'],
                        'maring' => "很抱歉，圈子{$toryTion['realm_name']}申请已被拒绝！",
                        'clue_time' => time(),
                        'status' => 0,
                        'much_id' => $this->much_id
                    ]);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
                return json(['code' => 1, 'msg' => '操作成功']);
            }
        } else {
            $uplid = request()->get('uplid', '');
            if ($uplid) {
                $tpList = Db::name('territory_petition')
                    ->alias('tep')
                    ->join('needle ne', 'tep.needle_id=ne.id', 'left')
                    ->join('user us', 'tep.user_id=us.id', 'left')
                    ->where('tep.id', $uplid)
                    ->where('tep.much_id', $this->much_id)
                    ->field('tep.*,ne.name,us.user_head_sculpture,us.user_nick_name,us.user_wechat_open_id,us.status ustatus')
                    ->find();
                if ($tpList) {
                    $this->assign('list', $tpList);
                    return $this->fetch();
                } else {
                    $this->redirect('solicit');
                }
            } else {
                $this->redirect('solicit');
            }
        }
    }

    // 关注审核列表
    public function savour()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $hazy_bering = request()->get('hazy_bering', '');
        $hazy_egon = request()->get('egon', 0);
        $toryInfo = Db::name('territory')->where('id', $hazy_bering)->where('much_id', $this->much_id)->find();
        if (!$toryInfo) {
            $this->redirect('compass/fence');
        }
        switch ($hazy_egon) {
            case 0:
                $where = [];
                break;
            case 1:
                $where['ti.status'] = 0;
                break;
            case 2:
                $where['ti.status'] = 1;
                break;
            case 3:
                $where['ti.status'] = 2;
                break;
        }
        $list = Db::name('territory_interest')
            ->alias('ti')
            ->join('user us', 'ti.user_id=us.id', 'left')
            ->join('territory tory', 'ti.tory_id=tory.id', 'left')
            ->where('us.user_nick_name|us.user_wechat_open_id', 'like', "%{$hazy_name}%")
            ->where('tory.id', $hazy_bering)
            ->where($where)
            ->where('ti.much_id', $this->much_id)
            ->order('status', 'asc')
            ->order('rest_time', 'desc')
            ->order('sult_time', 'asc')
            ->field('ti.*,us.user_nick_name,us.user_head_sculpture,us.user_wechat_open_id,tory.realm_name')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_bering' => $hazy_bering, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('toryInfo', $toryInfo);
        $this->assign('egon', $hazy_egon);
        $this->assign('hazy_bering', $hazy_bering);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //用户审核加入理由
    public function savour_tance()
    {
        if (request()->isGet() && request()->isAjax()) {
            $ecid = request()->get('ecid');
            $inter = Db::name('territory_interest')->where('id', $ecid)->where('much_id', $this->much_id)->find();
            return json(['info' => strip_tags(emoji_decode($inter['reason']))]);
        }
    }

    //审批审核列表
    public function arcanum()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['suid'] = request()->post('suid');
            $data['status'] = request()->post('status');
            Db::startTrans();
            try {
                $inter = Db::name('territory_interest')->where('id', $data['suid'])->where('much_id', $this->much_id)->find();
                if ($inter['status'] == 0 && ($data['status'] == 1 || $data['status'] == 2)) {
                    Db::name('territory_interest')->where('id', $data['suid'])->where('much_id', $this->much_id)->update(['rest_time' => time(), 'status' => $data['status']]);
                    $userTrailing = Db::name('user_trailing')->where('user_id', $inter['user_id'])->where('tory_id', $inter['tory_id'])->where('much_id', $this->much_id)->find();
                    $tory = Db::name('territory')->where('id', $inter['tory_id'])->where('much_id', $this->much_id)->find();
                    if (!$userTrailing) {
                        if ($data['status'] == 1) {
                            Db::name('user_trailing')->insert(['user_id' => $inter['user_id'], 'tory_id' => $inter['tory_id'], 'ling_time' => time(), 'much_id' => $this->much_id]);
                            Db::name('territory')->where('id', $inter['tory_id'])->where('much_id', $this->much_id)->setInc('concern', 1);
                        }
                    } else {
                        if ($data['status'] == 2) {
                            Db::name('user_trailing')->where('user_id', $inter['user_id'])->where('tory_id', $inter['tory_id'])->where('much_id', $this->much_id)->delete();
                            Db::name('territory')->where('id', $inter['tory_id'])->where('much_id', $this->much_id)->setDec('concern', 1);
                        }
                    }
                    $defaultNavigate = $this->defaultNavigate();
                    Db::name('user_smail')->insert([
                        'user_id' => $inter['user_id'],
                        'maring' => $data['status'] == 1 ? "您申请关注的{$defaultNavigate['landgrave']}<{$tory['realm_name']}>已通过审核！" : "您申请关注的{$defaultNavigate['landgrave']}<{$tory['realm_name']}>已被拒绝！",
                        'clue_time' => time(),
                        'status' => 0,
                        'much_id' => $this->much_id
                    ]);
                    $merger = [
                        'much_id' => $this->much_id,
                        'user_id' => $inter['user_id'],
                        'at_id' => 'YL0005',
                        'page' => 'yl_welore/pages/packageA/circle_info/index?id=' . $tory['id'],
                        'keyword1' => "申请加入: {$tory['realm_name']}",
                        'keyword2' => $data['status'] == 1 ? '申请通过' : '申请拒绝',
                        'keyword3' => date('Y年m月d日 H:i:s', time())
                    ];
                    $templet = new TmplService();
                    $templet->add_template($merger);
                    $result = true;
                    Db::commit();
                } else {
                    $result = false;
                    Db::rollback();
                }
            } catch (\Exception $e) {
                $result = false;
                Db::rollback();
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '操作成功']);
            } else {
                return json(['code' => 0, 'msg' => '操作失败']);
            }
        }
    }


    //关注审核数据删除
    public function savour_link()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['suid'] = request()->post('suid');
            Db::startTrans();
            try {
                Db::name('territory_interest')->where('id', $data['suid'])->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                $result = false;
                Db::rollback();
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '删除成功']);
            } else {
                return json(['code' => 0, 'msg' => '删除失败']);
            }
        }
    }

    //私密圈子已经关注用户
    public function caveat()
    {
        if (request()->isPost() && request()->isAjax()) {
            $type = request()->post('type');
            if ($type == 0) {
                $tyid = request()->post('tyid');
                $toryInfo = Db::name('territory')->where('id', $tyid)->where('much_id', $this->much_id)->find();
                $openid = request()->post('openid');
                $userInfo = Db::name('user')->where('user_wechat_open_id', $openid)->where('tourist', 0)->where('much_id', $this->much_id)->find();
                if (!$toryInfo || !$userInfo) {
                    return json(['code' => 0, 'msg' => '参数错误']);
                } else {
                    $utInfo = Db::name('user_trailing')->where('user_id', $userInfo['id'])->where('tory_id', $tyid)->where('much_id', $this->much_id)->find();
                    if ($utInfo) {
                        return json(['code' => 0, 'msg' => '该用户已经关注了此圈子，请勿重复添加！']);
                    }
                }
            }
            Db::startTrans();
            try {
                switch (intval($type)) {
                    case 0:
                        Db::name('user_trailing')->insert(['user_id' => $userInfo['id'], 'tory_id' => $tyid, 'ling_time' => time(), 'much_id' => $this->much_id]);
                        break;
                    case 1:
                        $hid = request()->post('hid');
                        Db::name('user_trailing')->where('id', $hid)->where('much_id', $this->much_id)->delete();
                        break;
                }
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '操作成功']);
            }
        } else {
            $url = $this->defaultQuery();
            $tyid = request()->get('tyid');
            $toryInfo = Db::name('territory')->where('id', $tyid)->where('much_id', $this->much_id)->find();
            if (!$toryInfo) {
                $this->redirect('compass/fence');
            }
            $hazy_name = trim(request()->get('hazy_name', ''));
            $hazy_name != '' && $hazy_name = emoji_encode($hazy_name);
            $utList = Db::name('user_trailing')
                ->alias('ut')
                ->join('user us', 'ut.user_id = us.id')
                ->where('us.user_nick_name', 'like', "%{$hazy_name}%")
                ->where('us.tourist', 0)
                ->where('us.uvirtual', 0)
                ->where('ut.tory_id', $tyid)
                ->where('ut.much_id', $this->much_id)
                ->field('ut.*,us.user_head_sculpture,us.user_nick_name,us.user_wechat_open_id')
                ->order('ut.ling_time', 'desc')
                ->paginate(10, false, ['query' => ['s' => $url, 'tyid' => $tyid, 'hazy_name' => $hazy_name]]);
            $this->assign('list', $utList);
            $this->assign('toryInfo', $toryInfo);
            $this->assign('hazy_name', emoji_decode($hazy_name));
            $this->assign('tyid', $tyid);
            $page = request()->get('page', 1);
            $pageCount = $utList->count();
            if ($pageCount < 1 && $page != 1) {
                $this->emptyDataRedirect(['hazy_name' => $hazy_name]);
            }
            $this->assign('page', $page);
            return $this->fetch();
        }
    }

    //圈子管理列表
    public function dominator()
    {
        //圈子编号
        $tyid = request()->get('tyid');
        //圈子是否存在
        $toryInfo = Db::name('territory')->where('id', $tyid)->where('much_id', $this->much_id)->find();
        //圈子不存在
        if (!$toryInfo) {
            $this->error('参数错误', 'compass/fence');
        }
        //圈子管理信息
        $tylInfo = Db::name('territory_learned')->where('tory_id', $tyid)->where('much_id', $this->much_id)->find();
        //圈子管理不存在
        if (!$tylInfo) {
            Db::startTrans();
            try {
                //新增圈子管理空白信息
                Db::name('territory_learned')->insert(['tory_id' => $tyid, 'much_id' => $this->much_id]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
        }
        //圈主
        $tylBulord = json_decode($tylInfo['bulord'], true);
        $tylBulord = array_merge($tylBulord);
        if (count($tylBulord) > 0) {
            foreach ($tylBulord as $key => $value) {
                $tylBulordInfo[] = Db::name('user')->where('user_wechat_open_id', $value)->where('much_id', $this->much_id)->find();
            }
        }
        //管理员
        $tylSulord = json_decode($tylInfo['sulord'], true);
        $tylSulord = array_merge($tylSulord);
        if (count($tylSulord) > 0) {
            foreach ($tylSulord as $key => $value) {
                $tylSulordInfo[] = Db::name('user')->where('user_wechat_open_id', $value)->where('much_id', $this->much_id)->find();
            }
        }
        //圈主申请
        $tylSnviteBulord = json_decode($tylInfo['snvite_bulord'], true);
        $tylSnviteBulord = array_merge($tylSnviteBulord);
        if (count($tylSnviteBulord) > 0) {
            foreach ($tylSnviteBulord as $key => $value) {
                $tylSnviteBulord[$key]['upshot'] = emoji_decode($tylSnviteBulord[$key]['upshot']);
                $tylSnviteBulordInfo[] = Db::name('user')->where('user_wechat_open_id', $value['openid'])->where('much_id', $this->much_id)->find();
            }
        }
        //管理员申请
        $tylEnviteSulord = json_decode($tylInfo['envite_sulord'], true);
        $tylEnviteSulord = array_merge($tylEnviteSulord);
        if (count($tylEnviteSulord) > 0) {
            foreach ($tylEnviteSulord as $key => $value) {
                $tylEnviteSulord[$key]['upshot'] = emoji_decode($tylEnviteSulord[$key]['upshot']);
                $tylEnviteSulordInfo[] = Db::name('user')->where('user_wechat_open_id', $value['openid'])->where('much_id', $this->much_id)->find();
            }
        }
        //圈子信息
        $this->assign('toryInfo', $toryInfo);
        //圈主信息
        $this->assign('tylBulord', $tylBulord);
        //圈主信息详情
        $this->assign('tylBulordInfo', $tylBulordInfo);
        //管理员信息
        $this->assign('tylSulord', $tylSulord);
        //管理员信息详情
        $this->assign('tylSulordInfo', $tylSulordInfo);
        //圈主申请信息
        $this->assign('tylSnviteBulord', $tylSnviteBulord);
        //圈主申请详情
        $this->assign('tylSnviteBulordInfo', $tylSnviteBulordInfo);
        //管理员申请信息
        $this->assign('tylEnviteSulord', $tylEnviteSulord);
        //管理员申请详情
        $this->assign('tylEnviteSulordInfo', $tylEnviteSulordInfo);
        return $this->fetch();
    }

    //圈子管理团队任命或任免
    public function nominate()
    {
        if (request()->isPost() && request()->isAjax()) {
            //获取数据
            $data = request()->post();
            //查询圈子数据
            $toryInfo = Db::name('territory')->where('id', $data['tyid'])->where('much_id', $this->much_id)->field('realm_name')->find();
            //查询用户数据
            $userInfo = Db::name('user')->where('user_wechat_open_id', $data['openid'])->where('much_id', $this->much_id)->find();
            if (!$toryInfo || !$userInfo) {
                $this->error('参数错误', 'compass/fence');
            }

            $data['type'] = intval($data['type']);
            $data['job'] = intval($data['job']);

            //任命圈主
            $data['type'] === 0 && $data['job'] === 0 && $contend = 0;
            //任命管理员
            $data['type'] === 0 && $data['job'] === 1 && $contend = 1;
            //任免圈主
            $data['type'] === 1 && $data['job'] === 0 && $contend = 2;
            //任免管理员
            $data['type'] === 1 && $data['job'] === 1 && $contend = 3;
            //圈子管理信息
            $tylInfo = Db::name('territory_learned')->where('tory_id', $data['tyid'])->where('much_id', $this->much_id)->order('id')->find();
            //圈主列表
            $tylInfo['bulord'] = json_decode($tylInfo['bulord'], true);
            //判断圈主人数是否小于三个
            if (count($tylInfo['bulord']) > 2 && $contend == 0) {
                return json(['code' => 0, 'msg' => '很抱歉，最多只能任命三位用户为圈主！']);
            }
            //管理员列表
            $tylInfo['sulord'] = json_decode($tylInfo['sulord'], true);
            if (count($tylInfo['sulord']) > 9 && $contend == 1) {
                return json(['code' => 0, 'msg' => '很抱歉，最多只能任命十位用户为管理员！']);
            }
            //如果同意任命圈主或管理员则判断是否有职位
            if ($contend == 0 || $contend == 1) {
                //是否已存在
                if (in_array($data['openid'], $tylInfo['bulord']) || in_array($data['openid'], $tylInfo['sulord'])) {
                    return json(['code' => 0, 'msg' => '很抱歉，此用户已有任命的职位，请取消原有职位后再重新任命！']);
                }
            }
            Db::startTrans();
            try {
                //如果同意任命圈主或管理员则自动关注此圈子
                if ($contend == 0 || $contend == 1) {
                    $utrInfo = Db::name('user_trailing')->where('user_id', $userInfo['id'])->where('tory_id', $data['tyid'])->where('much_id', $this->much_id)->find();
                    if (!$utrInfo) {
                        Db::name('user_trailing')->insert(['user_id' => $userInfo['id'], 'tory_id' => $data['tyid'], 'ling_time' => time(), 'much_id' => $this->much_id]);
                    }
                }
                //任命圈主
                if ($contend == 0 || $contend == 2) {
                    //获取自定义设置
                    $defaultNavigate = $this->defaultNavigate();
                    //截取圈子的自定义名称
                    $fristName = mb_substr($defaultNavigate['landgrave'], 0, 1, 'utf-8');
                }
                //取消任命圈主
                if ($contend == 2) {
                    //判断圈主列表数据是否足够
                    if (count($tylInfo['bulord']) > 0) {
                        foreach ($tylInfo['bulord'] as $key => $value) {
                            if ($value == $data['openid']) {
                                //移除圈主申请列表
                                unset($tylInfo['bulord'][$key]);
                            }
                        }
                        //没有用户申请圈主则置为空
                        if (count($tylInfo['bulord']) == 0) {
                            $tylInfo['bulord'] = null;
                        } else {
                            $tylInfo['bulord'] = json_encode($tylInfo['bulord'], true);
                        }
                    }
                }
                //取消任命管理员
                if ($contend == 3) {
                    //判断管理员申请列表数据是否足够
                    if (count($tylInfo['sulord']) > 0) {
                        foreach ($tylInfo['sulord'] as $key => $value) {
                            if ($value == $data['openid']) {
                                //移除管理员申请列表
                                unset($tylInfo['sulord'][$key]);
                            }
                        }
                        //没有用户申请管理员则置为空
                        if (count($tylInfo['sulord']) == 0) {
                            $tylInfo['sulord'] = null;
                        } else {
                            $tylInfo['sulord'] = json_encode($tylInfo['sulord'], true);
                        }
                    }
                }
                switch ($contend) {
                    case 0:
                        //新增一位圈主
                        $tylInfo['bulord'][] = $data['openid'];
                        //圈主去重
                        $tylInfo['bulord'] = array_unique($tylInfo['bulord']);
                        //圈主数据转换为可视数组
                        $tylInfo['bulord'] = json_encode($tylInfo['bulord'], true);
                        //通知给用户已经被任命为圈主
                        $maringInfo = "您已被系统管理员任命为<{$toryInfo['realm_name']}>{$fristName}主！";
                        break;
                    case 1:
                        //新增一位管理员
                        $tylInfo['sulord'][] = $data['openid'];
                        //管理员去重
                        $tylInfo['sulord'] = array_unique($tylInfo['sulord']);
                        //管理员数据转换为可视数组
                        $tylInfo['sulord'] = json_encode($tylInfo['sulord'], true);
                        //通知给用户已经被任命为管理员
                        $maringInfo = "您已被系统管理员任命为<{$toryInfo['realm_name']}>管理员！";
                        break;
                    case 2:
                        //通知给用户已经被任免为圈主
                        $maringInfo = "您已被系统管理员取消<{$toryInfo['realm_name']}>{$fristName}主职位，取消原因：{$data['reason']}！";
                        break;
                    case 3:
                        //通知给用户已经被任免为管理员
                        $maringInfo = "您已被系统管理员取消<{$toryInfo['realm_name']}>管理员职位，取消原因：{$data['reason']}！";
                        break;
                }

                //更新任命或拒绝同意数据
                Db::name('territory_learned')->where('id', $tylInfo['id'])->where('much_id', $this->much_id)->update($tylInfo);
                //通知给用户消息信息
                $feedback = Db::name('user_smail')->insert(['user_id' => $userInfo['id'], 'maring' => $maringInfo, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                if ($feedback) {
                    //模板消息 用户编号
                    $merger['user_id'] = $userInfo['id'];
                    //模板消息 多商户标识
                    $merger['much_id'] = $this->much_id;
                    //模板消息 模板编号
                    $merger['at_id'] = 'YL0010';
                    //模板消息 跳转路径
                    $merger['page'] = 'yl_welore/pages/user_smail/index';
                    //模板消息 提醒信息
                    $merger['keyword1'] = $maringInfo;
                    //模板消息 提醒类型
                    ($contend == 0 || $contend == 1) && $merger['keyword2'] = '职位任命';
                    //模板消息 提醒类型
                    ($contend == 2 || $contend == 3) && $merger['keyword2'] = '职位任免';
                    $templet = new TmplService();
                    $templet->add_template($merger);
                }
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '操作成功']);
            }
        } else {
            $this->error('参数错误', 'compass/fence');
        }
    }

    //圈子管理团队审批处理
    public function turnover()
    {
        if (request()->isPost() && request()->isAjax()) {
            //获取数据
            $data = request()->post();
            //查询圈子数据
            $toryInfo = Db::name('territory')->where('id', $data['tyid'])->where('much_id', $this->much_id)->field('realm_name')->find();
            //查询用户数据
            $userInfo = Db::name('user')->where('user_wechat_open_id', $data['openid'])->where('much_id', $this->much_id)->find();
            if (!$toryInfo || !$userInfo) {
                $this->error('参数错误', 'compass/fence');
            }
            //同意任命圈主
            $data['genre'] == 0 && $data['type'] == 0 && $contend = 0;
            //同意任命管理员
            $data['genre'] == 0 && $data['type'] == 1 && $contend = 1;
            //拒绝任命圈主
            $data['genre'] == 1 && $data['type'] == 0 && $contend = 2;
            //拒绝任命管理员
            $data['genre'] == 1 && $data['type'] == 1 && $contend = 3;
            //圈子管理信息
            $tylInfo = Db::name('territory_learned')->where('tory_id', $data['tyid'])->where('much_id', $this->much_id)->order('id')->find();
            //圈主列表
            $tylInfo['bulord'] = json_decode($tylInfo['bulord'], true);
            //判断圈主人数是否小于三个
            if (count($tylInfo['bulord']) > 2 && $contend == 0) {
                return json(['code' => 0, 'msg' => '很抱歉，最多只能任命3位用户为圈主！']);
            }
            //管理员列表
            $tylInfo['sulord'] = json_decode($tylInfo['sulord'], true);
            if (count($tylInfo['sulord']) > 9 && $contend == 1) {
                return json(['code' => 0, 'msg' => '很抱歉，最多只能任命10位用户为管理员！']);
            }
            //如果同意任命圈主或管理员则判断是否有职位
            if ($contend == 0 || $contend == 1) {
                //是否已存在
                if (in_array($data['openid'], $tylInfo['bulord']) || in_array($data['openid'], $tylInfo['sulord'])) {
                    return json(['code' => 0, 'msg' => '很抱歉，此用户已有任命的职位，请取消原有职位后再重新任命！']);
                }
            }
            Db::startTrans();
            try {
                //如果同意任命圈主或管理员则自动关注此圈子
                if ($contend == 0 || $contend == 1) {
                    $utrInfo = Db::name('user_trailing')->where('user_id', $userInfo['id'])->where('tory_id', $data['tyid'])->where('much_id', $this->much_id)->find();
                    if (!$utrInfo) {
                        Db::name('user_trailing')->insert(['user_id' => $userInfo['id'], 'tory_id' => $data['tyid'], 'ling_time' => time(), 'much_id' => $this->much_id]);
                    }
                }
                //通过或拒绝任命圈主
                if ($contend == 0 || $contend == 2) {
                    //圈主申请列表
                    $tylInfo['snvite_bulord'] = json_decode($tylInfo['snvite_bulord'], true);
                    //判断圈主申请列表数据是否足够
                    if (count($tylInfo['snvite_bulord']) > 0) {
                        foreach ($tylInfo['snvite_bulord'] as $key => $value) {
                            if ($value['openid'] == $data['openid']) {
                                //移除圈主申请列表
                                unset($tylInfo['snvite_bulord'][$key]);
                            }
                        }
                        //没有用户申请圈主则置为空
                        if (count($tylInfo['snvite_bulord']) == 0) {
                            $tylInfo['snvite_bulord'] = null;
                        } else {
                            $tylInfo['snvite_bulord'] = array_merge($tylInfo['snvite_bulord']);
                            $tylInfo['snvite_bulord'] = json_encode($tylInfo['snvite_bulord'], true);
                        }
                    }
                    //获取自定义设置
                    $defaultNavigate = $this->defaultNavigate();
                    //截取圈子的自定义名称
                    $fristName = mb_substr($defaultNavigate['landgrave'], 0, 1, 'utf-8');
                }

                //通过或拒绝任命管理员
                if ($contend == 1 || $contend == 3) {
                    //管理员申请列表
                    $tylInfo['envite_sulord'] = json_decode($tylInfo['envite_sulord'], true);
                    //判断管理员申请列表数据是否足够
                    if (count($tylInfo['envite_sulord']) > 0) {
                        foreach ($tylInfo['envite_sulord'] as $key => $value) {
                            if ($value['openid'] == $data['openid']) {
                                //移除管理员申请列表
                                unset($tylInfo['envite_sulord'][$key]);
                            }
                        }
                        //没有用户申请管理员则置为空
                        if (count($tylInfo['envite_sulord']) == 0) {
                            $tylInfo['envite_sulord'] = null;
                        } else {
                            //重置数组下标
                            $tylInfo['envite_sulord'] = array_merge($tylInfo['envite_sulord']);
                            //转为json对象
                            $tylInfo['envite_sulord'] = json_encode($tylInfo['envite_sulord'], true);
                        }
                    }
                }
                switch ($contend) {
                    case 0:
                        //新增一位圈主
                        $tylInfo['bulord'][] = $data['openid'];
                        //圈主去重
                        $tylInfo['bulord'] = array_unique($tylInfo['bulord']);
                        //圈主数据转换为可视数组
                        $tylInfo['bulord'] = json_encode($tylInfo['bulord'], true);
                        //通知给用户圈主申请成功状态
                        $maringInfo = "恭喜您，您申请的 <{$toryInfo['realm_name']}>{$fristName}主职位已通过审核，您已被系统管理员任命为<{$toryInfo['realm_name']}>{$fristName}主！";
                        break;
                    case 1:
                        //新增一位管理员
                        $tylInfo['sulord'][] = $data['openid'];
                        //管理员去重
                        $tylInfo['sulord'] = array_unique($tylInfo['sulord']);
                        //管理员数据转换为可视数组
                        $tylInfo['sulord'] = json_encode($tylInfo['sulord'], true);
                        //通知给用户管理员申请成功状态
                        $maringInfo = "恭喜您，您申请的 <{$toryInfo['realm_name']}>管理员职位已通过审核，您已被系统管理员任命为<{$toryInfo['realm_name']}>管理员！";
                        break;
                    case 2:
                        //通知给用户圈主申请失败状态
                        $maringInfo = "很遗憾，您申请的 <{$toryInfo['realm_name']}>{$fristName}主 已被拒绝，拒绝原因：{$data['reason']}！";
                        break;
                    case 3:
                        //通知给用户管理员申请失败状态
                        $maringInfo = "很遗憾，您申请的 <{$toryInfo['realm_name']}>管理员 已被拒绝，拒绝原因：{$data['reason']}！";
                        break;
                }
                //更新任命或拒绝同意数据
                Db::name('territory_learned')->where('id', $tylInfo['id'])->where('much_id', $this->much_id)->update($tylInfo);
                //通知给用户消息信息
                $feedback = Db::name('user_smail')->insert(['user_id' => $userInfo['id'], 'maring' => $maringInfo, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                if ($feedback) {
                    //模板消息 用户编号
                    $merger['user_id'] = $userInfo['id'];
                    //模板消息 多商户标识
                    $merger['much_id'] = $this->much_id;
                    //模板消息 模板编号
                    $merger['at_id'] = 'YL0010';
                    //模板消息 跳转路径
                    $merger['page'] = 'yl_welore/pages/user_smail/index';
                    //模板消息 申请信息
                    ($contend == 0 || $contend == 2) && $merger['keyword1'] = "您申请的 <{$toryInfo['realm_name']}> {$fristName}主职位";
                    //模板消息 申请信息
                    ($contend == 1 || $contend == 3) && $merger['keyword1'] = "您申请的 <{$toryInfo['realm_name']}> 管理员职位";
                    //模板消息 申请状态
                    ($contend == 0 || $contend == 1) && $merger['keyword2'] = '申请通过';
                    //模板消息 申请状态
                    ($contend == 2 || $contend == 3) && $merger['keyword2'] = '申请拒绝';
                    //模板消息 通知时间
                    $merger['keyword3'] = date('Y年m月d日 H:i:s', time());
                    $templet = new TmplService();
                    $templet->add_template($merger);
                }
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '操作成功']);
            }
        } else {
            $this->error('参数错误', 'compass/fence');
        }
    }

    //圈子审核删除
    public function citlint()
    {
        if (request()->isPost() && request()->isAjax()) {
            $ecid = request()->post('ecid');
            Db::startTrans();
            try {
                Db::name('territory_petition')->where('id', $ecid)->where('much_id', $this->much_id)->delete();
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                $result = false;
                Db::rollback();
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '删除成功']);
            } else {
                return json(['code' => 0, 'msg' => '删除失败']);
            }
        }
    }

    //置顶帖子列表
    public function topping()
    {
        if (request()->isPost() && request()->isAjax()) {
            $prid = request()->post('prid');
            Db::startTrans();
            try {
                Db::name('paper')->where('id', $prid)->where('much_id', $this->much_id)->update(['topping_time' => 0]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '取消置顶成功']);
            }
        }
        $url = $this->defaultQuery();
        $toryId = request()->get('tory_id', '');
        if ($toryId) {
            $getTory = Db::name('territory')->where('id', $toryId)->where('much_id', $this->much_id)->find();
            if ($getTory) {
                $this->assign('realmName', $getTory['realm_name']);
                $hazy_name = request()->get('hazy_name', '');
                $list = Db::name('paper')
                    ->alias('per')
                    ->join('user us', 'per.user_id=us.id', 'left')
                    ->where('per.study_title|per.study_content', 'like', "%{$hazy_name}%")
                    ->where('per.tory_id', $getTory['id'])
                    ->where('per.topping_time', '<>', 0)
                    ->where('per.whether_delete', 0)
                    ->where('per.much_id', $this->much_id)
                    ->order('per.topping_time', 'asc')
                    ->field('per.*,us.user_nick_name,us.user_wechat_open_id')
                    ->paginate(10, false, ['query' => ['s' => $url, 'tory_id' => $toryId, 'hazy_name' => $hazy_name]]);
                $this->assign('list', $list);
                $this->assign('hazy_name', $hazy_name);
                $this->assign('tory_id', $toryId);
                $page = request()->get('page', 1);
                $this->assign('page', $page);
                return $this->fetch();
            } else {
                $this->redirect('compass/fence');
            }
        } else {
            $this->redirect('compass/fence');
        }
    }

    //添加圈子帖子置顶
    public function inctoping()
    {
        if (request()->isPost() && request()->isAjax()) {
            //圈子编号
            $trid = request()->post('trid');
            //帖子编号
            $prid = request()->post('prid');
            Db::startTrans();
            try {
                $paper = Db::name('paper')->where('id', $prid)->where('tory_id', $trid)->where('whether_type', 0)->where('whether_delete', 0)->where('much_id', $this->much_id)->find();
                if ($paper) {
                    if ($paper['topping_time'] != 0) {
                        Db::rollback();
                        return json(['code' => 0, 'msg' => '该帖子已置顶，请勿重复置顶']);
                    } else {
                        Db::name('paper')->where('tory_id', $trid)->where('id', $prid)->where('much_id', $this->much_id)->update(['topping_time' => time()]);
                        $result = true;
                        Db::commit();
                    }
                } else {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => '该帖子ID不在当前圈子类目下或已被删除']);
                }
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result == true) {
                return json(['code' => 1, 'msg' => '置顶成功']);
            }

        } else {
            $this->error('参数错误', 'compass/fence');
        }
    }


    //精华帖子列表
    public function essence()
    {
        if (request()->isPost() && request()->isAjax()) {
            $prid = request()->post('prid');
            Db::startTrans();
            try {
                Db::name('paper')->where('id', $prid)->where('much_id', $this->much_id)->update(['essence_time' => 0]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '设置成功']);
            }
        }
        $url = $this->defaultQuery();
        $toryId = request()->get('tory_id', '');
        if ($toryId) {
            $getTory = Db::name('territory')->where('id', $toryId)->where('much_id', $this->much_id)->find();
            if ($getTory) {
                $this->assign('realmName', $getTory['realm_name']);
                $hazy_name = request()->get('hazy_name', '');
                $list = Db::name('paper')
                    ->alias('per')
                    ->join('user us', 'per.user_id=us.id', 'left')
                    ->where('per.study_title|per.study_content', 'like', "%{$hazy_name}%")
                    ->where('per.tory_id', $getTory['id'])
                    ->where('per.essence_time', '<>', 0)
                    ->where('per.whether_delete', 0)
                    ->where('per.much_id', $this->much_id)
                    ->order('per.essence_time', 'asc')
                    ->field('per.*,us.user_nick_name,us.user_wechat_open_id')
                    ->paginate(10, false, ['query' => ['s' => $url, 'tory_id' => $toryId, 'hazy_name' => $hazy_name]]);
                $this->assign('list', $list);
                $this->assign('hazy_name', $hazy_name);
                $this->assign('tory_id', $toryId);
                $page = request()->get('page', 1);
                $this->assign('page', $page);
                return $this->fetch();
            } else {
                $this->redirect('compass/fence');
            }
        } else {
            $this->redirect('compass/fence');
        }
    }

    //添加圈子帖子精华帖子
    public function ingence()
    {
        if (request()->isPost() && request()->isAjax()) {
            //圈子编号
            $trid = request()->post('trid');
            //帖子编号
            $prid = request()->post('prid');
            Db::startTrans();
            try {
                $paper = Db::name('paper')->where('id', $prid)->where('tory_id', $trid)->where('whether_type', 0)->where('whether_delete', 0)->where('much_id', $this->much_id)->find();
                if ($paper) {
                    if ($paper['essence_time'] != 0) {
                        Db::rollback();
                        return json(['code' => 0, 'msg' => '该帖子已设置为精华帖子，请勿重复设置']);
                    } else {
                        Db::name('paper')->where('tory_id', $trid)->where('id', $prid)->where('much_id', $this->much_id)->update(['essence_time' => time()]);
                        $result = true;
                        Db::commit();
                    }
                } else {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => '该帖子ID不在当前圈子类目下或已被删除']);
                }
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result == true) {
                return json(['code' => 1, 'msg' => '设置成功']);
            }

        } else {
            $this->error('参数错误', 'compass/fence');
        }
    }

    //话题列表
    public function theme()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $egon = request()->get('egon', 0);
        $page = request()->get('page', 1);
        $list = Db::name('gambit')
            ->where('gambit_name', 'like', "%{$hazy_name}%")
            ->where('is_del', $egon)
            ->where('much_id', $this->much_id)
            ->order('scores', 'asc')
            ->order('id', 'desc')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $egon, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('egon', $egon);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //话题排序
    public function themeSort()
    {
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $scores = request()->post('dalue');
            Db::startTrans();
            try {
                Db::name('gambit')->where('id', $syid)->where('much_id', $this->much_id)->update(['scores' => $scores]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '保存成功']);
            }
        }
    }

    //新增话题
    public function pressTheme()
    {
        if (request()->isPost() && request()->isAjax()) {
            $talk = request()->post('talk');
            $scores = request()->post('scores');
            Db::startTrans();
            try {
                $gambitInfo = Db::name('gambit')
                    ->where('gambit_name', $talk)
                    ->where('much_id', $this->much_id)
                    ->find();
                if ($gambitInfo && $gambitInfo['is_del'] == 0) {
                    return json(['code' => 0, 'msg' => '新增话题失败，该话题名称已存在']);
                } elseif ($gambitInfo && $gambitInfo['is_del'] == 1) {
                    Db::name('gambit')
                        ->where('id', $gambitInfo['id'])
                        ->where('gambit_name', $talk)
                        ->where('much_id', $this->much_id)
                        ->update(['add_time' => time(), 'is_del' => 0]);
                } else {
                    Db::name('gambit')->insert(['gambit_name' => $talk, 'add_time' => time(), 'scores' => $scores, 'is_del' => 0, 'much_id' => $this->much_id]);
                }
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '保存成功']);
            }
        }
    }

    //删除话题
    public function themeRemove()
    {
        if (request()->isPost() && request()->isAjax()) {
            $tgid = request()->post('tgid');
            Db::startTrans();
            try {
                Db::name('gambit')->where('id', $tgid)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '删除成功']);
            }
        }
    }


    //恢复话题
    public function themeRepeat()
    {
        if (request()->isPost() && request()->isAjax()) {
            $tgid = request()->post('tgid');
            Db::startTrans();
            try {
                Db::name('gambit')->where('id', $tgid)->where('much_id', $this->much_id)->update(['is_del' => 0]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                return json(['code' => 1, 'msg' => '恢复成功']);
            }
        }
    }

}