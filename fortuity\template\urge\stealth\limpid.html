{extend name="/base"/}
{block name="main"}
<style>.am-table > tbody > tr > td{font-size:14px;margin-top:8px;}.am-table > tbody > tr > td:nth-child(1){text-align:right;color:#000000;padding-right:20px;}.am-table > tbody > tr > td:nth-child(2){padding-left:20px;color:#999;}table{table-layout:fixed;word-wrap:break-word;}img{max-width: 100% !important;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-comment-o"></span> 小秘密详情
        </div>
        <div style="text-align: right">
            <a href="{:url('stealth/softly')}" target="_blank">
                <button type="button" class="am-btn am-btn-default am-btn-sm">小秘密列表</button>
            </a>
            <button type="button" class="am-btn am-btn-danger am-btn-sm" onclick="punishment();">删除小秘密</button>
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12">
                <div class="am-form am-form-horizontal" style="border:solid 1px #cccccc;width:700px;margin: 0 auto;padding:25px 3% 10px 3%;overflow: hidden;box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 1);">
                    <table class="am-table am-table-bordered">
                        <tr>
                            <td class="am-text-middle" style="width:35%;">用户昵称</td>
                            <td class="am-text-middle">
                                <a href="{:url('user/index')}&egon=0&openid={$list.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    <span style="font-size:13px;position:relative;top:2px;">{$list.user_nick_name|emoji_decode}</span>
                                </a>
                            </td>
                        </tr>
                        {if $list.at_user_id != 0}
                        <tr>
                            <td class="am-text-middle" style="width:35%;">@的好友</td>
                            <td class="am-text-middle">
                                <a href="{:url('user/index')}&egon=0&openid={$list.at_user_wechat_open_id}&page=1" title="{$vo.at_user_nick_name|emoji_decode}" target="_blank">
                                    <span style="font-size:13px;position:relative;top:2px;">{$list.at_user_nick_name|emoji_decode}</span>
                                </a>
                            </td>
                        </tr>
                        {/if}
                        <tr>
                            <td class="am-text-middle">小秘密内容</td>
                            <td class="am-text-middle" style="max-height:400px;width:100%;overflow:auto;">
                                {$list.content|emoji_decode|$expressionHtml}
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">点赞人数</td>
                            <td class="am-text-middle">
                                {$list.praise_number} 人
                                <span style="font-size:12px;margin-left:5px;cursor:pointer;" data-am-modal="{target: '#shandsel-1', closeViaDimmer: 0, width: 400, height: 185}" title="修改点赞人数" onclick="sendScamperFrequencySpecifyDefaultValue(0);">
                                    <span class="am-icon-edit"></span> 修改点赞人数
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">发布时间</td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$list.send_time)}
                                <span style="font-size:12px;margin-left:5px;cursor:pointer;" data-am-modal="{target: '#shandsel-2', closeViaDimmer: 0, width: 400, height: 185}" title="修改发布时间" onclick="sendScamperFrequencySpecifyDefaultValue(1);">
                                    <span class="am-icon-edit"></span> 修改发布时间
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">审核状态</td>
                            <td class="am-text-middle">
                                {if $list.status==0}
                                <span class="am-text-warning">待审核</span>
                                {elseif $list.status==1}
                                <span class="am-text-success">已通过</span>
                                {elseif $list.status==2}
                                <span class="am-text-danger">未通过</span>
                                {/if}
                            </td>
                        </tr>
                        {if $list.check_time}
                        <tr>
                            <td class="am-text-middle">审核时间</td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$list.check_time)}
                            </td>
                        </tr>
                        {/if}
                    </table>
                    {if $list.status==0}
                    <div class="am-form-group" style="text-align:center;margin-top:10px;">
                        <div class="am-u-sm-12">
                            <button type="button" class="am-btn am-btn-success am-btn-sm  am-round" style="margin-right: 120px;" onclick="judgment(1);">
                                通过
                            </button>
                            <button type="button" class="am-btn am-btn-warning am-btn-sm  am-round" onclick="judgment(2);">
                                拒绝
                            </button>
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>
</div>
<div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel-1">
    <div class="am-modal-dialog" style="background:#fefffe;">
        <div class="am-modal-hd">
            <span style="font-size: 14px;position: absolute;left:12px;top:7px;">修改点赞人数</span>
            <a href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
        </div>
        <div class="am-modal-bd am-form tpl-form-line-form">
            <div class="am-form-group" style="margin-top:35px;">
                <label class="am-u-sm-4 am-form-label" style="font-size:14px;margin:5px 0 0 -5px;">点赞人数</label>
                <div class="am-u-sm-6">
                    <input type="number" id="newPraiseNumber" oninput="digitalCheck(this);" class="tpl-form-input" style="margin:3px 0 0 -10px;" placeholder="请输入点赞人数">
                </div>
                <div class="am-u-sm-2 am-form-label" style="margin-top:5px;">
                    <span style="position:relative;left:-40px;font-size:14px;">人</span>
                </div>
            </div>
            <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top:10px;">
                <button type="button" class="am-btn am-btn-sm" class="am-btn" onclick="sendScamperFrequency(0);">保存</button>
            </div>
        </div>
    </div>
</div>
<div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel-2">
    <div class="am-modal-dialog" style="background:#fefffe;">
        <div class="am-modal-hd">
            <span style="font-size: 14px;position: absolute;left:12px;top:7px;">修改发布时间</span>
            <a href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
        </div>
        <div class="am-modal-bd am-form tpl-form-line-form">
            <div class="am-form-group" style="margin-top:35px;">
                <label class="am-u-sm-4 am-form-label" style="font-size:14px;margin:5px 0 0 -5px;">发布时间</label>
                <div class="am-u-sm-8">
                    <input class="newSendTime" type="text" placeholder="请选择发布时间" readonly style="cursor: pointer;">
                </div>
            </div>
            <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top:10px;">
                <button type="button" class="am-btn am-btn-sm" class="am-btn" onclick="sendScamperFrequency(1);">保存</button>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script src="static/datetime/laydate.js"></script>
<script>

    $(function () {
        laydate.render({
            elem: '.newSendTime',
            value: '{if $list.send_time}{:date(\'Y-m-d H:i:s\',$list.send_time)}{/if}',
            type: 'datetime'
        });
    });

    var sendScamperFrequencySpecifyDefaultValue = function (type) {
        switch (type) {
            case 0:
                $('#newPraiseNumber').val('{$list.praise_number}');
                break;
        }
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var sendScamperFrequency = function (type) {
        var sendData = {};
        sendData['type'] = type;
        sendData['paid'] = '{$list.id}';
        switch (parseInt(type)) {
            case 0:
                sendData['newPraiseNumber'] = $('#newPraiseNumber').val();
                break;
            case 1:
                sendData['newSendTime'] = $('.newSendTime').val();
                break;
        }
        $.post("{:url('stealth/updateLimpidFrequency')}", sendData, function (data) {
            layer.msg(data.msg, {icon: data.code > 0 ? 1 : 5, time: data.code > 0 ? 1200 : 2000}, function () {
                location.reload();
            });
        }, 'json');
    }

    var judgment = function (code) {
        if (code === 1) {
            layer.confirm("您确定同意通过审核吗？", {
                btn: ['确定', '取消'], title: '提示'
            }, function () {
                transmitDoom(code);
            }, function (index) {
                layer.close(index);
            });
        } else {
            layer.prompt({'title': "请您输入拒绝原因："}, function (rejectValue, index) {
                if ($.trim(rejectValue) === '') {
                    return false;
                }
                transmitDoom(code, rejectValue);
                layer.close(index);
            });
        }
    }

    var transmitDoom = function (code, caption) {
        $.ajaxSettings.async = false;
        $.post("{:url('stealth/teaching')}", {'sid': '{$list.id}', code: code, caption: caption}, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                    location.reload();
                });
            }
        }, 'json');
        $.ajaxSettings.async = true;
    }

    var punishment = function () {
        layer.confirm('您确定要删除这条小秘密吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('stealth/delSoftly')}", {sid: '{$list.id}'}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.href = "{:url('stealth/softly')}";
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

</script>
{/block}