{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-tasks {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .action-btn {display: inline-block;padding: 6px 12px;background: #fff;border: 1px solid #ddd;color: #23b7e5;border-radius: 3px;font-size: 12px;cursor: pointer;transition: all 0.3s;text-decoration: none;}
    .action-btn:hover {border-color: #23b7e5;color: #23b7e5;background-color: #f5fafd;text-decoration: none;}
    .action-btn.btn-success {background: #5cb85c;border-color: #5cb85c;color: #fff;}
    .action-btn.btn-success:hover {background: #449d44;border-color: #449d44;color: #fff;}
    .action-btn.btn-danger {background: #d9534f;border-color: #d9534f;color: #fff;}
    .action-btn.btn-danger:hover {background: #c9302c;border-color: #c9302c;color: #fff;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
    .sort-input {width: 50px;padding: 4px 6px;border: 1px solid #e8e8e8;border-radius: 3px;text-align: center;font-size: 12px;transition: all 0.3s;}
    .sort-input:focus {border-color: #23b7e5;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-tasks"></span> 任务列表
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索任务名称...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-6">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <a href="{:url('manual/newPropagate')}" class="action-btn btn-success">
                            <span class="am-icon-plus"></span> 新增任务
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="10%">排序</th>
                            <th width="10%">任务名称</th>
                            <th width="10%">任务类型</th>
                            <th width="10%">任务周期</th>
                            <th width="10%">奖励类型</th>
                            <th width="10%">所需次数</th>
                            <th width="10%">普通用户奖励</th>
                            <th width="10%">会员用户奖励</th>
                            <th width="10%">任务添加时间</th>
                            <th width="10%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                <input type="text" class="sort-input" id="sfixed-{$vo.id}" value="{$vo.scores}" data-score="{$vo.scores}" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                            </td>
                            <td class="am-text-middle">{$vo.task_name}</td>
                            <td class="am-text-middle">
                                {switch $vo.task_type}
                                {case 0}发布帖子{/case}
                                {case 1}回复帖子{/case}
                                {case 2}帖子点赞{/case}
                                {case 3}帖子收藏{/case}
                                {case 4}帖子转发{/case}
                                {case 5}赠送礼物{/case}
                                {case 6}充值贝壳{/case}
                                {case 7}关注用户{/case}
                                {case 7}购买商品{/case}
                                {case 9}邀请好友{/case}
                                {case 10}每日签到{/case}
                                {case 11}购买会员{/case}
                                {case 12}观看广告{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                {switch $vo.task_cycle}
                                {case 0}每日任务{/case}
                                {case 1}每周任务{/case}
                                {case 2}每月任务{/case}
                                {case 3}每年任务{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                {switch $vo.task_reward_type}
                                {case 0}{$defaultNavigate.confer}奖励{/case}
                                {case 1}经验奖励{/case}
                                {case 2}荣誉奖励{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">{$vo.task_frequency}次</td>
                            <td class="am-text-middle">
                                {$vo.poor_task_salary}
                                {switch $vo.task_reward_type}
                                {case 0}积分{/case}
                                {case 1}经验{/case}
                                {case 2}荣誉{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                {$vo.rich_task_salary}
                                {switch $vo.task_reward_type}
                                {case 0}积分{/case}
                                {case 1}经验{/case}
                                {case 2}荣誉{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">{:date('Y-m-d H:i:s',$vo.channel_time)}</td>
                            <td class="am-text-middle">
                                <a href="{:url('manual/editPropagate')}&layid={$vo.id}" target="_blank" class="action-btn" style="margin-right: 5px;">
                                    <span class="am-icon-edit"></span> 编辑
                                </a>
                                <button type="button" class="action-btn btn-danger" onclick="eraseTask('{$vo.id}');">
                                    <span class="am-icon-trash"></span> 删除
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var supre = function (asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            $.post("{:url('manual/propagateSort')}", {asyId, dalue}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 800});
                    $(domId).attr('data-score', dalue);
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600});
                }
            });
        }
    }

    var eraseTask = function (taid) {
        layer.confirm('您确定要删除这条任务吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('manual/delPropagate')}", {taid: taid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 800}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600}, function () {
                        location.reload();
                    });
                }
            });
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('manual/propagate')}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('manual/propagate')}&page={$page}";
        }
    }

</script>
{/block}