<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>文件保存信息</title>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <style>.pagination{font-size:12px}.select-transfer{cursor:pointer}.select-transfer:hover{color:red}a{color:#000;text-decoration:none !important}a:hover{color:#000}.tpl-table-black-operation-del{display:inline-block;padding:5px 6px;font-size:12px;line-height:12px;border:1px solid #e7505a;color:#e7505a}.tpl-table-black-operation-del:hover{background:#e7505a;color:#fff}</style>
</head>
<body>
<div class="am-g">
    <div class="am-form" style="min-height: 465px;">
        <table class="am-table am-table-compact am-table-bordered am-table-radius am-table-striped">
            <thead>
            <tr>
                <th class="text-center" style="width:25%;">
                    所属用户
                </th>
                <th class="text-center" style="width:30%;">
                    文件名称
                </th>
                <th class="text-center" style="width:20%;">
                    保存时间
                </th>
                <th class="text-center" style="width:25%;">
                    操作
                </th>
            </tr>
            </thead>
            <tbody>
            {volist name="list" id="vo"}
            <tr>
                <td class="text-center">
                    {if $vo.user.uvirtual == 0}
                    <a href="{:url('user/index')}&openid={$vo.user.user_wechat_open_id}&page=1" title="{$vo.user.user_nick_name|emoji_decode}" target="_blank">
                        {$vo.user.user_nick_name|emoji_decode}
                    </a>
                    {else}
                    <a href="{:url('user/theoretic')}&openid={$vo.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user.user_nick_name|emoji_decode}">
                        {$vo.user.user_nick_name|emoji_decode}
                    </a>
                    {/if}
                </td>
                <td class="text-center">
                    <span title="{$vo.file_name}.{$vo.ncInfo.file_suffix}">
                        {$vo.file_name|subtext=10}.{$vo.ncInfo.file_suffix}
                    </span>
                </td>
                <td class="text-center">
                    {:date('Y-m-d H:i:s',$vo.add_time)}
                </td>
                <td class="text-center">
                    <span onclick="transferCard('{$vo.id}');">
                         <a href="javascript:void(0);" class="tpl-table-black-operation-del">
                            <i class="am-icon-trash"></i> 删除
                        </a>
                    </span>
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>
    </div>
</div>
<div class="am-cf text-center">
    {$list->render()}
</div>
</body>
<script src="assets/js/jquery.min.js"></script>
<script src="assets/js/bootstrap.min.js"></script>
<script>
    var transferCard = function (fid) {
        parent.layer.confirm('您确定要删除当前选择的数据吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('cloud/DelUserFileInfo')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    parent.layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    parent.layer.msg(data.msg, {icon: 5, time: 2200});
                }
            }, 'json');
        }, function (index) {
            parent.layer.close(index);
        });
    }
</script>
</html>