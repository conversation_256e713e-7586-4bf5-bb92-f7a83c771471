<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>礼物明细</title>
    <style>.am-table-centered{margin:0;}a{color:#000000 !important;text-decoration:none !important;}a:hover{color:#000000 !important;}div{font-size:14px;}</style>
</head>
<link rel="stylesheet" href="assets/css/amazeui.min.css?v=1.0"/>
<body>
<table class="am-table am-table-bordered am-table-centered">
    <tr>
        <th width="30%">{if $type==0}受赠用户{else}赠送用户{/if}</th>
        <th width="70%">礼物详情</th>
    </tr>
    {volist name="list" id="vo"}
    <tr>
        <td class="am-table-centered am-text-middle" title="{$vo.user_nick_name|emoji_decode}">
            <img src="{$vo.user_head_sculpture}" style="width:50px;height:50px;border-radius:50%;">
            <span style="position:relative;top:5px;font-size:12px;">
                {if $vo.uvirtual == 0}
                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                    {$vo.user_nick_name|emoji_decode}
                </a>
                {else}
                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                    {$vo.user_nick_name|emoji_decode}
                </a>
                {/if}
            </span>
        </td>
        <td class="am-table-centered am-text-middle">
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">
                物品名称
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">
                {$vo.bute_name}
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">
                物品单价
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">
                {$vo.bute_price|number_format=2} ( {$defaultNavigate.currency} )
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">
                花费金额
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">
                {$vo.bute_price * $vo.num|number_format=2 } ( {$defaultNavigate.currency} )
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">
                收益利率
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">
                {$vo.allow_scale*100}%
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">
                结算金额
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">
                {$vo.bute_price * $vo.num * $vo.allow_scale|number_format=2} ( {$defaultNavigate.currency} )
            </div>
            <div style="float:left;width:50%;height:50%;border-right:#dddddd solid 1px;">
                {if $type==0}受赠{else}赠送{/if}时间
            </div>
            <div style="float:left;width:50%;height:50%;">
                {:date('Y-m-d H:i:s',$vo.bute_time)}
            </div>
        </td>
    </tr>
    {/volist}
</table>
<script src="assets/js/jquery.min.js?v=1.0"></script>
<script src="assets/js/amazeui.min.js?v=1.0"></script>
</body>
</html>