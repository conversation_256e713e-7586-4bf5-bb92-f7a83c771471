<?php

namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

use think\cache\driver\Redis;

class RedisLock
{

    /**
     * @var object|null
     */
    public $_redis;

    public function __construct()
    {
        try {
            $config = [];
            //  读取配置
            require __DIR__ . '/../../../../../data/config.php';
            //  初始化Redis配置
            $redisConfig = $config['setting']['redis'];
            //  判断是否配置redis
            if ($redisConfig && (trim($redisConfig['server']) !== '')) {
                //  创建连接
                $redis = new Redis([
                        'host' => $redisConfig['server'],
                        'port' => $redisConfig['port'],
                        'password' => $redisConfig['password'],
                        'timeout' => $redisConfig['timeout']]
                );
                //  判断连接是否正常
                $redis->has('connection_test');
                return $this->_redis = $redis->handler();
            } else {
                return $this->_redis = null;
            }
        } catch (\Exception $e) {
            return $this->_redis = null;
        }
    }

    /**
     * 获取锁
     * @param String $key 锁标识
     * @param Int $expire 锁过期时间
     * @param Int $num 重试次数
     * @return Boolean
     */
    public function lock($key, $expire = 5, $num = 0)
    {
        $is_lock = $this->_redis->setnx($key, time() + $expire);

        if (!$is_lock) {
            //获取锁失败则重试{$num}次
            for ($i = 0; $i < $num; $i++) {
                $is_lock = $this->_redis->setnx($key, time() + $expire);

                if ($is_lock) {
                    break;
                }
                sleep(1);
            }
        }

        // 不能获取锁
        if (!$is_lock) {

            // 判断锁是否过期
            $lock_time = $this->_redis->get($key);

            // 锁已过期，删除锁，重新获取
            if (time() > $lock_time) {
                $this->unlock($key);
                $is_lock = $this->_redis->setnx($key, time() + $expire);
            }
        }

        return $is_lock ? true : false;
    }

    /**
     * 释放锁
     * @param String $key 锁标识
     * @return Boolean
     */
    public function unlock($key)
    {
        return $this->_redis->del($key);
    }

}