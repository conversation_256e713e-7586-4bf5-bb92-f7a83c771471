{extend name="/base"/}
{block name="main"}
<style>.am-modal-hd .am-close {top: -5px;right: -3px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-edit"></span> 回复帖子
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">回帖用户昵称</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input title="{$userInfo.user_nick_name|emoji_decode}" type="text" value="{$userInfo.user_nick_name|emoji_decode}" disabled>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">回复类型</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select id="replyType" onchange="changeReplyType(this);">
                                <option value="0">回复帖子</option>
                                <option value="1">帖子回复 ( 楼中楼 )</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label replyText">帖子ID</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="number" id="paper_id" oninput="digitalCheck(this);">
                            <small class="replyText">请输入帖子ID ( 帖子列表可以找到 )</small>
                        </div>
                    </div>
                    <div class="am-form-group replyPicture" style="display:none;">
                        <label class="am-u-sm-3 am-form-label">被回复用户的UID</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="number" id="uid" oninput="digitalCheck(this);">
                            <small>请输入被回复用户的UID ( 用户列表可以找到 ) [ 非必填 用于回复指定用户 ]</small>
                        </div>
                    </div>
                    <div class="am-form-group replyPicture">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:5px;">回复图片</label>
                        <div class="am-u-sm-3">
                            <div id="shion">
                                <div class="multiple-img" style="width:120px;height:120px;margin:10px 20px 10px 0px;position: relative;float: left;">
                                    <img src="" name="sngimg" onerror="this.src='static/disappear/default.png'" style="width: 120px;height: 120px;margin: 7px 0px 0px 3px;"/>
                                    <div style="width: 126px;height: 126px;position: absolute;top: 4px;border: 1px solid #cccccc;border-radius: 3px;">
                                        <div class="am-modal-hd" style="text-align: left;">
                                            <a href="javascript: void(0);" class="am-close am-close-spin" style="color:#2D93CA;opacity:1;" onclick="multipleClose(this);">×</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="am-u-sm-2 am-u-end" style="margin-top:60px;margin-left: -100px;">
                            <button type="button" style="font-size: 12px;" onclick="cuonice();">选择图片</button>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">回复内容</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div style="width: 100%;height: 100%;margin-top: 5px;position: relative;">
                                <div style="position: absolute;top: 0;padding-left: 3px;background: #dddddd;width: 100%;height: 30px;opacity: 0.75;">
                                    <ul style="display: flex;">
                                        <li style="width: 30px;display: flex;justify-content: center;color: #1055ab;">
                                            <span style="cursor: pointer;" title="点击添加表情" onclick="complicatedFunc(0);">
                                                <span class="am-icon-meh-o"></span>
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                                <textarea id="content" style="height: 400px;resize: none;padding-top: 35px;"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin-top: 30px;">
                        <div class="am-u-sm-8 am-u-end am-u-sm-push-3">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script src="assets/js/jquery.dad.min.js"></script>
<script>

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var changeReplyType = function (obj) {
        var replyPicture = $('.replyPicture');
        if (obj.value == 0) {
            replyPicture.eq(0).hide();
            replyPicture.eq(1).show();
            $('label.replyText').text('帖子ID');
            $('div > small.replyText').text('请输入帖子ID ( 帖子列表可以找到 )');
        } else {
            replyPicture.eq(1).hide();
            replyPicture.eq(0).show();
            $('label.replyText').text('帖子回复ID');
            $('div > small.replyText').text('请输入帖子回复ID ( 帖子回复可以找到 )');
        }
    }

    function cuonice() {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    function sutake(eurl) {
        var multipleImg = $.trim($('.multiple-img').eq(0).attr('data-multiple-img'));
        if (multipleImg == '') {
            $('#shion').html('');
        }
        var shtml = '<div class="multiple-img" style="width:120px;height:120px;margin:10px 20px 10px 0px;position: relative;float: left;" data-multiple-img="' + eurl + '">';
        shtml += '<img src="' + eurl + '" name="sngimg" onerror="this.src=\'static/disappear/default.png\'" style="width: 120px;height: 120px;margin: 7px 0px 0px 3px;"/>';
        shtml += '<div style="width: 126px;height: 126px;position: absolute;top: 4px;border: 1px solid #cccccc;border-radius: 3px;">';
        shtml += '<div class="am-modal-hd" style="text-align: left;">';
        shtml += '<a href="javascript: void(0);" class="am-close am-close-spin" style="color:#2D93CA;opacity:1;" onclick="multipleClose(this);">×</a>';
        shtml += '</div>';
        shtml += '</div>';
        shtml += '</div>';
        $('#shion').html(shtml);
        layer.closeAll();
    }

    function multipleClose(obj) {
        $(obj).parent().parent().parent().remove();
        setTimeout(function () {
            if ($('.multiple-img').length < 1) {
                var shtml = '<div class="multiple-img" style="width:120px;height:120px;margin:10px 20px 10px 0px;position: relative;float: left;">';
                shtml += '<img src="" name="sngimg" onerror="this.src=\'static/disappear/default.png\'" style="width: 120px;height: 120px;margin: 7px 0px 0px 3px;"/>';
                shtml += '<div style="width: 126px;height: 126px;position: absolute;top: 4px;border: 1px solid #cccccc;border-radius: 3px;">';
                shtml += '<div class="am-modal-hd" style="text-align: left;">';
                shtml += '<a href="javascript: void(0);" class="am-close am-close-spin" style="color:#2D93CA;opacity:1;" onclick="multipleClose(this);">×</a>';
                shtml += '</div>';
                shtml += '</div>';
                shtml += '</div>';
                $('#shion').append(shtml);
            }
        }, 500);
    }

    var holdSave = function () {
        //  帖子ID
        var paperId = $.trim($('#paper_id').val());
        //  回复内容
        var content = $.trim($('#content').val());
        //  回复类型
        var replyType = Number($('#replyType').val());
        //  判断回复类型
        if (replyType === 0) {
            if (paperId === '') {
                layer.msg('帖子ID不能为空');
                return;
            }
            var multipleImg = $.trim($('.multiple-img').attr('data-multiple-img'));
            if (content === '' && multipleImg === '') {
                layer.msg('回帖内容或回复图片必填一项');
                return;
            }
            $.ajaxSettings.async = false;
            $.post("{:url('user/reticrpaper')}", {
                'userId': '{$userInfo.id}',
                'paperId': paperId,
                'content': content,
                'multipleImg': multipleImg
            }, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
            $.ajaxSettings.async = true;
        } else {
            if (paperId === '') {
                layer.msg('帖子回复ID不能为空');
                return;
            }
            if (content === '') {
                layer.msg('请输入回复内容！');
                return;
            }
            var uid = $.trim($('#uid').val());
            $.ajaxSettings.async = false;
            $.post("{:url('stealth/replyBuilding')}", {
                'userId': '{$userInfo.id}',
                'paperId': paperId,
                'uid': uid,
                'content': content
            }, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }
    }

    var complicatedFunc = function (type) {
        switch (type) {
            case 0:
                layer.open({
                    type: 2,
                    anim: 2,
                    title: false,
                    area: ['350px', '360px'],
                    scrollbar: true,
                    closeBtn: false,
                    shadeClose: true,
                    content: ["{:url('tedious/emoji')}&type=" + type, 'no'],
                });
                break;
        }
    }

    var complicatedCallFunc = function (type, data) {
        switch (type) {
            case 0:
                var content = $('#content');
                content.val(content.val() + data['content']);
                break;
        }
    }

</script>
{/block}