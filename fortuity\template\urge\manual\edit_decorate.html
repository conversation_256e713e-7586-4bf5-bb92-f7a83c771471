{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-edit"></span> 编辑勋章
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">勋章名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" value="{$list.merit_name}" placeholder="请输入勋章名称">
                            <small>建议勋章名称不超过四个字</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">勋章图标</label>
                        <div class="am-u-sm-9">
                            <img src="{$list.merit_icon}" id="shion" onerror="this.src='static/disappear/default.png'" onclick="cuonice();" style="width: 150px;height: 150px;cursor: pointer;"/>
                            <button type="button" style="margin-left:10px;font-size: 12px;" onclick="cuonice();">
                                选择图片
                            </button>
                            <small>建议选择图片大小尺寸为：128*128px 以内</small>
                            <input type="hidden" value="{$list.merit_icon}" name="sngimg">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">勋章介绍</label>
                        <div class="am-u-sm-9">
                            <textarea rows="8" id="annotate" style="resize: none;" placeholder="请输入勋章介绍">{$list.merit_annotate}</textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">所需荣誉点</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="outlay" value="{$list.unlock_outlay}" placeholder="请输入解锁勋章所需要的荣誉点数" oninput="digitalCheck(this);">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">状态</label>
                        <div class="am-u-sm-9">
                            <select id="status">
                                <option value="0" {if $list.status==0}selected{/if}>隐藏</option>
                                <option value="1" {if $list.status==1}selected{/if}>显示</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="scores" value="{$list.scores}" placeholder="请输入排序数字" oninput="digitalCheck(this);">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-push-3">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        $('#shion').attr('src', eurl);
        $("[name='sngimg']").val(eurl);
        layer.closeAll();
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
        return obj.value;
    }

    var onlock = false;
    var holdSave = function () {
        var setData = {};
        setData['merit_name'] = $.trim($('#name').val());
        if (setData['merit_name'] == '') {
            layer.msg('请输入勋章名称');
            return;
        }
        setData['merit_icon'] = $.trim($('[name=\'sngimg\']').val());
        if (setData['merit_icon'] == '') {
            layer.msg('请选择勋章图片');
            return;
        }
        setData['merit_annotate'] = $.trim($('#annotate').val());
        if (setData['merit_annotate'] == '') {
            layer.msg('请输入勋章介绍');
            return;
        }
        setData['unlock_outlay'] = digitalCheck($('#outlay')[0]);
        setData['status'] = $('#status').val();
        setData['scores'] = digitalCheck($('#scores')[0]);
        if (!onlock) {
            onlock = true;
            $.post("{:url('manual/editDecorate')}", {meid: '{$list.id}', setData}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600}, function () {
                        onlock = false;
                    });
                }
            }, 'json');
        }
    }
</script>
{/block}