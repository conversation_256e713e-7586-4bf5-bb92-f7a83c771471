{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 推送消息给部分用户
        </div>
        <div class="tpl-portlet-input tpl-fz-ml right">
            <a href="{:url('user/index')}" target="_blank" title="点击跳转用户列表">
                <span style="font-size: 12px;font-weight: bold;color:black;">用户 UID 编号 可在用户列表中查看</span>
            </a>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form" style="margin-top:30px;">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">UID区间</label>
                        <div class="am-u-sm-2">
                            <input type="number" id="userIntervalLeft" oninput="grender(this);" placeholder="请输入用户UID编号">
                            <small style="font-weight:bold;">请输入用户 UID 编号 例如 1 - 100</small>
                        </div>
                        <div class="am-u-sm-1" style="text-align:center;width:4%;">
                            —
                        </div>
                        <div class="am-u-sm-2 am-u-end">
                            <input type="number" id="userIntervalRight" oninput="grender(this);" placeholder="请输入用户UID编号">
                            <small style="font-weight:bold;">UID 区间不要过大 严重会导致服务器宕机</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">订阅消息</label>
                        <div class="am-u-sm-6 am-u-end">
                            <select id="tmplMutual">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>
                                需要配置 订阅消息 <strong>YL0009 ( 站内信提醒 )</strong> 后使用<strong> ( 长期不活跃用户可能接收不到订阅消息 )</strong>
                            </small>
                            <br>
                            <small style="font-weight:bold;">
                                发送前请务必设置超时时间为最大值
                                <span style="color:red;">max_execution_time</span>
                                &
                                <span style="color:red;">max_input_time</span>
                                =
                                <span style="color:red;">10800</span>
                                秒
                                ( 防止超时造成消息发送操作停止 )
                            </small>
                        </div>
                    </div>

                    <div class="am-form-group" style="margin-top:8px;">
                        <label class="am-u-sm-3 am-form-label">
                            消息内容
                        </label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 5px;">
                            <textarea id="messageContent" style="height:400px;overflow:auto;resize:none;" placeholder="请输入您要推送的消息内容"></textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-7 am-u-sm-push-5" style="margin:20px 0 0 3.5%;">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">发送消息</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    var grender = function (obj) {
        obj.value = Number(obj.value.match(/^\d+(?:\.\d{0,0})?/));
    }

    var holdSave = function () {
        var userIntervalLeft = $('#userIntervalLeft').val();
        var userIntervalRight = $('#userIntervalRight').val();
        if (userIntervalLeft == 0 || userIntervalRight == 0) {
            layer.msg('请输入正确的UID区间');
            return;
        }
        var messageContent = $.trim($('#messageContent').val());
        var tmplMutual = $('#tmplMutual').val();
        if (messageContent == '') {
            layer.msg('消息内容不能为空');
            return;
        }
        var alertTitle = '您确定要群发消息给指定区间用户吗？<br><span style="color:red;font-weight:bold;">( 用户量过于庞大时可能会造成无法弥补的后果 )</span>';
        layer.confirm(alertTitle, {
            btn: ['确定', '取消'], title: '危险操作提醒'
        }, function (index) {
            layer.close(index);
            var loadForm = layer.load(1, {shade: [0.1, '#fff']});
            $.ajax({
                type: "post",
                url: "{:url('unlawful/executeDestroyPlan')}",
                data: {userIntervalLeft, userIntervalRight, messageContent, tmplMutual},
                success: function (data) {
                    if (data.code > 0) {
                        layer.close(loadForm);
                        layer.msg('命令执行完成', {icon: 1, time: 1000});
                    } else {
                        layer.close(loadForm);
                        var errorTitle = '<span style="font-size:12px;">部分命令执行成功<br/><strong>可能会存在部分用户没有收到消息的情况</strong></span>';
                        layer.msg(errorTitle, {icon: 1, time: 2500});
                    }
                },
                statusCode: {
                    404: function () {
                        layer.close(loadForm);
                        layer.msg('运行超时，中断执行', {icon: 5, time: 1600});
                    }
                }
            });
        }, function (index) {
            layer.close(index);
        });
    }
</script>
{/block}