<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>二手交易内容详情</title>
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <style>img{max-width:100%;}a,a:hover,a:focus{color:#000;}</style>
</head>
<body>
<div class="am-g">
    <div class="am-form" style="min-height: 465px;">
        <table class="am-table am-table-compact am-table-bordered am-table-radius am-table-striped">
            <tbody>
            <tr>
                <td style="width: 35%;text-align: center;padding-right: 10px;">
                    用户昵称
                </td>
                <td style="width: 65%;padding: 3px 10px 0 10px;">
                    {if $list.user.uvirtual == 0}
                    <a href="{:url('user/index')}&openid={$list.user.user_wechat_open_id}&page=1" title="{$list.user.user_nick_name|emoji_decode}" target="_blank">
                        {$list.user.user_nick_name|emoji_decode}
                    </a>
                    {else}
                    <a href="{:url('user/theoretic')}&hazy_name={$list.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$list.user.user_nick_name|emoji_decode}">
                        {$list.user.user_nick_name|emoji_decode}
                    </a>
                    {/if}
                </td>
            </tr>
            <tr>
                <td style="width: 35%;text-align: center;padding-right: 10px;">
                    物品名称
                </td>
                <td style="width: 65%;padding: 3px 10px 0 10px;">
                    {$list.item_name|emoji_decode}
                </td>
            </tr>
            <tr>
                <td style="width: 35%;text-align: center;padding-right: 10px;">
                    物品类型
                </td>
                <td style="width: 65%;padding: 3px 10px 0 10px;">
                    {$list.type_name}
                </td>
            </tr>
            <tr>
                <td style="width: 35%;text-align: center;padding-right: 10px;">
                    发布类型
                </td>
                <td style="width: 65%;padding: 3px 10px 0 10px;">
                    {switch $list.release_type}
                    {case 0}出售{/case}
                    {case 1}求购{/case}
                    {case 2}租赁{/case}
                    {case 3}置换{/case}
                    {case 4}私人定制{/case}
                    {/switch}
                </td>
            </tr>
            <tr>
                <td style="width: 35%;text-align: center;padding-right: 10px;">
                    详细描述
                </td>
                <td style="width: 65%;padding: 3px 10px 0 10px;">
                    {$list.item_detail|emoji_decode|$expressionHtml}
                </td>
            </tr>
            <tr>
                <td style="width: 35%;text-align: center;padding-right: 10px;">
                    交易地点
                </td>
                <td style="width: 65%;padding: 3px 10px 0 10px;">
                    {$list.secondhand_address}
                </td>
            </tr>
            <tr>
                <td style="width: 35%;text-align: center;padding-right: 10px;">
                    联系方式
                </td>
                <td style="width: 65%;padding: 3px 10px 0 10px;">
                    {$list.contact_details}
                </td>
            </tr>
            <tr>
                <td style="width: 35%;text-align: center;padding-right: 10px;">
                    物品状态
                </td>
                <td style="width: 65%;padding: 3px 10px 0 10px;">
                    {switch $list.item_status}
                    {case 1}正常{/case}
                    {case 2}
                    {switch $list.release_type}
                    {case 0}已售出{/case}
                    {case 1}已购买{/case}
                    {default}已完成
                    {/switch}
                    {/case}
                    {/switch}
                </td>
            </tr>
            {if $list.top_time}
            <tr>
                <td style="width: 35%;text-align: center;padding-right: 10px;">
                    置顶到期时间
                </td>
                <td style="width: 65%;padding: 3px 10px 0 10px;">
                    {:date('Y-m-d H:i:s',$list.top_time)}
                </td>
            </tr>
            {/if}
            <tr>
                <td style="width: 35%;text-align: center;padding-right: 10px;">
                    审核状态
                </td>
                <td style="width: 65%;padding: 3px 10px 0 10px;">
                    {switch $list.audit_status}
                    {case 0}
                    <span style="color: orange;">待审核</span>
                    {/case}
                    {case 1}
                    <span style="color: green;">已通过</span>
                    {/case}
                    {case 2}
                    <span style="color: red;">已拒绝</span>
                    {/case}
                    {/switch}
                </td>
            </tr>
            {if $list.audit_status == 2}
            <tr>
                <td style="width: 35%;text-align: center;padding-right: 10px;">
                    拒绝原因
                </td>
                <td style="width: 65%;padding: 3px 10px 0 10px;">
                    {$list.audit_reason}
                </td>
            </tr>
            {/if}
            </tbody>
        </table>
    </div>
</div>
</body>
</html>