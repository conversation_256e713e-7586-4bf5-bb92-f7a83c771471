{extend name="/base"/}
{block name="main"}
<style>.customize-span{padding:3px 10px;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px}.cust-btn{font-size:14px;padding:5px 10px}.cust-btn-one{margin-left:-20px}.cust-btn-activate{border-bottom:solid 2px #6495ed}.layui-layer-page{border-radius:60px;overflow:hidden;background: rgb(0 0 0 / 0.15);}.template-container{width:200px;margin:30px 20px;float:left;height:391px;border-radius:10px;align-items:center;justify-content:center;position:relative}.template-container-img{height:100%;cursor:pointer;border-radius:15px;overflow:hidden;width:200px}.template-container-mask-layer{width:79%;height:10%;position:absolute;border-radius: 30px 30px 0 0;top:0}.template-container-text{display:flex;align-items:center;justify-content:center;color:#fff;text-align:center;flex-wrap:wrap}.hiden{display:none}.template-container:hover div{display:flex}.am-cf ul+span{top:25px!important;left:10px!important}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 模板列表
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="turtle();"></i>
                    <input type="text" class="form-control form-control-solid" id="waveName" value="{$waveName}" placeholder="搜索...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom:10px;">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-btn-toolbar">
                    <div class=" am-btn-group-xs" style="margin: 0 0 15px 35px;">
                        <a href="{:url('sketchpad/market')}&egon=0" class="cust-btn cust-btn-one {if $egon==0}cust-btn-activate{/if}">全部</a>
                        <a href="{:url('sketchpad/market')}&egon=1" class="cust-btn {if $egon==1}cust-btn-activate{/if}">首页</a>
                        <a href="{:url('sketchpad/market')}&egon=2" class="cust-btn {if $egon==2}cust-btn-activate{/if}">广场</a>
                        <a href="{:url('sketchpad/market')}&egon=3" class="cust-btn {if $egon==3}cust-btn-activate{/if}">商品</a>
                        <a href="{:url('sketchpad/market')}&egon=4" class="cust-btn {if $egon==4}cust-btn-activate{/if}">我的</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g" style="display: flex;justify-content: center;">
            <div class="am-u-sm-12" style="max-width: 1560px;display: flex;flex-wrap: wrap;justify-content: space-around;">
                {volist name="list" id="vo"}
                <div class="template-container" title="此模板仅支持小程序 v{$vo.narrowUnclasp} 及以上版本使用">
                    <div class="template-container-img"><!--style="box-shadow:0 0 10px 0 rgba(0, 0, 0, 1);"-->
                        <img src="{$vo.narrowImage}" width="200px" height="391px" onclick="lookImage('{$vo.narrowImage}','{$vo.narrowImageWidth}','{$vo.narrowImageHeight}','{$vo.narrowName}');">
                    </div>
                    <div class="template-container-mask-layer hiden" style="background: #000000;opacity: 0.7;width: 100%;"></div>
                    <div class="template-container-mask-layer template-container-text hiden" style="width: 100%;">
                        <span style="width:100%;position: absolute;top: 10px;">
                          v{$vo.narrowUnclasp}
                        </span>
                    </div>
                    <div style="color:#37BD72;font-size: 14px;margin:10px;text-align: center;width: 70%;display: block;">
                        {switch $vo.narrowType}{case value="0"}首页{/case}{case value="1"}广场{/case}{case value="2"}商品{/case}{case value="3"}我的{/case}{/switch}<span style="color: #000;margin-left:20px;">{$vo.narrowName}</span>
                    </div>
                    {if !$vo.under}
                    {if !$vo.narrowRamble}
                    <div class="am-btn-group am-btn-group-xs hiden" style="cursor:pointer;position:absolute;bottom:0;width:100%;background-color:red;height:40px;border-radius:0 0 30px 30px;opacity:0.7;"></div>
                    <div class="hiden" onclick="allegePlate('{$vo.narrowId}','{$vo.narrowType}')" style="cursor:pointer;justify-content:center;align-items:center;position:absolute;bottom:6px;width:100%;text-align: center;font-size:18px;color:#fff;">
                        <span class="am-icon-chevron-circle-up"></span>&nbsp;使用此模板
                    </div>
                    {else}
                    <span style="color:#fff;top:45%;text-align:center;position:absolute;width:200px;background-color:#CC3333;height:35px;line-height:35px;">未开放 ( 私人定制 )</span>
                    {/if}
                    {else}
                    <span style="color: #fff;top: 45%;text-align: center;position: absolute;width:200px;background-color: #33CC66;height: 35px;line-height: 35px;">当前正在使用</span>
                    {/if}
                </div>
                {/volist}
                {php} if(count($list)>0){ $i = 6; for($i;$i>count($list);$i--){ echo (base64_decode('PGRpdiBjbGFzcz0idGVtcGxhdGUtY29udGFpbmVyIj48L2Rpdj4=')); }; }{/php}
            </div>
        </div>
        <div class="am-g">
            <div class="tpl-alert"></div>
            <div class="am-u-sm-12 text-center">
                {$list->render()}
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var lookImage = function (url, width, height, imgAlt) {
        layer.open({
            type: 1,
            area: [width, height],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            //content: '<img src="' + url + '" width="' + width + 'px" height="' + height + 'px" title="' + imgAlt + '" alt="' + imgAlt + '">'
            content: '<img src="' + url + '" width="375px" height="auto" title="' + imgAlt + '" alt="' + imgAlt + '">'
        });
    }

    var allegePlate = function (narrowId, narrowType) {
        layer.confirm('您确定要使用此模板吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function (index) {
            layer.close(index);
            $.post("{:url('sketchpad/chooseTemplet')}", {
                'narrowId': narrowId,
                'narrowType': narrowType
            }, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1600}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }
    
    var turtle = function () {
        var waveName = $.trim($('#waveName').val());
        if (waveName) {
            location.href = "{:url('sketchpad/market')}&egon={$egon}&waveName=" + waveName + "&page={$page}";
        } else {
            location.href = "{:url('sketchpad/market')}&egon={$egon}&page={$page}";
        }
    }
</script>
{/block}