<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>短剧信息内容详情</title>
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <style>
        img {
            max-width: 100%;
        }
        a, a:hover, a:focus {
            color: #000;
        }
        .am-table {
            text-align: center; /* 水平居中 */
        }
        .am-table td {
            vertical-align: middle; /* 垂直居中 */
        }
    </style>
</head>
<body>
<div class="am-g">
    <div class="am-form" style="min-height: 465px;">
        <table class="am-table am-table-compact am-table-bordered am-table-radius am-table-striped">
            <tbody>
            {if $list.upload_user_id != 0}
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    用户昵称
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.user.user.uvirtual == 0}
                    <a href="{:url('user/index')}&openid={$list.user.user_wechat_open_id}&page=1" title="{$list.user.user_nick_name|emoji_decode}" target="_blank">
                        {$list.user.user_nick_name|emoji_decode}
                    </a>
                    {else}
                    <a href="{:url('user/theoretic')}&hazy_name={$list.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$list.user.user_nick_name|emoji_decode}">
                        {$list.user.user.user_nick_name|emoji_decode}
                    </a>
                    {/if}
                </td>
            </tr>
            {/if}
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧名称
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.title}{$list.title}{else}无{/if}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧海报图片
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    <img src="{$list.poster_url}" onerror="this.src='static/disappear/default.png'" style="width: auto; height: 150px;"/>
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧导演名
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.director}{$list.director}{else}无{/if}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧编剧名
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.screenwriter}{$list.screenwriter}{else}无{/if}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧主演名
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.lead_actors}{$list.lead_actors}{else}无{/if}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧制片国家或地区
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.production_country}{$list.production_country}{else}无{/if}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧语言
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.language}{$list.language}{else}无{/if}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧上映日期
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.release_date}{$list.release_date}{else}无{/if}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧片长
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.duration_minutes}{$list.duration_minutes}{else}无{/if}（分钟）
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧别名
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.alias}{$list.alias}{else}无{/if}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧剧情简介
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.plot_summary}{$list.plot_summary}{else}无{/if}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧总集数
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.total_episodes}{$list.total_episodes}{else}无{/if}集
                </td>
            </tr>
            {if $list.user_copyright_img}
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    用户版权或授权证书图片
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    <img src="{$list.user_copyright_img}" onerror="this.src='static/disappear/default.png'" style="width: auto; height: 150px;"/>
                </td>
            </tr>
            {/if}
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    审核状态
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {switch $list.status}
                    {case 0}
                    <span style="color: orange;">待审核</span>
                    {/case}
                    {case 1}
                    <span style="color: green;">已通过</span>
                    {/case}
                    {case 2}
                    <span style="color: red;">已拒绝</span>
                    {/case}
                    {/switch}
                </td>
            </tr>
            {if $list.audit_status == 2}
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    拒绝原因
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.audit_reason}{$list.audit_reason}{else}无{/if}
                </td>
            </tr>
            {/if}
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    显示状态
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {switch $list.display_status}
                    {case 0}
                    已下架
                    {/case}
                    {case 1}
                    正常
                    {/case}
                    {/switch}
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>
</body>
</html>
