{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;text-align:center;}.am-table > thead:first-child > tr:first-child > th{text-align:center;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-user"></span> 用户私信消息
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索内容...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="35%">用户信息</th>
                            <th width="35%">消息内容</th>
                            <th width="30%">发送时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle" style="display: flex;justify-content: center;">
                                <div style="width: 70%;text-align: left;white-space:nowrap;">
                                    <span style="margin-right: 10px;">
                                        {if $vo.user_head_sculpture && $vo.user_head_sculpture !='/yl_welore/style/icon/default.png'}
                                        <img src="{$vo.user_head_sculpture}" style="width: 50px;height: 50px;border-radius: 50%;">
                                        {else}
                                        <img src="{:urlBridging('static/disappear/tourist.png')}" style="width: 50px;height: 50px;border-radius: 50%;">
                                        {/if}
                                    </span>
                                    <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                        {$vo.user_nick_name|emoji_decode}
                                    </a>
                                </div>
                            </td>
                            <td class="am-text-middle" style="text-align: left;padding-left: 3%;">
                                {if $vo.le_type == 1}
                                <span title="点击跳转到帖子详情页面">
                                    <a href="{:url('essay/setails')}&uplid={$vo.paid}">
                                        {$vo.content|emoji_decode|$expressionHtml}
                                    </a>
                                </span>
                                {else}
                                {$vo.content|emoji_decode|$expressionHtml}
                                {/if}
                            </td>
                            <td class="am-text-middle">{:date('Y-m-d H:i:s',$vo.le_time)}</td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>
    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('journal/letterRecord')}&rid={$rid}&uid={$uid}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('journal/letterRecord')}&rid={$rid}&uid={$uid}&page={$page}";
        }
    }

</script>
{/block}