{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-gift {margin-right: 5px;color: #23b7e5;}
    .am-form-horizontal {background: #fff;border-radius: 6px;padding: 20px;box-shadow: 0 1px 3px rgba(0,0,0,0.05);}
    .am-form-group {margin-bottom: 20px;padding: 15px 0;border-bottom: 1px solid #f5f5f5;}
    .am-form-group:last-child {border-bottom: none;}
    .am-form-label {font-weight: 500;color: #333;line-height: 1.6;}
    .am-form-group input[type="text"], .am-form-group input[type="number"] {height: 36px;padding: 8px 12px;border: 2px solid #d1d5db;border-radius: 4px;background: #fff;transition: all 0.3s;font-size: 14px;}
    .am-form-group input[type="text"]:focus, .am-form-group input[type="number"]:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 3px rgba(35,183,229,0.15);}
    .am-form-group select {height: 36px;padding: 8px 12px;border: 2px solid #d1d5db;border-radius: 4px;background: #fff;transition: all 0.3s;font-size: 14px;}
    .am-form-group select:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 3px rgba(35,183,229,0.15);}
    .am-form-group small {color: #666;font-size: 12px;margin-top: 5px;display: block;line-height: 1.4;}
    .radio-group {display: flex;gap: 20px;align-items: center;}
    .radio-item {display: flex;align-items: center;gap: 8px;cursor: pointer;padding: 8px 12px;border-radius: 4px;transition: all 0.3s;}
    .radio-item:hover {background-color: #e9ecef;}
    .radio-item input[type="radio"] {margin: 0;width: 16px;height: 16px;vertical-align: middle;}
    .radio-item span {line-height: 16px;vertical-align: middle;}
    .warning-text {color: #dc3545;font-weight: 500;}
    .prize-card {background: #fff;border: 2px solid #e8e8e8;border-radius: 8px;padding: 20px;margin: 20px 0;position: relative;box-shadow: 0 1px 3px rgba(0,0,0,0.1);}
    .prize-card-header {display: flex;align-items: center;gap: 15px;margin-bottom: 15px;padding-bottom: 15px;border-bottom: 1px solid #e8e8e8;}
    .prize-card-body {display: flex;align-items: center;gap: 15px;}
    .prize-image {width: 70px;height: 70px;border-radius: 6px;cursor: pointer;border: 2px solid #e8e8e8;transition: all 0.3s;}
    .prize-image:hover {border-color: #23b7e5;}
    .editor-container {border: 1px solid #e8e8e8;border-radius: 6px;overflow: hidden;}
    .w-e-menu {font-size: 14px;}
    .w-e-text, .w-e-text-container {height: 650px !important;}
    .save-btn {background: linear-gradient(135deg, #23b7e5 0%, #1a9bc0 100%);border: none;color: white;padding: 12px 30px;border-radius: 6px;font-size: 14px;font-weight: 500;cursor: pointer;transition: all 0.3s ease;box-shadow: 0 2px 4px rgba(35,183,229,0.2);}
    .save-btn:hover {background: linear-gradient(135deg, #1a9bc0 0%, #1587a8 100%);box-shadow: 0 4px 8px rgba(35,183,229,0.3);transform: translateY(-1px);}
    .save-btn:active {transform: translateY(0);box-shadow: 0 2px 4px rgba(35,183,229,0.2);}
</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-gift"></span> 九宫格抽奖
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">抽奖名称</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="text" v-model="list.er_name">
                            <small>抽奖页面显示的标题</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">开始时间</label>
                        <div class="am-u-sm-3">
                            <input id="dateStartTime" type="text" v-once :value="list.start_time" readonly style="cursor:pointer;" ref="dateStartTime">
                            <small>抽奖活动开始时间</small>
                        </div>
                        <label class="am-u-sm-1 am-form-label">结束时间</label>
                        <div class="am-u-sm-3 am-u-end">
                            <input id="dateEndTime" type="text" v-once :value="list.end_time" readonly style="cursor:pointer;" ref="dateEndTime">
                            <small>抽奖活动结束时间</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">抽奖消耗类型</label>
                        <div class="am-u-sm-2">
                            <div class="radio-group">
                                <label class="radio-item">
                                    <input type="radio" v-model='list.deplete_type' value="0">
                                    <span v-text="touch.currency"></span>
                                </label>
                                <label class="radio-item">
                                    <input type="radio" v-model='list.deplete_type' value="1">
                                    <span v-text="touch.confer"></span>
                                </label>
                            </div>
                            <small>每一次抽奖消耗的货币类型</small>
                        </div>
                        <label class="am-u-sm-2 am-form-label">抽奖消耗分数</label>
                        <div class="am-u-sm-3 am-u-end">
                            <input type="text" v-model="list.deplete_score" @input="list.deplete_score = list.deplete_score.toString().replace(/\D/g,'')">
                            <small>每一次抽奖所消耗的分数</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">缓冲时间</label>
                        <div class="am-u-sm-2">
                            <input type="text" v-model="list.turning_speed" @input="list.turning_speed = list.turning_speed.toString().replace(/\D/g,'')">
                            <small>转盘缓冲时间 建议30-100</small>
                        </div>
                        <label class="am-u-sm-2 am-form-label">每日前几次免费</label>
                        <div class="am-u-sm-3 am-u-end">
                            <input type="text" v-model="list.free_chance" @input="list.free_chance = list.free_chance.toString().replace(/\D/g,'')">
                            <small>设置为 0 则不免费 <span class="warning-text">注：此功能和观看广告免费抽奖相互独立</span></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">观看广告免费抽奖</label>
                        <div class="am-u-sm-2">
                            <div class="radio-group">
                                <label class="radio-item">
                                    <input type="radio" v-model='list.free_ad_valve' value="0">
                                    <span>关闭</span>
                                </label>
                                <label class="radio-item">
                                    <input type="radio" v-model='list.free_ad_valve' value="1">
                                    <span>开启</span>
                                </label>
                            </div>
                            <small>开启后用户可通过观看激励式广告进行免费抽奖</small>
                        </div>
                        <label class="am-u-sm-2 am-form-label">每日抽奖上限次数</label>
                        <div class="am-u-sm-3 am-u-end">
                            <input type="text" v-model="list.draw_restrictions" @input="list.draw_restrictions = list.draw_restrictions.toString().replace(/\D/g,'')">
                            <small>设置为 0 则无限制</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">活动状态</label>
                        <div class="am-u-sm-2 am-u-end">
                            <select v-model="list.status">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>抽奖活动的开启与关闭</small>
                        </div>
                    </div>

                    <template v-for="(item,index) in list.prize_content">
                        <div class="prize-card" :style="{'margin-top':(index==0?'40px':'20px')}">
                            <div class="prize-card-header">
                                <label class="am-u-sm-2 am-form-label">奖品-{{index|markPlus}}</label>
                                <div class="am-u-sm-2">
                                    <select v-model="item.choose" @change="changeChoose(index);">
                                        <option value="0">未中奖</option>
                                        <option value="1">实物奖励</option>
                                        <option value="2">{{touch.confer}}奖励</option>
                                        <option value="3">{{touch.currency}}奖励</option>
                                        <option value="4">经验值奖励</option>
                                        <option value="5">荣誉点奖励</option>
                                    </select>
                                </div>
                                <label class="am-u-sm-2 am-form-label">奖品图片</label>
                                <div class="am-u-sm-1">
                                    <img :src="item.prize_img" @click="changePicture(index,1);" class="prize-image">
                                </div>
                                <label class="am-u-sm-2 am-form-label">奖品名称</label>
                                <div class="am-u-sm-3">
                                    <input type="text" v-model="item.prize_name">
                                </div>
                            </div>
                            <div class="prize-card-body">
                                <label class="am-u-sm-2 am-form-label">权重 ( 数值越大越容易中奖 )</label>
                                <div class="am-u-sm-2">
                                    <input type="number" v-model="item.odds" @input="scoreInput(index,0);">
                                </div>
                                <template v-if="item.choose > 0">
                                    <label class="am-u-sm-2 am-form-label">剩余数量</label>
                                    <div class="am-u-sm-2">
                                        <input type="text" v-model="item.reserve" @input="scoreInput(index,1);">
                                    </div>
                                </template>
                                <template v-if="item.choose > 1">
                                    <label class="am-u-sm-2 am-form-label">奖励分数</label>
                                    <div class="am-u-sm-2">
                                        <input type="text" v-model="item.score" @input="scoreInput(index,2);">
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">抽奖规则说明</label>
                        <div class="am-u-sm-6 am-u-end">
                            <div class="editor-container">
                                <div id="detail" style="min-height:680px;"></div>
                            </div>
                            <span id="customizeGallery" style="display:none;" @click="cuonice();"></span>
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;border-bottom: none;">
                        <button type="button" class="save-btn" @click="holdSave();">
                            保存配置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script src="static/datetime/laydate.js"></script>
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js"></script>
<script>

    var formatDate = function (outDate) {
        var date = new Date(outDate);
        let Y = date.getFullYear() + "-";
        let M = ((date.getMonth() + 1) < 10 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1)) + "-";
        let D = ((date.getDate() + 1) < 10 ? '0' + (date.getDate() + 1) : (date.getDate() + 1)) + " ";
        let h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
        let m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
        let s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
        return Y + M + D + h + m + s;
    }

    var vm = new Vue({
        el: '#app',
        data: {
            touch: [],
            list: [],
            E: [],
            editor: [],
        },
        created: function () {
            $.ajax({
                type: 'post',
                async: false,
                url: "{:url('unlawful/lottery')}",
                data: {'getData': true},
                dataType: 'json',
                success: data => {
                    this.touch = data[0];
                    data[1].prize_content = eval('(' + data[1].prize_content + ')');
                    data[1].start_time = formatDate(data[1].start_time * 1000);
                    data[1].end_time = formatDate(data[1].end_time * 1000);
                    this.list = data[1];
                }
            });
        }, mounted: function () {
            laydate.render({
                elem: '#dateStartTime'
                ,type: 'datetime'
            });
            laydate.render({
                elem: '#dateEndTime'
                ,type: 'datetime'
            });
            this.E = window.wangEditor;
            this.editor = new this.E('#detail');
            this.editor.customConfig.uploadImgServer = true;
            this.editor.create();
            this.E.fullscreen.init('#detail');
            this.editor.txt.html(this.list.illustrate);
        },
        methods: {
            changeChoose: function (index) {
                if (this.list.prize_content[index].choose < 2) {
                    this.list.prize_content[index].score = 0;
                }
                this.list.prize_content[index].reserve = 0;
            },
            changePicture: function (index, openPicture, picturePath) {
                switch (openPicture) {
                    case 0:
                        this.list.prize_content[index].prize_img = picturePath;
                        break;
                    case 1:
                        layer.open({
                            type: 2,
                            anim: 2,
                            scrollbar: true,
                            area: ['900px', '600px'],
                            title: false,
                            closeBtn: 0,
                            shadeClose: true,
                            content: ["{:url('images/dialogimages')}&gclasid=0&pictureIndex=" + index, 'no']
                        });
                        break;
                }
            },
            scoreInput: function (index,event) {
                switch (event) {
                    case 0:
                        this.list.prize_content[index].odds = Number(this.list.prize_content[index].odds.toString().match(/^\d*(\.?\d{0,2})/g)[0] || 0);
                        break;
                    case 1:
                        this.list.prize_content[index].reserve = this.list.prize_content[index].reserve.toString().replace(/\D/g, '');
                        break;
                    case 2:
                        this.list.prize_content[index].score = this.list.prize_content[index].score.toString().replace(/\D/g, '');
                        break;
                }
            },
            holdSave: function () {
                this.list.start_time = new Date(this.$refs.dateStartTime.value.substring(0, 19).replace(/-/g, '/')).getTime() / 1000;
                this.list.end_time = new Date(this.$refs.dateEndTime.value.substring(0, 19).replace(/-/g, '/')).getTime() / 1000;
                this.list.illustrate = this.editor.txt.html();
                $.post("{:url('unlawful/lottery')}", this.list, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
            }, cuonice: function () {
                layer.open({
                    type: 2,
                    anim: 2,
                    scrollbar: true,
                    area: ['900px', '600px'],
                    title: false,
                    closeBtn: 0,
                    shadeClose: true,
                    content: ["{:url('images/dialogImages')}&gclasid=0&pictureIndex=-1", 'no']
                });
            }
        },
        filters: {
            markPlus: function (value) {
                return parseInt(value) + 1;
            }
        }
    });

    var sutake = function (eurl, pictureIndex, type) {
        switch (type) {
            case 0:
                vm.changePicture(pictureIndex, 0, eurl);
                break;
            case 1:
                vm.editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
                break;
        }
        layer.closeAll();
    }

</script>
{/block}