<?php


namespace app\api\controller;


use app\api\service\RankingUtil;
use app\api\service\TmplService;
use app\common\FluxibleInfo;
use app\common\MagicTrick;
use app\common\Pisces;
use app\common\Suspense;
use think\Cache;
use think\Db;
use think\Request;

class Ranking extends Base
{
    /**
     * 广场内获取排行榜信息
     */
    public function get_ph_list()
    {
        $data = input('param.');
        $list = Db::name('user_leaderboard')
            ->where('status', 1)
            ->where('much_id', $data['much_id'])
            ->order('scores')
            ->field('id,ranking_name,status,scores,much_id')
            ->select();
        $domain = explode(':', $_SERVER['HTTP_HOST']);
        $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
        $absRess = "https://{$domain[0]}{$absAddress[0]}";
        foreach ($list as $k => $v) {
            $list[$k]['bg_img'] = $absRess . 'static/mineIcon/rank/ph' . $k . '.png';
        }

        return $this->json_rewrite(['status' => 'yes', 'list' => $list]);
    }

    /**
     * 获取排行详情
     */
    public function get_ph_info()
    {
        $util = new RankingUtil();
        $data = input('param.');
        $list = Db::name('user_leaderboard')
            ->where('id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->field('id,ranking_name,status,scores,much_id,wont_sort')
            ->find();
        $list['wont_sort'] = json_decode($list['wont_sort'], true);
        $keys = $util->calculation($list['wont_sort'], $data['much_id'], $data['type']);
        return $this->json_rewrite(['list' => $keys, 'info' => $list['ranking_name'], 'name' => $list]);
        //return $this->json_rewrite(['status' => 'yes', 'list' => $list['wont_sort']]);
    }

    /**
     * 获取认证信息列表
     */
    public function get_rz_list()
    {
        $data = input('param.');
        $list = Db::name('attest')
            ->where('much_id', $data['much_id'])
            ->where('status', 1)
            ->where('is_del', 0)
            ->field('id,at_name,at_icon,handsel_day,status,is_del,much_id,is_del,scores')
            ->order('scores')
            ->select();
        foreach ($list as $k => $v) {
            $check = Db::name('user_attest')
                ->where('at_id', $v['id'])
                ->where('user_id', $this->user_info['id'])
                ->where('much_id', $data['much_id'])
                ->order('refer_time desc')
                ->field('adopt_status,ut_inject,refer_time,refuse_time')
                ->find();
            $count = Db::name('user_attest')->where('at_id', $v['id'])->where('adopt_status', 1)->count();
            $list[$k]['count'] = $count;
            if (empty($check)) {
                $list[$k]['refuse'] = '';
            } else {
                $check['refer_time'] = date('Y-m-d H:i:s', $check['refer_time']);
                $check['refuse_time'] = date('Y-m-d H:i:s', $check['refuse_time']);
                $list[$k]['refuse'] = $check;
            }


        }
        return $this->json_rewrite(['list' => $list]);
    }

    /**
     * 获取认证信息详情
     */
    public function get_rz_info()
    {
        $data = input('param.');
        $info = Db::name('attest')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->field('id,at_name,introduction')
            ->find();
        $check = Db::name('user_attest')->alias('u')
            ->join('attest a', 'a.id=u.at_id')
            ->where('a.is_del', 0)
            ->where('u.user_id', $this->user_info['id'])
            ->where('u.much_id', $data['much_id'])
            ->where('u.adopt_status', 'in', '0,1')
            ->order('u.refer_time desc')
            ->find();
        $info['check'] = empty($check) ? 0 : 1;
        return $this->json_rewrite(['info' => $info]);
    }

    /**
     * 获取认证信表单
     */
    public function get_rz_from()
    {
        $data = input('param.');
        $info = Db::name('attest')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->field('id,at_name,custom_form')
            ->find();
        $info['custom_form'] = json_decode($info['custom_form'], true);
        foreach ($info['custom_form'][1] as $k => $v) {
            $key = [];
            if ($v['dataType'] == 'select') {
                foreach ($v['childItems'] as $a => $b) {
                    $key[$a] = $b['value'];
                }
                $info['custom_form'][1][$k]['childItems'] = $key;
            }
            if ($v['dataType'] == 'image') {
                $info['custom_form'][1][$k]['value'] = [];
            }
        }
        return $this->json_rewrite(['info' => $info]);
    }

    /**
     * 提交认证
     */
    public function set_user_attest()
    {
        $data = input('param.');
        $info = json_decode($data['info'], true);
        $key = [];
        $check_info = Db::name('attest')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->find();
        if (empty($check_info)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '认证通道已关闭！']);
        }
        if ($check_info['status'] == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '认证通道已关闭！']);
        }
        if ($check_info['is_del'] == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '认证通道已关闭！']);
        }
        if($this->user_info['tourist']==1){
            return $this->json_rewrite(['status' => 'error', 'msg' => '请登录后再认证！']);
        }
        //查询是否提交过认证
        $check = Db::name('user_attest')->alias('u')
            ->join('attest a', 'a.id=u.at_id')
            ->where('a.is_del', 0)
            ->where('u.user_id', $this->user_info['id'])
            ->where('u.much_id', $data['much_id'])
            ->where('at_id', $data['id'])
            ->where('u.adopt_status', 'in', '0,1')
            ->order('u.refer_time desc')
            ->find();
        if (!empty($check)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '您已通过认证或认证正在审核中！']);
        }
        foreach ($info as $k => $v) {
            if ($v['dataType'] != 'submit') {
                if ($v['required'] == 'true') {
                    if (empty($v['value'])) {
                        return $this->json_rewrite(['status' => 'error', 'msg' => '请填写必填项！']);
                    }
                }
                $key[$k]['text'] = $v['text'];
                $key[$k]['dataType'] = $v['dataType'];
                $key[$k]['value'] = $v['value'];
            }
        }

        $d['user_id'] = $this->user_info['id'];
        $d['at_id'] = $data['id'];
        $d['postback_data'] = json_encode($key, JSON_UNESCAPED_UNICODE);
        $d['adopt_status'] = 0;
        $d['refer_time'] = time();
        $d['refuse_time'] = 0;
        $d['ut_inject'] = '';
        $d['much_id'] = $data['much_id'];
        $ins = Db::name('user_attest')->insertGetId($d);
        if (!$ins) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '申请失败，请稍候重试！']);
        } else {
            FluxibleInfo::SendDetectPost([
                'type' => 1,
                'attest_id' => $ins,
                'user_id' => $this->user_info['id'],
                'much_id' => $data['much_id'],
            ], $data['much_id']);
            $msg = '用户：' . $this->user_info['user_nick_name'] . '提交认证【' . $check_info['at_name'] . '】待审核！';
            Db::name('prompt_msg')->insert(['capriole' => 16, 'tyid' => 0, 'msg_time' => time(), 'type' => 0, 'retter' => $msg, 'status' => 0, 'much_id' => $data['much_id']]);
            $notices = Db::name('prompt_msg')
                ->where('status', 0)
                ->where('type', 0)
                ->where('much_id', $data['much_id'])
                ->count('*');
            cache('notices_' . $data['much_id'], $notices);
            //查询open_id
            $user_maker = Db::name('user_maker')->orderRaw('rand()')->where('much_id', $data['much_id'])->where('status', 1)->find();
            $user_mb = Db::name('user')->where('user_wechat_open_id', $user_maker['user_open_id'])->where('much_id', $data['much_id'])->where('status', 1)->find();
            if (!empty($user_mb)) {
                //模板消息
                $tmplData = [
                    'much_id' => $data['much_id'],
                    'at_id' => 'YL0099',
                    'user_id' => $user_mb['id'],
                    'page' => 'yl_welore/pages/packageC/examine/index',
                    'keyword1' => $msg,
                    'keyword2' => date('Y年m月d日 H:i:s', time())
                ];
                $tmplService = new TmplService();
                $tmplService->add_template($tmplData);
            }
            return $this->json_rewrite(['status' => 'success', 'msg' => '申请成功,请等待审核！']);
        }
    }

    /**
     * 信息配置
     */
    public function get_easy_config()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('7ccde9e6-dd67-915d-30a5-b1f63fbdd926', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        //类别
        $info = Db::name('easy_info_type')->where('much_id', $data['much_id'])->where('status', 1)->order('sort asc')->select();
        $chunk_result = array_chunk($info, 10);
        //配置
        $config = Db::name('easy_info_config')->where('much_id', $data['much_id'])->field('is_show_btn,precautions,custom_title,btn_icon,waiter_qrcode')->find();
        return $this->json_rewrite(['info' => $chunk_result, 'config' => $config]);
    }


    /**
     * 信息列表
     */
    public function get_easy_list()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('7ccde9e6-dd67-915d-30a5-b1f63fbdd926', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $where = [];
        if ($data['type'] != 0) {
            $where['merchant_type'] = ['eq', $data['type']];
        }
        if ($data['search_key'] != '') {
            $where['merchant_name|address_name'] = ['like', "%" . $data['search_key'] . "%"];
        }
        //列表
        $list = Db::name('easy_info_list')->where($where)->where('status', 1)->order('create_time desc')->where('much_id', $data['much_id'])->page($data['page'], 10)->field('id,merchant_icon_carousel,merchant_introduce,merchant_name,address_name,address_longitude,address_latitude,merchant_phone')->select();
        foreach ($list as $k => $v) {
            $list[$k]['merchant_icon_carousel'] = json_decode($v['merchant_icon_carousel']);
        }
        return $this->json_rewrite(['info' => $list]);
    }

    /**
     * 信息详情
     */
    public function get_easy_info()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('7ccde9e6-dd67-915d-30a5-b1f63fbdd926', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        //列表
        $info = Db::name('easy_info_list')->where('id', $data['id'])->find();
        $info['merchant_icon_carousel'] = json_decode($info['merchant_icon_carousel']);
        return $this->json_rewrite(['info' => $info]);
    }

    public function set_pos()
    {
        $data = input('param.');
        $code = authcode($data['token_data'], 'DECODE', 'QrcodeDate');
        $code = json_decode($code, true);
        $easy_info_shop_order = Db::name('easy_info_shop_order')
            ->where('id', $code['order'])
            ->where('user_id', $code['uid'])
            ->where('much_id', $data['much_id'])
            ->find();
        //查询店员权限
        $easy_info_shop_assistant = Db::name('easy_info_shop_assistant')
            ->where('eil_id', $easy_info_shop_order['eil_id'])
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->find();

        if (!$easy_info_shop_assistant || $easy_info_shop_assistant['status'] == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '暂无验证权限！', 'info' => '']);
        }
        if (!$easy_info_shop_order) {
            return $this->json_rewrite(['code' => 1, 'msg' => '未找到该订单！', 'info' => '']);
        }
        //查询商家
        $easy_info = Db::name('easy_info_list')
            ->where('id', $easy_info_shop_order['eil_id'])
            ->where('much_id', $data['much_id'])
            ->field('merchant_name,merchant_icon_carousel')
            ->find();
        $easy_info['merchant_icon_carousel'] = json_decode($easy_info['merchant_icon_carousel'], true);
        //查询商品
        //查询订单
        $order_info = Db::name('shop_order')
            ->where('id', $easy_info_shop_order['so_id'])
            ->where('much_id', $data['much_id'])
            ->field('product_name')
            ->find();
        $order_info['vested_attribute'] = json_decode($order_info['vested_attribute'], true);
        $info['easy_info'] = $easy_info;
        $info['order_info'] = $order_info;
        $info['verify'] = '';
        if ($easy_info_shop_order['use_status'] == 1) {
            //查询验证时间
            $verify = Db::name('easy_info_shop_order_verify_log')
                ->where('eiso_id', $easy_info_shop_order['id'])
                ->find();
            $verify['verify_time'] = date('Y年m月d日 H:i:s', $verify['verify_time']);
            $info['verify'] = $verify['verify_time'];
            return $this->json_rewrite(['code' => 1, 'msg' => '此二维码已被验证！', 'info' => $info]);
        }
        if ($easy_info_shop_order['order_status'] == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '此订单已被取消！', 'info' => $info]);
        }

        // 启动事务
        Db::startTrans();
        try {
            //修改订单状态
            $e_order = Db::name('easy_info_shop_order')
                ->where('id', $easy_info_shop_order['id'])
                ->update(['use_status' => 1]);
            if (!$e_order) {
                return $this->json_rewrite(['code' => 1, 'msg' => '验证失败，请稍后重试:1！', 'info' => $info]);
            }
            //修改订单状态
            $s_order = Db::name('shop_order')
                ->where('id', $easy_info_shop_order['so_id'])
                ->update(['status' => 4]);
            if (!$s_order) {
                return $this->json_rewrite(['code' => 1, 'msg' => '验证失败，请稍后重试:2！', 'info' => $info]);
            }
            //订单核验表
            $item['eiso_id'] = $easy_info_shop_order['id'];
            $item['user_id'] = $this->user_info['id'];
            $item['eil_id'] = $easy_info_shop_order['eil_id'];
            $item['so_id'] = $easy_info_shop_order['so_id'];
            $item['verify_time'] = time();
            $item['much_id'] = $data['much_id'];
            //修改订单状态
            $res = Db::name('easy_info_shop_order_verify_log')->insertGetId($item);
            if (!$res) {
                return $this->json_rewrite(['code' => 1, 'msg' => '验证失败，请稍后重试:3！', 'info' => $info]);
            }
            // 提交事务
            Db::commit();
            return $this->json_rewrite(['code' => 0, 'msg' => '验证成功！', 'info' => $info]);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['code' => 1, 'msg' => '验证失败，请稍后重试:4！' . $e->getMessage(), 'info' => $info]);
        }

    }
}