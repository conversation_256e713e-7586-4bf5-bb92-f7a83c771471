{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.resth button{margin-right:5px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-cc-amex"></span> 充值明细
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}"
                           placeholder="搜索订单号...">
                </div>
            </div>
        </div>

    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div style="display: flex;justify-content: space-between;">
                    <div class="am-btn-toolbar" style="margin: 0 0 10px 20px;">
                        <div class="am-btn-group am-btn-group-xs resth">
                            <a href="{:url('user/water')}&egon=0"
                               class="cust-btn cust-btn-one {if $egon==0}cust-btn-activate{/if}">全部</a>
                            <a href="{:url('user/water')}&egon=1" class="cust-btn {if $egon==1}cust-btn-activate{/if}">未支付</a>
                            <a href="{:url('user/water')}&egon=2" class="cust-btn {if $egon==2}cust-btn-activate{/if}">已支付</a>
                        </div>
                    </div>
                    <div style="justify-content: flex-end;cursor: pointer;">
                        <el-button type="primary" size="mini" icon="el-icon-download" onclick="exportTheFile();">
                            导出全部信息
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="18%">订单号</th>
                            <th width="18%">用户名称</th>
                            <th width="18%">订单创建时间</th>
                            <th width="18%">订单金额</th>
                            <th width="18%">实付金额</th>
                            <th width="10%">订单状态</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>{$vo.single_mark}</td>
                            <td>
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1"
                                   target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode|subtext=15}
                                </a>
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.add_time)}</td>
                            <td>{$vo.money}</td>
                            <td>{$vo.pay_money}</td>
                            <td>
                                {if $vo.status==0}
                                <span style="background: indianred; color: white;padding: 5px;border-radius: 3px;">未支付</span>
                                {else}
                                <span style="background: seagreen; color: white;padding: 5px;border-radius: 3px;">已支付</span>
                                {/if}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    new Vue({
        el: '#app'
    });

    function exportTheFile() {
        let fileName = '充值明细.csv';
        if ('{$egon}' === '0') {
            fileName = '全部' + fileName;
        }
        if ('{$egon}' === '1') {
            fileName = '未支付' + fileName;
        }
        if ('{$egon}' === '2') {
            fileName = '已支付' + fileName;
        }
        let url = "{:url('rawls/export_the_top_up_details')}&egon={$egon}";
        const a = document.createElement('a');
        a.style.display = 'none';
        a.setAttribute('target', '_blank');
        a.setAttribute('download', fileName);
        a.href = url;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    function fuzzy() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name)
            location.href = "{:url('user/water')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        else
            location.href = "{:url('user/water')}&egon={$egon}&page={$page}";
    }

</script>
{/block}