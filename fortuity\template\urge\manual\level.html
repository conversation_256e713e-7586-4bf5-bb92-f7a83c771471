{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-signal {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .action-btn {display: inline-block;padding: 6px 12px;background: #fff;border: 1px solid #ddd;color: #23b7e5;border-radius: 3px;font-size: 12px;cursor: pointer;transition: all 0.3s;text-decoration: none;}
    .action-btn:hover {border-color: #23b7e5;color: #23b7e5;background-color: #f5fafd;text-decoration: none;}
    .action-btn.btn-success {background: #5cb85c;border-color: #5cb85c;color: #fff;}
    .action-btn.btn-success:hover {background: #449d44;border-color: #449d44;color: #fff;}
    .action-btn.btn-danger {background: #d9534f;border-color: #d9534f;color: #fff;}
    .action-btn.btn-danger:hover {background: #c9302c;border-color: #c9302c;color: #fff;}
    .action-btn.btn-disabled {background: #f8f9fa;border-color: #dee2e6;color: #6c757d;cursor: not-allowed;}
    .action-btn.btn-disabled:hover {background: #f8f9fa;border-color: #dee2e6;color: #6c757d;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .level-badge {display: inline-block;padding: 4px 8px;background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);color: white;border-radius: 12px;font-size: 12px;font-weight: 600;text-shadow: 0 1px 2px rgba(0,0,0,0.3);}
    .level-icon {height: 32px;border-radius: 4px;border: 2px solid #e8e8e8;box-shadow: 0 2px 4px rgba(0,0,0,0.1);transition: all 0.3s;}
    .level-icon:hover {border-color: #23b7e5;box-shadow: 0 2px 8px rgba(35,183,229,0.3);transform: scale(1.1);}
    .experience-value {font-weight: 600;color: #3498db;font-size: 14px;}
    .honor-value {font-weight: 600;color: #e74c3c;font-size: 14px;}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-signal"></span> 等级列表
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索等级名称...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <a href="{:url('manual/newLevel')}" class="action-btn btn-success">
                            <span class="am-icon-plus"></span> 新增等级
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="16.66%">等级级别</th>
                            <th width="16.66%">等级名称</th>
                            <th width="16.66%">等级图标</th>
                            <th width="16.66%">升级所需经验值</th>
                            <th width="16.66%">升级赠送荣誉点</th>
                            <th width="16.66%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                <span class="level-badge">Lv.{$vo.level_hierarchy}</span>
                            </td>
                            <td class="am-text-middle">
                                <span title="{$vo.merit_annotate}" style="font-weight: 500;">
                                    {$vo.level_name}
                                </span>
                            </td>
                            <td class="am-text-middle">
                                <a href="{$vo.level_icon}" target="_blank" title="{$vo.level_name}">
                                    <img src="{$vo.level_icon}" class="level-icon">
                                </a>
                            </td>
                            <td class="am-text-middle">
                                <span class="experience-value">{$vo.need_experience}</span> 经验
                            </td>
                            <td class="am-text-middle">
                                <span class="honor-value">{$vo.honor_point}</span> 荣誉点
                            </td>
                            <td class="am-text-middle">
                                <a href="{:url('manual/editLevel')}&leid={$vo.id}" target="_blank" class="action-btn" style="margin-right: 5px;">
                                    <span class="am-icon-edit"></span> 编辑
                                </a>
                                {if $vo.level_hierarchy != $levelMax}
                                <button type="button" class="action-btn btn-disabled" onclick="eraseLevel(0);" title="请先删除最大等级">
                                    <span class="am-icon-trash"></span> 删除
                                </button>
                                {else}
                                <button type="button" class="action-btn btn-danger" onclick="eraseLevel(1,'{$vo.id}');">
                                    <span class="am-icon-trash"></span> 删除
                                </button>
                                {/if}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var supre = function (asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            $.post("{:url('manual/decorateSort')}", {asyId, dalue}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 800});
                    $(domId).attr('data-score', dalue);
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600});
                }
            });
        }
    }

    var eraseLevel = function (type, leid) {
        if (type == 0) {
            layer.msg('请先删除最大等级');
            return;
        }
        layer.confirm('您确定要删除当前等级吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('manual/delLevel')}", {leid: leid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 800}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600}, function () {
                        location.reload();
                    });
                }
            });
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('manual/level')}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('manual/level')}&page={$page}";
        }
    }

</script>
{/block}