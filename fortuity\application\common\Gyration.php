<?php

namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

class Gyration
{

    //  获取GET请求数据
    public static function _requestGet($url, $ssl = true, $curlOptTimeout = 30)
    {
        return self::networkRequest($url, [], $ssl, false, $curlOptTimeout);
    }

    //  获取POST请求数据
    public static function _requestPost($url, $data, $ssl = true, $curlOptTimeout = 30)
    {
        return self::networkRequest($url, $data, $ssl, true, $curlOptTimeout);
    }

    //  发送网络请求
    private static function networkRequest($url, $data, $ssl, $director, $curlOptTimeout)
    {
        //  curl完成
        $curl = curl_init();
        //  设置curl选项
        curl_setopt($curl, CURLOPT_URL, $url); //URL
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:38.0) Gecko/20100101 Firefox/38.0 FirePHP/0.7.4';
        //  user_agent，请求代理信息
        curl_setopt($curl, CURLOPT_USERAGENT, $user_agent);
        //  referer头，请求来源
        curl_setopt($curl, CURLOPT_AUTOREFERER, true);
        //  设置超时时间
        curl_setopt($curl, CURLOPT_TIMEOUT, $curlOptTimeout);
        //  使用IPv4地址
        curl_setopt($curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
        //  忽略SSL相关选项
        if ($ssl) {
            //禁用后cURL将终止从服务端进行验证
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            //检查服务器SSL证书中是否存在一个公用名(common name)。
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 2);
        }
        //  处理post相关选项
        if ($director) {
            // 是否为POST请求
            curl_setopt($curl, CURLOPT_POST, true);
            // 处理请求数据
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        //  curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);//是否抓取跳转后的页面
        //  是否处理响应头
        curl_setopt($curl, CURLOPT_HEADER, false);
        //  处理响应结果
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        //  curl_exec()   是否返回响应结果
        //  发出请求
        $response = curl_exec($curl);
        if (false === $response) {
            return false;
        }
        curl_close($curl);
        return $response;
    }

    //  获取资源状态码
    public static function _requestCode($url, $ssl = true, $curlOptTimeout = 30)
    {
        return self::networkHttpCode($url, $ssl, $curlOptTimeout);
    }

    //  网络请求资源状态码
    private static function networkHttpCode($url, $ssl, $curlOptTimeout)
    {
        $curl = curl_init();
        //  设置URL
        curl_setopt($curl, CURLOPT_URL, $url);
        //  user_agent
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:38.0) Gecko/20100101 Firefox/38.0 FirePHP/0.7.4';
        //  请求代理信息
        curl_setopt($curl, CURLOPT_USERAGENT, $user_agent);
        //  设置超时时间
        curl_setopt($curl, CURLOPT_TIMEOUT, $curlOptTimeout);
        //  忽略SSL相关选项
        if ($ssl) {
            //  禁用后cURL将终止从服务端进行验证
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            //  检查服务器SSL证书中是否存在一个公用名(common name)
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 2);
        }
        //  获取Header
        curl_setopt($curl, CURLOPT_HEADER, 1);
        //  不需要body
        curl_setopt($curl, CURLOPT_NOBODY, true);
        //  获取的信息以文件流的形式返回，而不是直接输出
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        //  开始执行
        curl_exec($curl);
        //  获取状态码
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        //  关闭请求
        curl_close($curl);
        //  返回数据
        return $httpCode;
    }
}
