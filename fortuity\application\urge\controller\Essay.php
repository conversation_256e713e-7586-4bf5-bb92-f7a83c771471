<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\Alternative;
use app\api\service\TmplService;
use app\api\service\UserService;
use app\api\service\Util;
use app\common\Playful;
use app\common\Remotely;
use think\Cache;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\Url;
use think\View;

#帖子管理
class Essay extends Base
{

    //  帖子列表
    public function index()
    {
        $url = $this->defaultQuery();
        $hazy_name = emoji_encode(request()->get('hazy_name', ''));
        $hazy_egon = request()->get('egon', 0);
        $where = [];
        switch ($hazy_egon) {
            case 0:
                $where['pa.whether_delete'] = 0;
                break;
            case 1:
                $where['pa.study_status'] = 0;
                $where['pa.whether_delete'] = 0;
                break;
            case 2:
                $where['pa.study_status'] = 1;
                $where['pa.whether_delete'] = 0;
                break;
            case 3:
                $where['pa.study_status'] = 2;
                $where['pa.whether_delete'] = 0;
                break;
            case 4:
                $where['pa.whether_delete'] = 1;
                break;
        }
        $tgid = intval(request()->get('tgid', 0));
        if ($tgid !== 0) {
            $where['pa.tg_id'] = $tgid;
        }

        $list = Db::name('paper')
            ->alias('pa')
            ->join('territory tory', 'pa.tory_id=tory.id')
            ->join('user us', 'pa.user_id=us.id')
            ->where('pa.study_title|pa.study_content|us.user_nick_name|tory.realm_name|us.user_wechat_open_id', 'like', "%{$hazy_name}%")
            ->where($where)
            ->where('pa.much_id', $this->much_id)
            ->field('us.user_nick_name,us.user_wechat_open_id,us.uvirtual,tory.realm_name,pa.*')
            ->orderRaw('pa.study_status <> 0 asc,case when pa.study_status = 0 then pa.id end asc,case when pa.study_status <> 0 then pa.id end desc')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'tgid' => $tgid, 'hazy_name' => $hazy_name]])
            ->each(function ($item, $key) {
                $item['welfare'] = Db::name('paper_red_packet')->where('paper_id', $item['id'])->where('much_id', $this->much_id)->find();
                return $item;
            });
        $expressionHtml = function ($val) {
            return Alternative::ExpressionHtml($val);
        };
        $this->assign('expressionHtml', $expressionHtml);
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        if ($hazy_name) {
            $this->assign('hazy_name', emoji_decode($hazy_name));
        }
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }


    //  帖子详细信息
    public function setails()
    {
        if (request()->isPost() && request()->isAjax()) {
            $uplid = request()->post('uplid');
            $pical = request()->post('pical');
            if ($pical == 1 || $pical == 2) {
                $perInfo = Db::name('paper')->where('id', $uplid)->where('much_id', $this->much_id)->find();
                if (!$perInfo) {
                    return json(['code' => 0, 'msg' => '帖子不存在']);
                } else {
                    if ($perInfo['study_status'] != 0) {
                        return json(['code' => 0, 'msg' => '非法操作']);
                    }
                }
            }
            //  通过审核
            if ($pical == 1) {
                Db::startTrans();
                try {
                    Db::name('paper')->where('id', $uplid)->where('much_id', $this->much_id)->update(['prove_time' => time(), 'study_status' => 1]);
                    //    站内信
                    $toryTion = Db::name('paper')->where('id', $uplid)->where('much_id', $this->much_id)->find();
                    $maringText = '您的发帖' . subtext(strip_tags($toryTion['study_title']), 10) . '已通过审核！';
                    Db::name('user_smail')->insert(['user_id' => $toryTion['user_id'], 'maring' => $maringText, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                    $user = new UserService();
                    $user->examine($uplid, $this->much_id);
                    $tmplData = [
                        'much_id' => $this->much_id,
                        'at_id' => 'YL0009',
                        'user_id' => $toryTion['user_id'],
                        'page' => 'yl_welore/pages/user_smail/index',
                        'keyword1' => $maringText,
                        'keyword2' => date('Y年m月d日 H:i:s', time())
                    ];
                    $tmplService = new TmplService();
                    $tmplService->add_template($tmplData);
                    //    新人营销
                    $util = new Util();
                    $util->new_user_task([
                        'tory_id' => $toryTion['tory_id'],
                        'paper_id' => $uplid,
                        'uid' => $toryTion['user_id'],
                        'key' => 1, //    1.发帖 2.回帖
                        'much_id' => $this->much_id
                    ]);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => '操作失败，' . $e->getMessage()]);
                }
                Cache::clear("globalIndexCache_{$this->much_id}");
                return json(['code' => 1, 'msg' => '操作成功']);
            } else {
                $reason = request()->post('reason');
                if ($pical == 2) {
                    Db::startTrans();
                    try {
                        Db::name('paper')->where('id', $uplid)->where('much_id', $this->much_id)->update(['prove_time' => time(), 'study_status' => 2, 'reject_reason' => $reason]);
                        $toryTion = Db::name('paper')->where('id', $uplid)->where('much_id', $this->much_id)->find();
                        Db::name('user_smail')->insert(['user_id' => $toryTion['user_id'], 'maring' => "您的发帖" . subtext($toryTion['study_title'], 10) . '已被打回！', 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                        Db::commit();
                    } catch (\Exception $e) {
                        Db::rollback();
                        return json(['code' => 0, 'msg' => '操作失败，' . $e->getMessage()]);
                    }
                    Cache::clear("globalIndexCache_{$this->much_id}");
                    return json(['code' => 1, 'msg' => '操作成功']);
                } elseif ($pical == 3) {
                    Db::startTrans();
                    try {
                        Db::name('paper')->where('id', $uplid)->where('much_id', $this->much_id)->update(['prove_time' => time(), 'study_status' => 1, 'whether_type' => 1, 'whether_reason' => $reason, 'whether_delete' => 1, 'whetd_time' => time(), 'token' => md5(time())]);
                        $toryTion = Db::name('paper')->where('id', $uplid)->where('much_id', $this->much_id)->find();
                        Db::name('user_smail')->insert(['user_id' => $toryTion['user_id'], 'maring' => "您的发帖" . subtext($toryTion['study_title'], 10) . "已被系统管理员删除，删除原因：{$reason}！", 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                        Db::commit();
                    } catch (\Exception $e) {
                        Db::rollback();
                        return json(['code' => 0, 'msg' => '删帖失败，' . $e->getMessage()]);
                    }
                    Cache::clear("globalIndexCache_{$this->much_id}");
                    return json(['code' => 1, 'msg' => '删帖成功']);
                } else {
                    return json(['code' => 0, 'msg' => '非法操作']);
                }
            }
        }

        $uplid = request()->get('uplid', '');
        $token = request()->get('token', '');
        if ($uplid) {
            if ($token) {
                $kind = 'pa.token';
                $wed = $token;
            } else {
                $kind = 'pa.whether_delete';
                $wed = 0;
            }
            $elementList = Db::name('paper')
                ->alias('pa')
                ->join('territory tory', 'pa.tory_id=tory.id', 'left')
                ->join('user us', 'pa.user_id=us.id', 'left')
                ->where('pa.id', $uplid)
                ->where('pa.much_id', $this->much_id)
                ->where($kind, $wed)
                ->field('pa.*,us.user_nick_name,us.user_wechat_open_id,us.uvirtual,tory.realm_name')
                ->find();
            if ($elementList) {
                //  图片
                $elementList['image_part'] = json_decode($elementList['image_part'], true);
                //  如果是付费帖子
                if ($elementList['is_buy'] > 0) {
                    //  购买帖子人数
                    $elementList['user_buy_count'] = Db::name('paper_buy_user')->where('paper_id', $elementList['id'])->where('much_id', $this->much_id)->count();
                    //  共获收益贝壳或积分 税前
                    $elementList['fraction_buy_count'] = Db::name('paper_buy_user')->where('paper_id', $elementList['id'])->where('much_id', $this->much_id)->sum('buy_price');
                    $userBuyPerPicTaxCount = Db::name('paper_buy_user')->where('paper_id', $elementList['id'])->where('much_id', $this->much_id)->field('sum(buy_price * (1 - buy_taxing)) as deduction')->find();
                    //  共获收益 贝壳/积分 税后
                    $elementList['fraction_buy_tax_count'] = number_format($userBuyPerPicTaxCount['deduction'], 2);
                }
                if ($elementList['study_type'] == 3) {
                    //  活动信息
                    $briskTeamInfo = Db::name('brisk_team')->where('paper_id', $elementList['id'])->where('much_id', $this->much_id)->find();
                    //  活动已报名人数
                    $briskTeamInfo['userBriskTeamCount'] = Db::name('user_brisk_team')->where('paper_id', $elementList['id'])->where('brisk_id', $briskTeamInfo['id'])->where('much_id', $this->much_id)->count();
                    //  活动已验证人数
                    $briskTeamInfo['userBriskTeamWriteOffCount'] = Db::name('user_brisk_team')->where('paper_id', $elementList['id'])->where('brisk_id', $briskTeamInfo['id'])->where('is_write_off', '>', 0)->where('much_id', $this->much_id)->count();
                    $this->assign('briskTeamInfo', $briskTeamInfo);
                }
                //  是否是福利帖子
                $elementList['welfare'] = Db::name('paper_red_packet')->where('paper_id', $elementList['id'])->where('much_id', $this->much_id)->find();
                $defaultNavigate = $this->defaultNavigate();
                $this->assign('defaultNavigate', $defaultNavigate);

                $preg = '/<embed.*?src=[\"|\']?(.*?)[\"|\']?\s.*?><\/embed>/i';
                preg_match_all($preg, $elementList['study_content'], $videoUrl);
                for ($i = 0; $i < count($videoUrl[1]); $i++) {
                    $videoHtml = "<video src='{$videoUrl[1][$i]}' controls='controls' style='width:98%;height:350px;'></video>";
                    $elementList['study_content'] = preg_replace($preg, $videoHtml, $elementList['study_content'], 1);
                }
                //    style替换-1
                $elementList['study_content'] = preg_replace('/style=[\"].*?[\"]/i', '', $elementList['study_content']);
                //    style替换-2
                $elementList['study_content'] = preg_replace('/style=[\'].*?[\']/i', '', $elementList['study_content']);
                //    xss过滤
                $elementList['study_content'] = $this->safe_html($elementList['study_content']);
                /*
                $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
                preg_match_all($preg, $elementList['study_content'], $match);
                */
                //  替换所有的a标签 #
                $preg = '/href=(\"|\')(.*?)(\"|\')/i';
                preg_match_all($preg, $elementList['study_content'], $aLabelList);
                for ($i = 0; $i < count($aLabelList[2]); $i++) {
                    $elementList['study_content'] = str_replace($aLabelList[0][$i], 'href="#"', $elementList['study_content']);
                }
                //  拨打电话
                $callPhonePluginKey = Remotely::isUnLockProperty(base64_decode('5LiA6ZSu5ouo5Y+3'));
                $this->assign('callPhonePluginKey', $callPhonePluginKey);
                //  网盘存储
                $netDiscPluginKey = Remotely::isUnLockProperty(base64_decode('572R55uY5YiX6KGo'));
                $this->assign('netDiscPluginKey', $netDiscPluginKey);
                if ($elementList['is_buy'] > 1 && $netDiscPluginKey) {
                    $ncsInfo = Db::name('netdisc_sell')->where('pa_id', $elementList['id'])->where('much_id', $this->much_id)->find();
                    $elementList['buyFilesInfo'] = Db::name('netdisc_belong')->where('id', $ncsInfo['nb_id'])->where('much_id', $this->much_id)->find();
                    $elementList['buyFilesInfo']['ncInfo'] = Db::name('netdisc')->where('id', $ncsInfo['nc_id'])->where('much_id', $this->much_id)->find();
                }
                //  内容点评
                $correctPluginKey = Remotely::isUnLockProperty(base64_decode('5YaF5a6554K56K+E'));
                $this->assign('correctPluginKey', $correctPluginKey);
                if ($correctPluginKey) {
                    $prsList = Db::name('paper_review_score')->where('pa_id', $elementList['id'])->where('much_id', $this->much_id)->select();
                    for ($i = 0; $i < count($prsList); $i++) {
                        $userInfo = Db::name('user')->where('id', $prsList[$i]['user_id'])->where('much_id', $this->much_id)->field('user_head_sculpture as user_head_img,user_nick_name as user_name,user_wechat_open_id as user_openid,uvirtual')->find();
                        $prsList[$i] = array_merge($prsList[$i], $userInfo);
                    }
                    $this->assign('prsList', $prsList);
                }
                //  视频解析
                $videoParsePluginKey = Remotely::isUnLockProperty(base64_decode('6KeG6aKR6Kej5p6Q'));
                $this->assign('videoParsePluginKey', $videoParsePluginKey);

                //  处理表情
                $elementList['study_content'] = Alternative::ExpressionHtml($elementList['study_content']);

                $this->assign('list', $elementList);
                $toryList = Db::name('territory')->where('status', 1)->where('is_del', 0)->where('much_id', $this->much_id)->order('scores', 'asc')->select();
                $this->assign('toryList', $toryList);
                $gambitInfo = Db::name('gambit')->where('id', $elementList['tg_id'])->where('is_del', 0)->where('much_id', $this->much_id)->order('scores')->find();
                $this->assign('gambitInfo', $gambitInfo);


                if (intval($elementList['study_type']) === 4 || intval($elementList['study_type']) === 5) {
                    $pvInfo = Db::name('paper_vote')->where('paper_id', $elementList['id'])->where('much_id', $this->much_id)->select();
                    $pvPeopleCount = Db::name('user_vote')->where('paper_id', $elementList['id'])->where('much_id', $this->much_id)->count();
                    for ($i = 0; $i < count($pvInfo); $i++) {
                        $voters = Db::name('user_vote')->where('paper_id', $elementList['id'])->where('pv_id', $pvInfo[$i]['id'])->where('much_id', $this->much_id)->count();
                        $pvInfo[$i]['voters'] = $voters;
                        $ratio = (($pvInfo[$i]['voters'] / $pvPeopleCount) * 100);
                        if (is_nan($ratio)) {
                            $pvInfo[$i]['ratio'] = "style=\"width:0%;\"";
                        } else {
                            $pvInfo[$i]['ratio'] = "style=\"width:{$ratio}%;\"";
                        }
                    }
                    $this->assign('pvInfo', $pvInfo);
                }

                if (intval($elementList['study_type']) === 6) {
                    $paperWechatChannelVideoInfo = Db::name('paper_wechat_channel_video')->where('paper_id', $elementList['id'])->where('much_id', $this->much_id)->find();
                    $this->assign('paperWechatChannelVideoInfo', $paperWechatChannelVideoInfo);
                }

                return $this->fetch();
            } else {
                $url = Url::build('essay/index');
                return "<script>alert('很抱歉，您当前所查看的 帖子不存在 或 已被删除 ！');location.href='{$url}';</script>";
            }
        } else {
            $this->redirect('index');
        }
    }

    //  恢复帖子
    public function restper()
    {
        if (request()->isPost() && request()->isAjax()) {
            $prid = request()->post('prid');
            Db::startTrans();
            try {
                Db::name('paper')->where('id', $prid)->where('much_id', $this->much_id)->update(['whether_type' => 0, 'whether_reason' => null, 'whether_delete' => 0, 'whetd_time' => null, 'token' => null,]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            $redUrl = url('essay/setails');
            Cache::clear("globalIndexCache_{$this->much_id}");
            return json(['code' => 1, 'msg' => '恢复成功', 'url' => $redUrl . '&uplid=' . $prid]);
        } else {
            $this->redirect('essay/index');
        }
    }

    //  修改浏览次数
    public function updateScamperFrequency()
    {
        if (request()->isPost() && request()->isAjax()) {
            $paid = request()->post('paid');
            $type = intval(request()->post('type'));
            $oUnit = intval(request()->post('oUnit'));
            $dbName = '';
            $updateData = [];
            if ($oUnit === 0) {
                $dbName = 'paper';
                if ($type == 0) {
                    $newStudyHeat = request()->post('newStudyHeat');
                    $updateData['study_heat'] = $newStudyHeat;
                } elseif ($type == 1) {
                    $newStudyLaud = request()->post('newStudyLaud');
                    $updateData['study_laud'] = $newStudyLaud;
                }
            }
            if ($oUnit === 1) {
                $dbName = 'paper_reply';
                $newStudyPraise = request()->post('newStudyPraise');
                $updateData['praise'] = $newStudyPraise;
            }
            Db::startTrans();
            try {
                Db::name($dbName)->where('id', $paid)->where('much_id', $this->much_id)->update($updateData);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $this->error('参数错误', 'essay/index');
        }
    }

    //  编辑帖子
    public function editWritings()
    {
        //  拨打电话
        $callPhonePluginKey = Remotely::isUnLockProperty(base64_decode('5LiA6ZSu5ouo5Y+3'));
        //  网络云盘
        $netDiscPluginKey = Remotely::isUnLockProperty(base64_decode('572R55uY5YiX6KGo'));
        //  视频解析
        $videoParsePluginKey = Remotely::isUnLockProperty(base64_decode('6KeG6aKR6Kej5p6Q'));
        if (request()->isPost() && request()->isAjax()) {
            $many = request()->post();
            $rectify = $many['postData'];
            $data['tory_id'] = $rectify['toryid'];
            $data['study_title'] = emoji_encode($rectify['title']);
            if (trim($rectify['titleColor']) === '') {
                $rectify['titleColor'] = '#000000';
            }
            $data['study_title_color'] = $rectify['titleColor'];
            $data['is_buy'] = intval($rectify['isBuy']);
            if ($data['is_buy'] > 1 && $netDiscPluginKey) {
                if (intval($rectify['fileId']) === 0) {
                    return json(['code' => 0, 'msg' => '请选择付费文件！']);
                }
            }
            $data['buy_price_type'] = intval($rectify['buyPriceType']);
            if ($data['is_buy']) {
                $data['buy_price'] = $rectify['buyPrice'];
            } else {
                $data['buy_price'] = 0.00;
            }
            if ($callPhonePluginKey) {
                $data['call_phone'] = $rectify['callPhone'];
            } else {
                $data['call_phone'] = null;
            }
            $data['img_show_type'] = intval($rectify['imgShowType']);
            $data['study_content'] = $this->safe_html(emoji_encode($rectify['content']));
            $data['study_type'] = intval($rectify['patype']);
            $data['video_type'] = $rectify['videoType'];
            $data['topping_time'] = 0;
            switch ($data['study_type']) {
                case 0:
                    //  图文帖
                    $data['video_type'] = 0;
                    $data['study_video'] = null;
                    $data['third_part_vid'] = null;
                    $data['study_voice'] = null;
                    $data['study_voice_time'] = 0;
                    if ($rectify['multipleImg']) {
                        $imagePart = json_encode($rectify['multipleImg'], 320);
                        $data['image_part'] = $imagePart != '[""]' ? $imagePart : '[]';
                    } else {
                        $data['image_part'] = '[]';
                    }
                    break;
                case 1:
                    //  语音帖
                    $data['study_voice'] = trim($rectify['voice']) != '' ? $rectify['voice'] : null;
                    $data['study_voice_time'] = $rectify['voiceTime'] != 0 ? $rectify['voiceTime'] : 1;
                    $data['video_type'] = 0;
                    $data['study_video'] = null;
                    $data['third_part_vid'] = null;
                    $data['image_part'] = '[]';
                    break;
                case 2:
                    //  视频帖
                    $data['study_voice'] = null;
                    $data['study_voice_time'] = 0;
                    $data['study_video'] = $rectify['video'];
                    switch (intval($data['video_type'])) {
                        case 0:
                            $data['study_video'] = $rectify['video'];
                            $data['third_part_vid'] = null;
                            break;
                        case 1:
                        case 2:
                            $data['study_video'] = null;
                            $data['third_part_vid'] = $rectify['tencentVideoVid'];
                            break;
                    }
                    $data['image_part'] = "[\"{$rectify['videoImg']}\"]";
                    break;
                case 3:
                case 4:
                case 5:
                    //  活动帖 投票帖
                    $data['video_type'] = 0;
                    $data['study_video'] = null;
                    $data['third_part_vid'] = null;
                    $data['study_voice'] = null;
                    $data['study_voice_time'] = 0;
                    if ($rectify['multipleImg']) {
                        $imagePart = json_encode($rectify['multipleImg'], 320);
                        $data['image_part'] = $imagePart != '[""]' ? $imagePart : '[]';
                    } else {
                        $data['image_part'] = '[]';
                    }
                    $data['vote_deadline'] = strtotime(date($rectify['voteDeadline']));
                    break;
                case 6:
                    $feedToken = trim($rectify['feedToken']);
                    break;
            }
            $adapterTime = trim($rectify['adapterTime']);
            if ($adapterTime) {
                $adapterTimeFormat = intval(floatval($rectify['adapterTime']) / 1000);
            } else {
                $adapterTimeFormat = time();
            }
            $data['adapter_time'] = $adapterTimeFormat;
            $data['prove_time'] = $adapterTimeFormat;
            Db::startTrans();
            try {
                if ($rectify['gambit'] != '') {
                    //    查询是否有此话题
                    $gambitInfo = Db::name('gambit')->where('gambit_name', $rectify['gambit'])->where('much_id', $this->much_id)->find();
                    //  如果有且已经删除则改变状态
                    if ($gambitInfo && $gambitInfo['is_del'] == 1) {
                        Db::name('gambit')->where('id', $gambitInfo['id'])->where('gambit_name', $rectify['gambit'])->where('much_id', $this->much_id)->update(['add_time' => time(), 'is_del' => 0]);
                    }
                    if (!$gambitInfo) {
                        //  没有话题则新增一个话题
                        $data['tg_id'] = Db::name('gambit')->insertGetId(['gambit_name' => $rectify['gambit'], 'add_time' => time(), 'scores' => 0, 'is_del' => 0, 'much_id' => $this->much_id]);
                    }
                    //  话题编号
                    if (!$data['tg_id']) {
                        $data['tg_id'] = $gambitInfo['id'];
                    }
                } else {
                    $data['tg_id'] = 0;
                }
                Db::name('paper')->where('id', $rectify['prid'])->where('much_id', $this->much_id)->update($data);
                if ($data['is_buy'] > 1 && $netDiscPluginKey) {
                    $netDiscSellData = ['pa_id' => $rectify['prid'], 'nc_id' => intval($rectify['ncId']), 'nb_id' => intval($rectify['fileId']), 'is_sell' => intval($rectify['fileIsSell']), 'much_id' => $this->much_id];
                    $ncsInfo = Db::name('netdisc_sell')->where('pa_id', $rectify['prid'])->where('much_id', $this->much_id)->find();
                    if (!$ncsInfo) {
                        Db::name('netdisc_sell')->where('much_id', $this->much_id)->insert($netDiscSellData);
                    } else {
                        Db::name('netdisc_sell')->where('pa_id', $rectify['prid'])->where('much_id', $this->much_id)->update($netDiscSellData);
                    }
                } else {
                    Db::name('netdisc_sell')->where('pa_id', $rectify['prid'])->where('much_id', $this->much_id)->delete();
                }
                //  是否是活动帖
                if ($data['study_type'] == 3) {
                    //  查询是否存在活动信息
                    $briskTeamInfo = Db::name('brisk_team')->where('paper_id', $rectify['prid'])->where('much_id', $this->much_id)->find();
                    //  如果有则更改 如果没有则添加
                    if ($briskTeamInfo) {
                        Db::name('brisk_team')->where('id', $briskTeamInfo['id'])->where('much_id', $this->much_id)->update([
                            'is_approve' => $rectify['briskApprove'],
                            'brisk_address' => $rectify['briskAddress'],
                            'brisk_address_latitude' => $rectify['briskAddressLatitude'],
                            'brisk_address_longitude' => $rectify['briskAddressLongitude'],
                            'start_time' => strtotime(date($rectify['dateStartTime'])),
                            'end_time' => strtotime(date($rectify['dateEndTime'])),
                            'number_of_people' => $rectify['numberOfPeople']
                        ]);
                    } else {
                        Db::name('brisk_team')->insert([
                            'paper_id' => $rectify['prid'],
                            'is_approve' => $rectify['briskApprove'],
                            'brisk_address' => $rectify['briskAddress'],
                            'brisk_address_latitude' => $rectify['briskAddressLatitude'],
                            'brisk_address_longitude' => $rectify['briskAddressLongitude'],
                            'start_time' => strtotime(date($rectify['dateStartTime'])),
                            'end_time' => strtotime(date($rectify['dateEndTime'])),
                            'number_of_people' => $rectify['numberOfPeople'],
                            'much_id' => $this->much_id
                        ]);
                    }
                } else {
                    Db::name('brisk_team')->where('paper_id', $rectify['prid'])->where('much_id', $this->much_id)->delete();
                }
                if ($data['study_type'] === 4 || $data['study_type'] === 5) {
                    for ($i = 0; $i < count($many['shocked']['pretendsKey']); $i++) {
                        Db::name('paper_vote')->where('id', $many['shocked']['pretendsKey'][$i])->where('paper_id', $rectify['prid'])->where('much_id', $this->much_id)->update(['cheat_ballot' => intval($many['shocked']['pretendsValue'][$i])]);
                    }
                }
                if ($data['study_type'] === 6) {
                    Db::name('paper_wechat_channel_video')->where('paper_id', $rectify['prid'])->update([
                        'feed_token' => $feedToken,
                        'create_time' => time(),
                        'much_id' => $this->much_id
                    ]);
                } else {
                    Db::name('paper_wechat_channel_video')->where('paper_id', $rectify['prid'])->delete();
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            Cache::clear("globalIndexCache_{$this->much_id}");
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $prid = request()->get('prid', 0);
            $perInfo = Db::name('paper')->alias('per')->join('user us', 'per.user_id=us.id')->where('per.id', $prid)->where('per.much_id', $this->much_id)->field('per.*,us.user_nick_name')->find();
            if ($perInfo) {
                if ($perInfo['is_buy'] > 1 && $netDiscPluginKey) {
                    $ncsInfo = Db::name('netdisc_sell')->where('pa_id', $perInfo['id'])->where('much_id', $this->much_id)->find();
                    $perInfo['buyFilesInfo'] = Db::name('netdisc_belong')->where('id', $ncsInfo['nb_id'])->where('much_id', $this->much_id)->find();
                    $perInfo['buyFilesInfo']['ncsInfo'] = $ncsInfo;
                    $perInfo['buyFilesInfo']['ncInfo'] = Db::name('netdisc')->where('id', $ncsInfo['nc_id'])->where('much_id', $this->much_id)->find();
                }
                $this->assign('callPhonePluginKey', $callPhonePluginKey);
                $this->assign('netDiscPluginKey', $netDiscPluginKey);
                $this->assign('videoParsePluginKey', $videoParsePluginKey);

                if ($perInfo['study_type'] == 3) {
                    $briskTeamInfo = Db::name('brisk_team')->where('paper_id', $perInfo['id'])->where('much_id', $this->much_id)->find();
                    $this->assign('briskTeamInfo', $briskTeamInfo);
                }
                if ($perInfo['study_type'] == 4 || $perInfo['study_type'] == 5) {
                    $pvInfo = Db::name('paper_vote')->where('paper_id', $perInfo['id'])->where('much_id', $this->much_id)->select();
                    $this->assign('pvInfo', $pvInfo);
                }
                if ($perInfo['study_type'] == 6) {
                    $paperWechatChannelVideoInfo = Db::name('paper_wechat_channel_video')->where('paper_id', $perInfo['id'])->where('much_id', $this->much_id)->find();
                    $this->assign('paperWechatChannelVideoInfo', $paperWechatChannelVideoInfo);
                }
                $toryInfo = Db::name('territory')->where('status', 1)->where('is_del', 0)->where('much_id', $this->much_id)->select();
                $this->assign('toryInfo', $toryInfo);
                $this->assign('list', $perInfo);
                $defaultNavigate = $this->defaultNavigate();
                $this->assign('defaultNavigate', $defaultNavigate);
                $gambitInfo = Db::name('gambit')->where('id', $perInfo['tg_id'])->where('much_id', $this->much_id)->find();
                $gambitInfo['gambit_name'] = str_replace('#', '', $gambitInfo['gambit_name']);
                $this->assign('gambitInfo', $gambitInfo);
                return $this->fetch();
            } else {
                $this->error('参数错误', 'essay/index');
            }
        }
    }

    //  活动报名信息
    public function enrollment()
    {
        $url = $this->defaultQuery();
        $prid = request()->get('prid');
        $hazy_name = request()->get('hazy_name', '');
        if ($prid) {
            $prInfo = Db::name('paper')->where('id', $prid)->where('study_type', 3)->where('much_id', $this->much_id)->find();
            if ($prInfo) {
                $list = Db::name('user_brisk_team')
                    ->alias('ubt')
                    ->join('user us', 'ubt.user_id=us.id', 'left')
                    ->where('paper_id', $prid)
                    ->where('us.user_nick_name|us.user_wechat_open_id', 'like', "%{$hazy_name}%")
                    ->where('ubt.much_id', $this->much_id)
                    ->field('ubt.*,us.user_nick_name,us.user_wechat_open_id')
                    ->order('ubt.id', 'asc')
                    ->paginate(10, false, ['query' => ['s' => $url, 'prid' => $prid, 'hazy_name' => $hazy_name]]);
                $this->assign('list', $list);
                $this->assign('hazy_name', $hazy_name);
                $page = request()->get('page', 1);
                $this->assign('prid', $prid);
                $this->assign('page', $page);
                return $this->fetch();
            } else {
                $this->redirect('essay/index');
            }
        } else {
            $this->redirect('essay/index');
        }
    }

    //  回复列表
    public function reply()
    {
        $url = $this->defaultQuery();
        $hazy_name = emoji_encode(request()->get('hazy_name', ''));
        $egon = trim(request()->get('egon', 0));
        if ($egon == '') {
            $egon = 0;
        }
        switch ($egon) {
            case 0:
                $where['rep.whether_delete'] = 0;
                $where['per.whether_delete'] = 0;
                break;
            case 1:
                $where['rep.whether_delete'] = 1;
                break;
        }
        $list = Db::name('paper_reply')
            ->alias('rep')
            ->join('paper per', 'rep.paper_id=per.id', 'left')
            ->join('user us', 'rep.user_id=us.id', 'left')
            ->where('rep.reply_content|us.user_nick_name|per.study_title', 'like', "%{$hazy_name}%")
            ->where('rep.much_id', $this->much_id)
            ->where($where)
            ->orderRaw('rep.reply_status <> 0 asc,case when rep.reply_status = 0 then rep.id end asc,case when rep.reply_status <> 0 then rep.id end desc')
            ->field('rep.*,per.study_title,per.tory_id,us.user_nick_name,us.user_wechat_open_id')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $egon, 'hazy_name' => $hazy_name]])
            ->each(function ($item) {
                $item['tory'] = Db::name('territory')->where('id', $item['tory_id'])->where('much_id', $this->much_id)->field('realm_name')->find();
                return $item;
            });

        $expressionHtml = function ($val) {
            return Alternative::ExpressionHtml($val);
        };
        $this->assign('expressionHtml', $expressionHtml);
        $this->assign('list', $list);
        $this->assign('hazy_name', emoji_decode($hazy_name));
        $this->assign('egon', $egon);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  回复详情
    public function repas()
    {
        if (request()->isPost() && request()->isAjax()) {
            $uplid = request()->post('uplid');
            $reason = request()->post('reason');
            Db::startTrans();
            try {
                Db::name('paper_reply')->where('id', $uplid)->where('much_id', $this->much_id)->update(['whether_type' => 1, 'whether_reason' => $reason, 'whether_delete' => 1, 'whetd_time' => time(), 'token' => md5(time())]);
                $toryTion = Db::name('paper_reply')->where('id', $uplid)->where('much_id', $this->much_id)->find();
                Db::name('user_smail')->insert(['user_id' => $toryTion['user_id'], 'maring' => "您的帖子回复" . subtext($toryTion['reply_content'], 10) . "已被系统管理员删除，删除原因：{$reason}！", 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '删除成功']);
        }
        $uplid = request()->get('uplid', '');
        $token = request()->get('token', '');
        if ($uplid) {
            if ($token) {
                $kind = 'rep.token';
                $wed = $token;
            } else {
                $kind = 'rep.whether_delete';
                $wed = 0;
            }
            $repList = Db::name('paper_reply')
                ->alias('rep')
                ->join('paper per', 'rep.paper_id=per.id', 'left')
                ->join('user us', 'rep.user_id=us.id', 'left')
                ->where('rep.id', $uplid)
                ->where('rep.much_id', $this->much_id)
                ->where($kind, $wed)
                ->field('rep.*,per.study_title,us.user_nick_name,us.user_wechat_open_id')
                ->find();
            if ($repList) {
                $repList['image_part'] = json_decode($repList['image_part'], true);
                $repList['reply_content'] = $this->safe_html(Alternative::ExpressionHtml($repList['reply_content']));
                $this->assign('list', $repList);
                //  回复楼中楼
                $repDux = Db::name('paper_reply_duplex')->where('reply_id', $repList['id'])->where('much_id', $this->much_id)->order('id')->select();
                foreach ($repDux as $key => $value) {
                    $userInfo = Db::name('user')->where('id', $value['user_id'])->where('much_id', $this->much_id)->find();
                    $repDux[$key]['user_head_img'] = $userInfo['user_head_sculpture'];
                    $repDux[$key]['user_name'] = $userInfo['user_nick_name'];
                    $repDux[$key]['user_openid'] = $userInfo['user_wechat_open_id'];
                    $repDux[$key]['duplex_content'] = $this->safe_html(Alternative::ExpressionHtml($repDux[$key]['duplex_content']));
                }
                $this->assign('rep_dux', $repDux);
                return $this->fetch();
            } else {
                $url = Url::build('essay/reply');
                return "<script>alert('很抱歉，您当前所查看的回复不存在或已被删除！');location.href='{$url}';</script>";
            }
        } else {
            $this->redirect('reply');
        }
    }

    //  更改回复状态
    public function adjudicationReply()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            $prInfo = Db::name('paper_reply')->alias('pr')->join('paper pa', 'pr.paper_id=pa.id')->where('pr.id', $data['reid'])->where('pr.much_id', $this->much_id)->field('pr.*,pa.tory_id,pa.study_title,pa.study_content')->find();
            if (trim($prInfo['study_title']) != '') {
                $slight = emoji_encode(subtext(emoji_decode(strip_tags($prInfo['study_title'])), 10));
            } else {
                $slight = emoji_encode(subtext(emoji_decode(strip_tags($prInfo['study_content'])), 10));
            }
            if ($data['code'] == 1) {
                $combination = "您回复的内容已通过审核！";
                $combinationPrompt = "您回复的帖子 \"{$slight}\" 回复内容已通过审核！";
            } elseif ($data['code'] == 2) {
                $combination = "您回复的内容未通过审核！";
                $combinationPrompt = "您回复的帖子 \"{$slight}\" 回复内容未通过审核，未通过原因：{$data['caption']}！";
            } else {
                return json(['code' => 0, 'msg' => 'error']);
            }
            $util = new Util();
            Db::startTrans();
            try {
                Db::name('paper_reply')->where('id', $data['reid'])->where('much_id', $this->much_id)->update(['prove_time' => time(), 'reply_status' => $data['code']]);
                Db::name('user_smail')->insert(['user_id' => $prInfo['user_id'], 'maring' => $combinationPrompt, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                if ($data['code'] == 1) {
                    $util->new_user_task([
                        'tory_id' => $prInfo['tory_id'],
                        'paper_id' => $prInfo['paper_id'],
                        'uid' => $prInfo['user_id'],
                        'key' => 2, //    1.发帖 2.回帖
                        'much_id' => $this->much_id
                    ]);
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            //    审核后积分
            $util->set_user_red([
                'uid' => $prInfo['user_id'],
                'reply_id' => $data['reid'],
                'id' => $prInfo['paper_id'],
                'much_id' => $this->much_id
            ]);
            //    模板消息
            $tmplData = [
                'much_id' => $this->much_id,
                'at_id' => 'YL0009',
                'user_id' => $prInfo['user_id'],
                'page' => 'yl_welore/pages/user_smail/index',
                'keyword1' => $combination,
                'keyword2' => date('Y年m月d日 H:i:s', time())
            ];
            $tmplService = new TmplService();
            $tmplService->add_template($tmplData);
            return json(['code' => 1, 'msg' => '成功']);
        } else {
            $this->error('参数错误', 'essay/reply');
        }
    }

    //  恢复回复帖子
    public function regainReply()
    {
        if (request()->isPost() && request()->isAjax()) {
            $repId = request()->post('repId');
            Db::startTrans();
            try {
                Db::name('paper_reply')->where('id', $repId)->where('much_id', $this->much_id)->update(['whetd_time' => null, 'whether_delete' => 0, 'whether_type' => 0, 'whether_reason' => null, 'token' => null]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '恢复成功', 'url' => url('essay/repas')]);
        } else {
            $this->error('参数错误', 'essay/reply');
        }
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    //  帖子设置
    public function ritual()
    {
        if (request()->isPost() && request()->isAjax()) {
            $uplid = request()->post('uplid');

            $data['auto_review'] = request()->post('review');
            $data['number_limit'] = request()->post('limit');

            $data['reply_auto_review'] = request()->post('replyReview');
            $data['reply_number_limit'] = request()->post('replyLimit');

            $data['discuss_auto_review'] = request()->post('discussReview');
            $data['discuss_number_limit'] = request()->post('discussLimit');

            $data['buy_paper_auto_review'] = request()->post('buyPaperReview');
            $data['buy_paper_number_limit'] = request()->post('buyPaperLimit');

            $buy_paper_taxing = request()->post('buyPaperTaxing');
            $data['buy_paper_taxing'] = $buy_paper_taxing * 0.01;

            $data['tractate_font_size'] = intval(request()->post('tractateFontSize', 14));

            $data['is_show_forum_declaration'] = intval(request()->post('isShowForumDeclaration'));
            $data['forum_declaration'] = $this->safe_html(request()->post('forumDeclaration'));

            $data['notice'] = $this->safe_html(request()->post('notice'));

            Db::startTrans();
            try {
                Db::name('paper_smingle')->where('id', $uplid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        $tuaList = Db::name('paper_smingle')->where('much_id', $this->much_id)->find();
        $this->assign('list', $tuaList);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        return $this->fetch();
    }

    //  首页帖子置顶列表
    public function home_topping()
    {
        if (request()->isPost() && request()->isAjax()) {
            $hid = request()->post('hid');
            Db::startTrans();
            try {
                Db::name('home_topping')->where('id', $hid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '取消置顶成功']);
        }
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $list = Db::name('paper')
            ->alias('per')
            ->join('home_topping hp', 'per.id=hp.paper_id')
            ->join('user us', 'per.user_id=us.id', 'left')
            ->where('per.id|per.study_title|per.study_content', 'like', "%{$hazy_name}%")
            ->where('per.much_id', $this->much_id)
            ->field('hp.id as hid,hp.top_time,hp.style_type,per.*,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
            ->order('hp.id', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $expressionHtml = function ($val) {
            return Alternative::ExpressionHtml($val);
        };
        $this->assign('expressionHtml', $expressionHtml);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  新增帖子置顶
    public function rutoping()
    {
        if (request()->isPost() && request()->isAjax()) {
            //  获取要置顶帖子的编号
            $prid = request()->post('prid');
            $illutype = request()->post('illutype');
            Db::startTrans();
            try {
                //  查询帖子是否存在且状态正常
                $paper = Db::name('paper')->where('id', $prid)->where('whether_type', 0)->where('whether_delete', 0)->where('much_id', $this->much_id)->find();
                //  判断帖子是否存在
                if ($paper) {
                    //  判断是否已经置顶
                    $hting = Db::name('home_topping')->where('paper_id', $prid)->where('much_id', $this->much_id)->find();
                    if ($hting) {
                        Db::rollback();
                        return json(['code' => 0, 'msg' => '该帖子已置顶，请勿重复置顶']);
                    } else {
                        Db::name('home_topping')->insert(['paper_id' => $prid, 'style_type' => $illutype, 'top_time' => time(), 'much_id' => $this->much_id]);
                        Db::commit();
                    }
                } else {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => '该帖子ID不存在或已被删除']);
                }
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '置顶成功']);
        } else {
            $this->redirect('essay/home_topping');
        }
    }

    //  首页置顶帖风格切换
    public function pickingStyle()
    {
        if (request()->isPost() && request()->isAjax()) {
            //  获取要置顶帖子的编号
            $hid = request()->post('hid');
            $sid = request()->post('sid');
            Db::startTrans();
            try {
                //  判断是否已经置顶
                $hting = Db::name('home_topping')->where('id', $hid)->where('much_id', $this->much_id)->find();
                if ($hting) {
                    Db::name('home_topping')->where('id', $hid)->where('much_id', $this->much_id)->update(['style_type' => $sid]);
                    Db::commit();
                } else {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => '参数错误']);
                }
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '风格样式切换成功']);
        } else {
            $this->redirect('essay/home_topping');
        }
    }

    //  付费帖购买详情
    public function buyPaperUser()
    {
        $jetty = input('param.jetty');
        $reid = input('param.reid');
        $page = input('param.page', 1);
        $list = Db::name('paper_buy_user')->alias('byr')->join('user us', 'byr.user_id=us.id', 'left')->where('byr.paper_id', $reid)->where('byr.much_id', $this->much_id)->field('byr.*,us.user_head_sculpture,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')->page($page, 6)->order('id', 'desc')->select();
        if ($jetty != 0) {
            foreach ($list as $key => $value) {
                $list[$key]['buy_time'] = date('Y-m-d H:i:s', $list[$key]['buy_time']);
            }
            return json($list);
        } else {
            $this->assign('list', $list);
            $defaultNavigate = $this->defaultNavigate();
            $this->assign('defaultNavigate', $defaultNavigate);
            return $this->fetch();
        }
    }
}