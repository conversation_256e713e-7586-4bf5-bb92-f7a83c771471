<?php
defined('IN_IA') or exit('Access Denied');

//检查某个索引是否存在
function manual_indexexists($tablename, $indexname)
{
    if (!empty($indexname)) {
        $indexs = pdo_fetchall("SHOW INDEX FROM " . $tablename, array(), '');
        if (!empty($indexs) && is_array($indexs)) {
            foreach ($indexs as $row) {
                if ($row['Key_name'] == $indexname) {
                    return true;
                }
            }
        }
    }
    return false;
}

//检查某张表是否存在
function manual_tableexists($table)
{
    if (!empty($table)) {
        $data = pdo_fetch("SHOW TABLES LIKE '{$table}'", array());
        if (!empty($data)) {
            $data = array_values($data);
            $tablename = $table;
            if (in_array($tablename, $data)) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    } else {
        return false;
    }
}

//检查某个字段是否存在
function manual_fieldexists($tablename, $fieldname)
{
    $isexists = pdo_fetch("DESCRIBE " . $tablename . " `{$fieldname}`", array());
    return !empty($isexists) ? true : false;
}

//检查某个表中的字段类型是不是符合
function manual_fieldmatch($tablename, $fieldname, $datatype = '', $length = '')
{
    $datatype = strtolower($datatype);
    $field_info = pdo_fetch("DESCRIBE " . $tablename . " `{$fieldname}`", array());
    if (empty($field_info)) {
        return false;
    }
    if (!empty($datatype)) {
        $find = strexists($field_info['Type'], '(');
        if (empty($find)) {
            $length = '';
        }
        if (!empty($length)) {
            $datatype .= ("({$length})");
        }
        return strpos($field_info['Type'], $datatype) === 0 ? true : -1;
    }
    return true;
}
