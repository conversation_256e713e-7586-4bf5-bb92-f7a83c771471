<?php

namespace app\api\controller;

use app\api\service\Util;
use think\Db;

class Tape extends Base
{
    /**
     * 获取配置
     */
    public function tape_info()
    {
        $data = input('param.');
        //获取纸条配置
        $config = Db::name('feeling_stipulate')->where('much_id', $data['much_id'])->find();
        if (empty($config)) {
            $config['throw_price_male'] = 0;
            $config['throw_price_female'] = 0;
            $config['pick_price_male'] = 0;
            $config['pick_price_female'] = 0;
            $config['pay_type'] = 0;
            $config['ten_local_key'] = '';
        }
        $config['custom_title'] =empty($config['custom_title'])?'小纸条':$config['custom_title'];
        $config['confer'] = $this->design['confer'];
        $config['currency'] = $this->design['currency'];
        $config['city'] = $this->get_position_by_ip(request()->ip(), $config['ten_local_key']);
        //获取纸条数量
        $config['men'] = Db::name('feeling')
            ->where('much_id', $data['much_id'])
            ->where('is_del', 0)
            ->where('check_status', 1)
            ->where('gender', 1)
            ->count();
        $config['women'] = Db::name('feeling')
            ->where('much_id', $data['much_id'])
            ->where('is_del', 0)
            ->where('check_status', 1)
            ->where('gender', 0)
            ->count();
        //$config['men']=1;
        //$config['women']=2;
        return $this->json_rewrite($config);
    }

    /**
     * 存入纸条
     */
    public function do_tape()
    {
        $data = input('param.');
        if (empty(str_replace(" ", '', $data['contact_person']))) {
            return $this->json_rewrite(['code' => 1, 'msg' => '联系方式不能为空']);
        }
        if ($data['con_index'] == null) {
            return $this->json_rewrite(['code' => 1, 'msg' => '请选择星座']);
        }
        if (empty(str_replace(" ", '', $data['hedge_content']))) {
            return $this->json_rewrite(['code' => 1, 'msg' => '内容不能为空']);
        }
        $util=new Util();
        $check=$util->get_check_msg($data['hedge_content'],$data['much_id'],$data['openid']);
        if($check['code']==1){
            return $this->json_rewrite($check);
        }
        $check_person=$util->get_check_msg($data['contact_person'],$data['much_id'],$data['openid']);
        if($check_person['code']==1){
            return $this->json_rewrite($check_person);
        }
        //获取纸条配置
        $config = Db::name('feeling_stipulate')->where('much_id', $data['much_id'])->find();
        $key = 1;//0 通行 1禁止
        if (empty($config)) {
            $key = 0;
            $item['check_time'] = time();
        } else {
            $item['check_time'] = $config['auto_careful'] == 1 ? time() : 0;
        }
        if ($config['throw_price_male'] == 0 && $data['gender'] == 1) {//男
            $key = 0;
        }
        if ($config['throw_price_female'] == 0 && $data['gender'] == 0) {//女
            $key = 0;
        }
        if ($config['pay_type'] == 2) {//微信支付判断支付号
            if ($key == 1) {
                $user_serial = Db::name('user_serial')
                    ->where('user_id', $this->user_info['id'])
                    ->where('single_mark', $data['number'])
                    ->where('much_id', $data['much_id'])
                    ->find();
                if (empty($user_serial) || $user_serial['status'] == 0) {
                    return $this->json_rewrite(['code' => 1, 'msg' => '订单未能成功支付，请稍候重试！']);
                }
                $f_pay = Db::name('feeling_pay')
                    ->where('much_id', $data['much_id'])
                    ->where('user_id', $this->user_info['id'])
                    ->where('order_id', $data['number'])
                    ->find();
                if (!empty($f_pay)) {
                    return $this->json_rewrite(['code' => 1, 'msg' => '订单重复，请稍候重试！']);
                }
            }

        }
        //查询是否有放入限制
        if (!empty($config) && $config['throw_limit'] > 0) {
            //查询我今日放入的
            $count = Db::name('feeling')
                ->where('user_id', $this->user_info['id'])
                ->where('much_id', $data['much_id'])
                ->whereTime('create_time', 'today')
                ->count();
            if ($count >= $config['throw_limit']) {
                return $this->json_rewrite(['code' => 1, 'msg' => '今日放入小纸条已达上限']);
            }
        }
        $item['user_id'] = $this->user_info['id'];
        $item['contact_person'] = $data['contact_person'];
        $item['age'] = $data['old_name'];
        $item['gender'] = $data['gender'];
        $item['constellation'] = $data['con_index'];
        $item['remain_city'] = $data['area_index'];
        $item['restrict_city'] = $data['c_area_index'];
        $html = '';
        $hedge_content = emoji_encode($data['hedge_content']);
        $html .= $hedge_content;
        if (!empty($data['imgList'])) {
            $string_arr = explode(",", $data['imgList']);
            foreach ($string_arr as $k => $v) {
                $html .= "<img src='{$v}' />";
            }
        }

        $item['hedge_content'] = $html;
        $item['longevity'] = $data['life_index'] == 0 ? 0 : $data['life_name'];
        $item['create_time'] = time();
        $item['check_status'] = empty($config) ? 1 : $config['auto_careful'];
        $item['check_opinion'] = '';
        $item['is_del'] = 0;
        $item['much_id'] = $data['much_id'];
        if ($data['gender'] == 1) {
            $money = abs($config['throw_price_male']);
        } else {
            $money = abs($config['throw_price_female']);
        }
        if (!empty($config) && $config['pay_type'] != 2) {
            if ($config['pay_type'] == 0) {
                //判断积分
                if (bccomp($money, $this->user_info['fraction'], 2) == 1) {
                    return $this->json_rewrite(['code' => '1', 'msg' => $this->design['confer'] . '不足！']);
                }
            } else {
                //判断贝壳
                if (bccomp($money, $this->user_info['conch'], 2) == 1) {
                    return $this->json_rewrite(['code' => '1', 'msg' => $this->design['currency'] . '不足！']);
                }
            }
        }
        // 启动事务
        Db::startTrans();
        try {
            $res = Db::name('feeling')->insertGetId($item);
            if (!$res) {
                Db::rollback();
                return $this->json_rewrite(['code' => '1', 'msg' => '放入失败，请稍候重试！1']);
            }
            if (!empty($config) && $config['pay_type'] != 2 && $key == 1) {
                //明细表增加数据
                $amount['user_id'] = $this->user_info['id'];
                $amount['category'] = 2;
                $amount['finance'] = -$money;
                if ($config['pay_type'] == 0) {
                    $amount['poem_fraction'] = $this->user_info['fraction'];
                    $amount['surplus_fraction'] = bcsub($this->user_info['fraction'], $money, 2);
                } else {
                    $amount['poem_conch'] = $this->user_info['conch'];
                    $amount['surplus_conch'] = bcsub($this->user_info['conch'], $money, 2);
                }
                $amount['ruins_time'] = time();
                $amount['solution'] = '放入小纸条';
                $amount['evaluate'] = $config['pay_type'] == 0 ? 1 : 0;
                $amount['much_id'] = $data['much_id'];
                $amount_res = Db::name('user_amount')->insert($amount);
                if (!$amount_res) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => '1', 'msg' => '放入失败，请稍候重试！2']);
                }
                if ($config['pay_type'] == 0) {
                    $user_res = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['fraction' => $amount['surplus_fraction']]);
                } else {
                    $user_res = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['conch' => $amount['surplus_conch']]);
                }
                if (!$user_res) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => '1', 'msg' => '放入失败，请稍候重试！3']);
                }
            }
            if ($config['pay_type'] == 2 && $key == 1) {//微信支付判断支付号
                $user_serial = Db::name('user_serial')
                    ->where('user_id', $this->user_info['id'])
                    ->where('single_mark', $data['number'])
                    ->where('much_id', $data['much_id'])
                    ->find();
                if (!empty($user_serial)) {
                    Db::name('feeling_pay')->insert(['create_time' => time(), 'pay_type' => 0, 'pay_status' => $user_serial['status'], 'order_id' => $user_serial['single_mark'], 'user_id' => $this->user_info['id'], 'fg_id' => $res, 'much_id' => $data['much_id']]);
                }
            }
            // 提交事务
            Db::commit();
            $msg = '';
            if (empty($config) || $config['auto_careful'] == 1) {
                $msg = '放入成功！';
            }
            if (!empty($config) && $config['auto_careful'] == 0) {
                $msg = '请等待审核！';
            }
            return $this->json_rewrite(['code' => '0', 'msg' => $msg]);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['code' => '1', 'msg' => '放入失败，请稍候重试！4']);
        }

    }

    /**
     * 修改纸条
     */
    public function update_tape_do()
    {
        $data = input('param.');
        if (empty(str_replace(" ", '', $data['contact_person']))) {
            return $this->json_rewrite(['code' => 1, 'msg' => '联系方式不能为空']);
        }
        if (empty(str_replace(" ", '', $data['hedge_content']))) {
            return $this->json_rewrite(['code' => 1, 'msg' => '内容不能为空']);
        }
        $util=new Util();
        $check=$util->get_check_msg($data['hedge_content'],$data['much_id'],$data['openid']);
        if($check['code']==1){
            return $this->json_rewrite($check);
        }
        $check_person=$util->get_check_msg($data['contact_person'],$data['much_id'],$data['openid']);
        if($check_person['code']==1){
            return $this->json_rewrite($check_person);
        }
        $item['contact_person'] = $data['contact_person'];
        $html = '';
        $hedge_content = emoji_encode($data['hedge_content']);
        $html .= $hedge_content;
        if (!empty($data['imgList'])) {
            $string_arr = explode(",", $data['imgList']);
            foreach ($string_arr as $k => $v) {
                $html .= "<img src='{$v}' />";
            }
        }
        $item['hedge_content'] = $html;
        $item['create_time'] = time();
        $item['check_status'] = 0;
        $item['check_opinion'] = '';
        $item['is_del'] = 0;
        $item['much_id'] = $data['much_id'];
        //判断当前纸条状态
        $fle=Db::name('feeling')
            ->where('much_id',$data['much_id'])
            ->where('id',$data['id'])
            ->find();
        if(empty($fle)){
            return $this->json_rewrite(['code' => 1, 'msg' => '纸条不见了']);
        }
        if($fle['check_status']==0||$fle['check_status']==1){
            return $this->json_rewrite(['code' => 1, 'msg' => '纸条不允许修改']);
        }
        if($fle['is_del']==1){
            return $this->json_rewrite(['code' => 1, 'msg' => '纸条已被删除']);
        }
        if($fle['user_id']!=$this->user_info['id']){
            return $this->json_rewrite(['code' => 1, 'msg' => '你没有权限修改']);
        }
        // 启动事务
        Db::startTrans();
        try {
            $res = Db::name('feeling')->where('id',$data['id'])->update($item);
            if (!$res) {
                Db::rollback();
                return $this->json_rewrite(['code' => '1', 'msg' => '放入失败，请稍候重试！1']);
            }

            // 提交事务
            Db::commit();
            return $this->json_rewrite(['code' => '0', 'msg' => '提交成功，请等待审核！']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['code' => '1', 'msg' => '放入失败，请稍候重试！4'.$e->getMessage()]);
        }

    }

    /**
     * 获取IP地区
     */
    public function get_position_by_ip($ip, $key)
    {
        if (empty($key)) {
            return ['adcode' => null, 'province' => '隐藏'];
        }
        $url = 'https://apis.map.qq.com/ws/location/v1/ip?ip=' . $ip . '&key=' . $key;
        $options = [
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ],
            'socket' => [
                'bindto' => '0:0'
            ]
        ];
        $context = stream_context_create($options);
        $ip_content = file_get_contents($url,false,$context);
        $ip_content = json_decode($ip_content, true);
        if (empty($ip_content) || $ip_content['status'] != 0) {
            return ['adcode' => null, 'province' => '隐藏'];
        }

        return ['adcode' => $ip_content['result']['ad_info']['adcode'], 'province' => $ip_content['result']['ad_info']['province']];
    }

    /**
     * 抽取小纸条（积分/贝壳）
     */
    public function do_smoke_tape()
    {
        $data = input('param.');
        //获取纸条配置
        $config = Db::name('feeling_stipulate')->where('much_id', $data['much_id'])->find();
        $key = 1;//0 通行 1禁止
        if ($config['pick_price_male'] == 0 && intval($data['gender']) == 1) {//男
            $key = 0;
        }
        if ($config['pick_price_female'] == 0 && intval($data['gender']) == 0) {//女
            $key = 0;
        }
        if (intval($config['pay_type']) == 2) {//微信支付判断支付号
            if ($key == 1) {
                $user_serial = Db::name('user_serial')
                    ->where('user_id', $this->user_info['id'])
                    ->where('single_mark', $data['number'])
                    ->where('much_id', $data['much_id'])
                    ->find();
                if (empty($user_serial) || $user_serial['status'] == 0) {
                    return $this->json_rewrite(['code' => 1, 'msg' => '订单未能成功支付，请稍候重试！']);
                }
                $f_pay = Db::name('feeling_pay')
                    ->where('much_id', $data['much_id'])
                    ->where('user_id', $this->user_info['id'])
                    ->where('order_id', $data['number'])
                    ->find();
                if (!empty($f_pay)) {
                    return $this->json_rewrite(['code' => 1, 'msg' => '订单重复，请稍候重试！']);
                }
            }

        }
        //查询是否有抽取限制
        if (!empty($config) && intval($config['pick_limit']) > 0) {
            //查询我今日抽取的
            $count = Db::name('user_feeling')
                ->where('user_id', $this->user_info['id'])
                ->where('much_id', $data['much_id'])
                ->whereTime('pull_time', 'today')
                ->count();
            if ($count >= $config['throw_limit']) {
                return $this->json_rewrite(['code' => 1, 'msg' => '今日抽小纸条已达上限']);
            }
        }

        //return $this->json_rewrite($where);
        $smoke = $this->rand_tape($data);
        //return $this->json_rewrite($smoke);
        if (empty($smoke)) {
            return $this->json_rewrite(['code' => 1, 'msg' => '没有抽到合适的小纸条']);
        }
        $item['user_id'] = $this->user_info['id'];
        $item['fg_id'] = $smoke['id'];
        $item['pull_time'] = time();
        $item['much_id'] = $data['much_id'];
        if ($data['gender'] == 1) {
            $money = abs($config['pick_price_male']);
        } else {
            $money = abs($config['pick_price_female']);
        }
        if (!empty($config) && intval($config['pay_type']) != 2) {
            if ($config['pay_type'] == 0) {
                //判断积分
                if (bccomp($money, $this->user_info['fraction'], 2) == 1) {
                    return $this->json_rewrite(['code' => '1', 'msg' => $this->design['confer'] . '不足！']);
                }
            } else {
                //判断贝壳
                if (bccomp($money, $this->user_info['conch'], 2) == 1) {
                    return $this->json_rewrite(['code' => '1', 'msg' => $this->design['currency'] . '不足！']);
                }
            }
        }
        // 启动事务
        Db::startTrans();
        try {
            $res = Db::name('user_feeling')->insertGetId($item);
            if (!$res) {
                Db::rollback();
                return $this->json_rewrite(['code' => '1', 'msg' => '抽纸条失败，请稍候重试！1']);
            }
            if (!empty($config) && $config['pay_type'] != 2 && $key == 1) {
                //明细表增加数据
                $amount['user_id'] = $this->user_info['id'];
                $amount['category'] = 2;
                $amount['finance'] = -$money;
                if ($config['pay_type'] == 0) {
                    $amount['poem_fraction'] = $this->user_info['fraction'];
                    $amount['surplus_fraction'] = bcsub($this->user_info['fraction'], $money, 2);
                } else {
                    $amount['poem_conch'] = $this->user_info['conch'];
                    $amount['surplus_conch'] = bcsub($this->user_info['conch'], $money, 2);
                }
                $amount['ruins_time'] = time();
                $amount['solution'] = '抽取小纸条';
                $amount['evaluate'] = $config['pay_type'] == 0 ? 1 : 0;
                $amount['much_id'] = $data['much_id'];
                $amount_res = Db::name('user_amount')->insert($amount);
                if (!$amount_res) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => '1', 'msg' => '抽纸条失败，请稍候重试！2']);
                }
                if ($config['pay_type'] == 0) {
                    $user_res = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['fraction' => $amount['surplus_fraction']]);
                } else {
                    $user_res = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['conch' => $amount['surplus_conch']]);
                }
                if (!$user_res) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => '1', 'msg' => '抽纸条失败，请稍候重试！3']);
                }
            }
            if ($config['pay_type'] == 2 && $key == 1) {//微信支付判断支付号
                $user_serial = Db::name('user_serial')
                    ->where('user_id', $this->user_info['id'])
                    ->where('single_mark', $data['number'])
                    ->where('much_id', $data['much_id'])
                    ->find();
                if (!empty($user_serial)) {
                    Db::name('feeling_pay')->insert(['create_time' => time(), 'pay_type' => 1, 'pay_status' => $user_serial['status'], 'order_id' => $user_serial['single_mark'], 'user_id' => $this->user_info['id'], 'fg_id' => $res, 'much_id' => $data['much_id']]);
                }
            }
            // 提交事务
            Db::commit();
            return $this->json_rewrite(['code' => '0', 'msg' => '抽中了一张纸条！', 'tape' => $smoke]);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['code' => '1', 'msg' => '抽纸条失败，请稍候重试！4', $e->getMessage()]);
        }

    }

    /**
     * 查询我放入的小纸条
     */
    public function get_tape_list()
    {
        $data = input('param.');
        $feeling = Db::name('feeling')
            ->where('user_id', $this->user_info['id'])
            ->where('is_del', 0)
            ->where('much_id', $data['much_id'])
            ->field('id,contact_person,age,gender,constellation,hedge_content,longevity,restrict_city,create_time,check_status,check_opinion')
            ->page($data['page'], 15)
            ->select();
        $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
        foreach ($feeling as $k => $v) {
            preg_match_all($preg, emoji_decode($v['hedge_content']), $match);
            if (!empty($match[0])) {
                $img = array();
                foreach ($match[1] as $a => $b) {
                    $img[$a] = htmlspecialchars_decode($b);
                }
                $feeling[$k]['image_part'] = $img;
            } else {
                $feeling[$k]['image_part'] = [];
            }
            $feeling[$k]['hedge_content'] = strip_tags(emoji_decode($v['hedge_content']));
            //查询抽到次数
            $feeling[$k]['smoke_content'] = Db::name('user_feeling')->where('much_id', $data['much_id'])->where('fg_id', $v['id'])->count();
        }
        return $this->json_rewrite(['info' => $feeling]);
    }

    /**
     *  我抽的
     */
    public function somke_tape_list()
    {
        $data = input('param.');
        $feeling = Db::name('user_feeling')->alias('f')
            ->join('feeling u', 'u.id=f.fg_id')
            ->where('f.user_id', $this->user_info['id'])
            ->where('f.much_id', $data['much_id'])
            ->field('f.pull_time,u.id,u.contact_person,u.age,u.gender,u.constellation,u.hedge_content,u.longevity,u.restrict_city,u.create_time,u.check_status,u.check_opinion')
            ->page($data['page'], 15)
            ->select();
        $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
        foreach ($feeling as $k => $v) {
            preg_match_all($preg, emoji_decode($v['hedge_content']), $match);
            if (!empty($match[0])) {
                $img = array();
                foreach ($match[1] as $a => $b) {
                    $img[$a] = htmlspecialchars_decode($b);
                }
                $feeling[$k]['image_part'] = $img;
            } else {
                $feeling[$k]['image_part'] = [];
            }
            $feeling[$k]['hedge_content'] = strip_tags(emoji_decode($v['hedge_content']));
        }
        return $this->json_rewrite(['info' => $feeling]);
    }

    /**
     * 删除
     */
    public function del_tape_do()
    {
        $data = input('param.');
        $feeling = Db::name('feeling')
            ->where('user_id', $this->user_info['id'])
            ->where('is_del', 0)
            ->where('id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if (empty($feeling)) {
            return $this->json_rewrite(['code' => '1', 'msg' => '删除失败，请稍候重试！']);
        }
        $del = Db::name('feeling')
            ->where('user_id', $this->user_info['id'])
            ->where('id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->update(['is_del' => 1]);
        if ($del !== false) {
            return $this->json_rewrite(['code' => '0', 'msg' => '删除成功！']);
        } else {
            return $this->json_rewrite(['code' => '1', 'msg' => '删除失败，请稍候重试！']);
        }

    }

    /**
     * 循环取出小纸条
     */
    public function rand_tape($data, $already = 0)
    {
        //判断是否能抽到纸条
        $where = [];
        //查询我已经抽到的
        $my_feeling = Db::name('user_feeling')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
        if (!empty($my_feeling)) {
            //我抽到的纸条
            $my_feeling_list = '';
            foreach ($my_feeling as $k => $v) {
                $my_feeling_list .= $v['fg_id'] . ',';
            }
            $my_feeling_list = substr($my_feeling_list, 0, -1);
            $where['id'] = ['not in', $my_feeling_list];
        }
        //抽男的还是女的纸条
        if (intval($data['gender']) == 1) {
            $where['gender'] = ['eq', 1];
        } else {
            $where['gender'] = ['eq', 0];
        }
        //排除我自己的纸条
        $where['user_id'] = ['neq', $this->user_info['id']];
        //有条件的查询

        //选择地区查询
        if ($data['c_type'] == 1) {
            if (intval($data['c_area_index']) != 0) {
                $where['remain_city'] = ['eq', $data['c_area_index']];
            }
        } else if ($data['c_type'] == 2) {
            if (intval($data['a_area_index']) != 0) {
                $where['remain_city'] = ['eq', $data['a_area_index']];
            }
        } else if ($data['c_type'] == 3) {
            //星座查询
            if ($data['constellation'] != null) {
                $where['constellation'] = ['eq', $data['constellation']];
            }
        }
        if ($data['c_type'] == 1 || $data['c_type'] == 2) {
            //选择年龄查询
            if (intval($data['old_name']) != 0) {
                $where['age'] = ['eq', $data['old_name']];
            }
        }
        if ($already == 1) {
            $where['restrict_city'] = ['eq', 0];
        }

        $feeling = Db::name('feeling')->where('is_del', 0)->where('check_status', 1)->where($where)->where('much_id', $data['much_id'])->orderRaw('rand()')->field('id,contact_person,age,gender,constellation,hedge_content,longevity,restrict_city')->find();
        //return $feeling;
        $my_feeling = Db::name('user_feeling')->where('fg_id', $feeling['id'])->where('much_id', $data['much_id'])->count();
        if ($feeling['longevity'] != 0 && $my_feeling >= $feeling['longevity']) {
            return $this->rand_tape($data);
        } else {
            //判断取出的小纸条 是否有地区需求
            if ($feeling['restrict_city'] != 0) {
                if (intval($data['c_my_area_id']) != intval($feeling['restrict_city'])) {
                    return $this->rand_tape($data, 1);
                } else {
                    if (empty($feeling)) {
                        return $feeling;
                    }
                    $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
                    preg_match_all($preg, emoji_decode($feeling['hedge_content']), $match);
                    if (!empty($match[0])) {
                        $img = array();
                        foreach ($match[1] as $a => $b) {
                            $img[$a] = htmlspecialchars_decode($b);
                        }
                        $feeling['image_part'] = $img;
                    } else {
                        $feeling['image_part'] = [];
                    }
                    $feeling['hedge_content'] = strip_tags(emoji_decode($feeling['hedge_content']));
                    return $feeling;
                }
            } else {
                if (empty($feeling)) {
                    return $feeling;
                }
                $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
                preg_match_all($preg, emoji_decode($feeling['hedge_content']), $match);
                if (!empty($match[0])) {
                    $img = array();
                    foreach ($match[1] as $a => $b) {
                        $img[$a] = htmlspecialchars_decode($b);
                    }
                    $feeling['image_part'] = $img;
                } else {
                    $feeling['image_part'] = [];
                }
                $feeling['hedge_content'] = strip_tags(emoji_decode($feeling['hedge_content']));
                return $feeling;
            }
        }
    }
}