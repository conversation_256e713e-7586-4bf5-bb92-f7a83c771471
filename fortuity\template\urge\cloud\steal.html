<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>用户网盘信息</title>
    <link rel="stylesheet" href="./assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <style>.pagination{font-size:12px}.select-transfer{cursor:pointer}.select-transfer:hover{color:red}a{color:#000;text-decoration:none !important}a:hover{color:#000}.tpl-table-black-operation-del{display:inline-block;padding:5px 6px;font-size:12px;line-height:12px;border:1px solid #e7505a;color:#e7505a}.tpl-table-black-operation-del:hover{background:#e7505a;color:#fff}.am-btn-secondary{color:#59bcdb;background-color:#fff;border-color:#59bcdb;padding:5px 8px;font-size:12px}.am-btn-secondary:hover{background-color:#59bcdb;color:#fff}</style>
</head>
<body>
{if $type!=2}
<div class="am-g" style="width: 100%;height: 40px;display: flex;align-items: center;padding-left: 5px;">
    <a href="javascript:void(0);" class="contact-btn" onclick="$('#netDiscFile').click();">
        <button type="button" class="am-btn am-btn-secondary am-radius">
            上传文件
        </button>
    </a>
    <span style="display: none;">
        <input id="netDiscFile" type="file" name="netDiscFile" multiple onchange="uploadFiles();">
    </span>
    {if $pid != 0}
    <a href="{:url('cloud/steal')}&uid={$uid}&type={$type}" target="_self" class="contact-btn" style="margin-left: 10px;">
        <button type="button" class="am-btn am-btn-secondary am-radius">
            返回上级目录
        </button>
    </a>
    {else}
    <a href="javascript:void(0);" class="contact-btn" style="margin-left: 10px;" onclick="newForMoveFolder(0);">
        <button type="button" class="am-btn am-btn-secondary am-radius">
            新建文件夹
        </button>
    </a>
    {/if}
</div>
{/if}
<div class="am-g">
    <div class="am-form" style="font-size: 14px;">
        <table class="am-table am-table-compact am-table-bordered am-table-radius am-table-striped">
            <thead>
            <tr>
                <th class="text-center" style="width:25%;">
                    文件名
                </th>
                <th class="text-center" style="width:15%;">
                    文件类型
                </th>
                <th class="text-center" style="width:15%;">
                    文件大小
                </th>
                <th class="text-center" style="width:30%;">
                    保存时间
                </th>
                {if $type!=2}
                <th class="text-center" style="width:15%;">
                    操作
                </th>
                {/if}
            </tr>
            </thead>
            <tbody>
            {volist name="list" id="vo"}
            <tr>
                <td class="am-text-middle text-left" style="padding-left: 10px;">
                    {if $vo.is_dir}
                    <a href="{:url('cloud/steal')}&uid={$uid}&pid={$vo.id}&type={$type}" target="_self" title="{$vo.file_name}">
                       <i class="am-icon-folder-o" style="margin-right: 3px;"></i>
                        {$vo.file_name|subtext=8}
                    </a>
                    {else}
                    <a href="{$vo.ncInfo.file_address}" target="_blank" title="{$vo.file_name}.{$vo.ncInfo.file_suffix}">
                        <i class="am-icon-file-text-o" style="margin-right: 3px;"></i>
                        {$vo.file_name|subtext=6}.{$vo.ncInfo.file_suffix}
                    </a>
                    {/if}
                </td>
                <td class="am-text-middle text-center">
                    {if $vo.is_dir == 0}{$vo.ncInfo.file_suffix}{else}文件夹{/if}
                </td>
                <td class="am-text-middle text-center">
                    {if $vo.is_dir == 0} {:$setupSize($vo.ncInfo.file_size)} {else} — {/if}
                </td>
                <td class="am-text-middle text-center">
                    {:date('Y-m-d H:i:s',$vo.add_time)}
                </td>
                {if $type!=2}
                <td class="am-text-middle text-center">
                    <div class="am-btn-group am-btn-group-sm" style="display: flex;justify-content: center;">
                        <button class="am-btn" style="background: #fff;padding: 0.5em 0.5em; color:#000;height: 30px;border-left:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                            功能列表
                        </button>
                        <div class="am-dropdown" data-am-dropdown style="height: 30px;line-height: 0;border-right:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                            <button class="am-btn am-dropdown-toggle" data-am-dropdown-toggle style="background: #fff;padding: 0.2em 0.5em;">
                                <span class="am-icon-caret-down"></span>
                            </button>
                            <ul class="am-dropdown-content" style="width: 90px;min-width: initial;">
                                {if $type}
                                <li style="display: flex;justify-content: center;cursor: pointer;" onclick="transferFiles('{$vo.nc_id}','{$vo.id}','{$vo.file_name}{if !$vo.is_dir}.{$vo.ncInfo.file_suffix}{/if}','{$vo.parent_path_id}');">
                                    选择文件
                                </li>
                                {/if}
                                {if $vo.is_dir == 1}
                                <li style="display: flex;justify-content: center;cursor: pointer;" onclick="transferCard('{$vo.id}',true);">
                                    删除目录
                                </li>
                                {else}
                                <li style="display: flex;justify-content: center;cursor: pointer;" onclick="newForMoveFolder(1,'{$vo.id}');">
                                    移动文件
                                </li>
                                <li style="display: flex;justify-content: center;cursor: pointer;" onclick="transferCard('{$vo.id}',false);">
                                    删除文件
                                </li>
                                {/if}
                            </ul>
                        </div>
                    </div>
                </td>
                {/if}
            </tr>
            {/volist}
            </tbody>
        </table>
    </div>
</div>
<div class="text-center" style="position: fixed;bottom: 0;width: 100%;">
    {$list->render()}
</div>
<div id="newFolders" class="hide">
    <div class="am-g tpl-amazeui-form" style="background: #fcfcfc;width: 100%;height: 100%;">
        <div class="am-form am-form-horizontal">
            <div class="am-form-group" style="margin-top: 35px;">
                <label class="am-u-sm-4 am-form-label">新建文件夹</label>
                <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                    <input class="newFolderName" type="text" style="width: 220px;" placeholder="请输入文件夹名称">
                </div>
            </div>
            <div class="am-form-group" style="margin-top: 30px;display: flex;justify-content: center;">
                <div style="cursor:pointer;color:#ffffff;background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;">
                    <div style="width: 100%;height: 100%;" onclick="holdSave(0)">
                        保存
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="moveFolders" class="hide">
    <div class="am-g tpl-amazeui-form" style="background: #fcfcfc;width: 100%;height: 100%;">
        <div class="am-form am-form-horizontal">
            <div class="am-form-group" style="margin-top: 35px;">
                <label class="am-u-sm-4 am-form-label">移动目录</label>
                <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                    <span class="fileId hide"></span>
                    <select class="moveFolderId" style="width: 220px;font-weight:bold;">
                        <option value="-1" style="font-size: 12px;font-weight:bold;">
                            请选择
                        </option>
                        {if $pid}
                        <option value="0" style="font-size: 12px;font-weight:bold;">
                            /
                        </option>
                        {/if}
                        {volist name="folderList" id="vo"}
                        <option value="{$vo.id}" style="font-size: 12px;font-weight:bold;">
                            <span style="margin-right: 2px;">/</span>
                            <span>{$vo.file_name}</span>
                        </option>
                        {/volist}
                    </select>
                </div>
            </div>
            <div class="am-form-group" style="margin-top: 30px;display: flex;justify-content: center;">
                <div style="cursor:pointer;color:#ffffff;background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;">
                    <div style="width: 100%;height: 100%;" onclick="holdSave(1)">
                        保存
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script src="./assets/js/jquery.min.js"></script>
<script src="./assets/js/bootstrap.min.js"></script>
<script src="./assets/js/amazeui.min.js"></script>
<script src="./static/layer/layer.js"></script>
<script>

    var uploadFiles = function () {
        var netDiscFile = $('#netDiscFile');
        var filesExtLimit = '{$allowedUploadTypes}';
        var filesExtLimitArray = filesExtLimit.split(',');
        var filesQualified = true;
        $(netDiscFile.get(0).files).each(function (i) {
            var suffix = netDiscFile.get(0).files[i].name.lastIndexOf('.');
            filesQualified = filesExtLimitArray.includes(netDiscFile.get(0).files[i].name.substring(suffix + 1, netDiscFile.get(0).files[i].name.length).toLowerCase());
            if (!filesQualified) {
                return false;
            }
        });
        if (filesQualified) {
            layer.load();
            var i = 0;
            var n = 0;
            var uploadResult = function (j) {
                i += j;
                if (n === netDiscFile.get(0).files.length) {
                    layer.closeAll('loading');
                    if (n === i) {
                        layer.msg('上传成功', {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        $('#netDiscFile').val('');
                        layer.msg('上传失败，请检查上传配置');
                    }
                }
            };
            $(netDiscFile.get(0).files).each(function (i) {
                var formData = new FormData();
                formData.append('netDiscFile', netDiscFile.get(0).files[i]);
                formData.append('uid', '{$uid}');
                formData.append('pid', '{$pid}');
                $.ajax({
                    type: "post",
                    url: "{:url('cloud/uploadNetDisc')}",
                    async: true,
                    data: formData,
                    processData: false,
                    contentType: false,
                    dataType: 'json',
                    success: function (data) {
                        ++n;
                        if (data.code > 0) {
                            uploadResult(1);
                        } else {
                            uploadResult(-1);
                        }
                    }
                });
            });
        } else {
            netDiscFile.val('');
            layer.msg('目前仅支持上传 ' + filesExtLimit + ' 类型的文件，请排除后再次尝试！', {time: 6000});
        }
    }

    var transferFiles = function (nid, fid, fileName, pid) {
        parent.layer.confirm('您确定要选择这个文件（夹）吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            parent.filesRefrain(nid, fid, fileName, pid);
            parent.layer.closeAll();
        }, function (index) {
            parent.layer.close(index);
        });
    }

    var newForMoveFolder = function (type, fid) {
        var content = '';
        switch (type) {
            case 0:
                content = $('#newFolders').html();
                break;
            case 1:
                content = $('#moveFolders').html();
                break;
        }
        layer.open({
            type: 1,
            title: false,
            scrollbar: false,
            closeBtn: true,
            area: ['400px', '170px'],
            shadeClose: true,
            content: content,
            success: function () {
                $('.layui-layer .fileId').text(fid);
            }
        });
    }

    var holdSave = function (type) {
        var uid = '{$uid}';
        switch (type) {
            case 0:
                var newFolderName = $('.layui-layer .newFolderName').val();
                $.post("{:url('cloud/newUserFolder')}", {'uid': uid, 'folderName': newFolderName}, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 1800});
                    }
                }, 'json');
                break;
            case 1:
                var fid = $.trim($('.layui-layer .fileId').text());
                var moveFolderId = Number($('.layui-layer .moveFolderId').val());
                $.post("{:url('cloud/moveUserFile')}", {'fid': fid, 'uid': uid, 'pid': moveFolderId}, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 1800});
                    }
                }, 'json');
                break;
        }
    };


    var transferCard = function (fid, isDir) {
        var title = '您确定要删除选择的';
        if (isDir) {
            title += '目录包括目录下的文件吗？';
        } else {
            title += '数据吗？';
        }
        layer.confirm(title, {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('cloud/delUserFileInfo')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }
</script>
</html>