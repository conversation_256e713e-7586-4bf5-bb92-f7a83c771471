{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 新增榜单
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">

                    <div class="am-form-group am-margin-top-xl">
                        <label class="am-u-sm-3 am-form-label">榜单名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" placeholder="请输入榜单名称">
                        </div>
                    </div>

                    <div class="am-form-group am-margin-top-xl">
                        <label class="am-u-sm-3 am-form-label">排序条件</label>
                        <div class="am-u-sm-9">
                            <label class="am-checkbox-inline" style="margin-left:10px">
                                <input name="sortCondition" type="checkbox" value="chongzhi"> 充值金额
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="xiaofei"> 消费金额
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="fensi"> 粉丝数量
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="fatie"> 发帖次数
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="huitie"> 回帖次数
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="beihuitie"> 被回帖次数
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="dianzan"> 点赞次数
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="beidianzan"> 被点赞次数
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="fahongbao"> 发红包次数
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="songli"> 送礼次数
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="shouli"> 收礼次数
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="renwu"> 任务完成次数
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="jingyan"> 经验值累计获取点数
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="rongyu"> 荣誉值累计获取点数
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="yaoqing"> 邀请好友数量
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="qiandao"> 签到次数
                            </label>
                            <label class="am-checkbox-inline">
                                <input name="sortCondition" type="checkbox" value="jifen"> 积分余额
                            </label>
                        </div>
                    </div>

                    <div class="am-form-group am-margin-top-xl">
                        <label class="am-u-sm-3 am-form-label">状态</label>
                        <div class="am-u-sm-9">
                            <select id="status">
                                <option value="0">隐藏</option>
                                <option value="1">显示</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group am-margin-top-xl">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="scores" value="0" placeholder="请输入排序数字">
                        </div>
                    </div>

                    <div class="am-form-group am-margin-top-xl">
                        <div class="am-u-sm-9 am-u-sm-push-3">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var slock = false;
    function holdSave() {
        if (!slock) {
            var name = $.trim($('#name').val());
            if (name === '') {
                layer.msg('请输入榜单名称');
                return;
            }
            var sortCondition = [];
            $("[name='sortCondition']:checked").each(function () {
                sortCondition.push($(this).val());
            });
            if (sortCondition.length === 0) {
                layer.msg('请选择排序条件');
                return;
            }
            var status = Number($('#status').val());
            var scores = Number($('#scores').val());
            slock = true;
            $.ajax({
                type: "post",
                url: "{:url('unlawful/ruannunciation')}",
                data: {
                    'name': name,
                    'sortCondition': sortCondition,
                    'status': status,
                    'scores': scores
                },
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.href = "{:url('unlawful/annunciation')}";
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                            slock = false;
                        });
                    }
                }
            });
        }
    }
</script>
{/block}