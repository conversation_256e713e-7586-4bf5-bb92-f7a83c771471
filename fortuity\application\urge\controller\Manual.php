<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\common\Playful;
use think\Cache;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

#任务系统
class Manual extends Base
{

    //任务列表
    public function propagate()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('task')
            ->where('task_name', 'like', "%{$hazy_name}%")
            ->where('is_being', 0)
            ->where('much_id', $this->much_id)
            ->order('scores', 'asc')
            ->order('id', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //任务排序
    public function propagateSort()
    {
        $syid = request()->post('asyId');
        $scores = request()->post('dalue');
        Db::startTrans();
        try {
            Db::name('task')->where('id', $syid)->where('much_id', $this->much_id)->update(['scores' => $scores]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
        }
        return json(['code' => 1, 'msg' => '保存成功']);
    }

    //删除任务
    public function delPropagate()
    {
        if (request()->isPost() && request()->isAjax()) {
            $taid = request()->post('taid');
            $taskInfo = Db::name('task')->where('id', $taid)->where('is_being', 0)->where('much_id', $this->much_id)->find();
            if (!$taskInfo) {
                return json(['code' => 0, 'msg' => '删除失败']);
            }
            Db::startTrans();
            try {
                Db::name('task')->where('id', $taid)->where('is_being', 0)->where('much_id', $this->much_id)->update(['is_being' => 1, 'death_time' => time()]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '删除成功']);
        } else {
            $this->error('参数错误', 'manual/propagate');
        }
    }

    //新增任务
    public function newPropagate()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            $data['channel_time'] = time();
            $data['is_being'] = 0;
            $data['death_time'] = 0;
            $data['much_id'] = $this->much_id;
            try {
                Db::name('task')->insert($data);

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $defaultNavigate = $this->defaultNavigate();
            $this->assign('defaultNavigate', $defaultNavigate);
            //  查找未被删除的圈子
            $toryList = Db::name('territory')->where('is_del', 0)->where('much_id', $this->much_id)->field('id,realm_name')->select();
            $this->assign('toryList', $toryList);
            return $this->fetch();
        }
    }

    public function editPropagate()
    {
        if (request()->isPost() && request()->isAjax()) {
            $conflux = request()->post();
            //  任务名称目前不可修改
            unset($conflux['task_name']);
            $meid = $conflux['meid'];
            $data = $conflux['setData'];
            try {
                Db::name('task')->where('id', $meid)->update($data);

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $layid = request()->get('layid');
            $list = Db::name('task')->where('id', $layid)->where('much_id', $this->much_id)->find();
            if ($list) {
                $defaultNavigate = $this->defaultNavigate();
                $this->assign('defaultNavigate', $defaultNavigate);
                $this->assign('list', $list);
                //  查找未被删除的圈子
                $toryList = Db::name('territory')->where('is_del', 0)->where('much_id', $this->much_id)->field('id,realm_name')->select();
                $this->assign('toryList', $toryList);
                return $this->fetch();
            } else {
                $this->error('参数错误', 'manual/propagate');
            }
        }
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    //头像框列表
    public function adhesion()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('avatar_frame')
            ->where('adorn_name', 'like', "%{$hazy_name}%")
            ->where('purge_time', 0)
            ->where('much_id', $this->much_id)
            ->order('scores', 'asc')
            ->order('id', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //头像框排序
    public function adhesionSort()
    {
        $syid = request()->post('asyId');
        $scores = request()->post('dalue');
        Db::startTrans();
        try {
            Db::name('avatar_frame')->where('id', $syid)->where('much_id', $this->much_id)->update(['scores' => $scores]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
        }
        return json(['code' => 1, 'msg' => '保存成功']);
    }

    //新增头像框
    public function newAdhesion()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            $data['prepare_time'] = time();
            $data['purge_time'] = 0;
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('avatar_frame')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            return $this->fetch();
        }
    }

    //编辑像框
    public function editAdhesion()
    {
        if (request()->isPost() && request()->isAjax()) {
            $conflux = request()->post();
            $meid = $conflux['meid'];
            $data = $conflux['setData'];
            Db::startTrans();
            try {
                Db::name('avatar_frame')->where('id', $meid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $layid = request()->get('layid');
            $list = Db::name('avatar_frame')->where('id', $layid)->where('much_id', $this->much_id)->find();
            if ($list) {
                $this->assign('list', $list);
                return $this->fetch();
            } else {
                $this->error('参数错误', 'manual/adhesion');
            }
        }
    }

    //删除头像框
    public function delAdhesion()
    {
        if (request()->isPost() && request()->isAjax()) {
            $meid = request()->post('meid');
            $medalInfo = Db::name('avatar_frame')->where('id', $meid)->where('purge_time', 0)->where('much_id', $this->much_id)->find();
            if (!$medalInfo) {
                return json(['code' => 0, 'msg' => '删除失败']);
            }
            Db::startTrans();
            try {
                Db::name('avatar_frame')->where('id', $meid)->where('purge_time', 0)->where('much_id', $this->much_id)->update(['status' => 0, 'purge_time' => time()]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '删除成功']);
        } else {
            $this->error('参数错误', 'manual/adhesion');
        }
    }

    //勋章列表
    public function decorate()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('medal')
            ->where('merit_name', 'like', "%{$hazy_name}%")
            ->where('purge_time', 0)
            ->where('much_id', $this->much_id)
            ->order('scores', 'asc')
            ->order('id', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //勋章排序
    public function decorateSort()
    {
        $syid = request()->post('asyId');
        $scores = request()->post('dalue');
        Db::startTrans();
        try {
            Db::name('medal')->where('id', $syid)->where('much_id', $this->much_id)->update(['scores' => $scores]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
        }
        return json(['code' => 1, 'msg' => '保存成功']);
    }

    //新增勋章
    public function newDecorate()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            $data['prepare_time'] = time();
            $data['purge_time'] = 0;
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('medal')->insert($data);

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            return $this->fetch();
        }
    }

    //编辑勋章
    public function editDecorate()
    {
        if (request()->isPost() && request()->isAjax()) {
            $conflux = request()->post();
            $meid = $conflux['meid'];
            $data = $conflux['setData'];
            Db::startTrans();
            try {
                Db::name('medal')->where('id', $meid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $layid = request()->get('layid');
            $list = Db::name('medal')->where('id', $layid)->where('much_id', $this->much_id)->find();
            if ($list) {
                $this->assign('list', $list);
                return $this->fetch();
            } else {
                $this->error('参数错误', 'manual/decorate');
            }
        }
    }

    //删除勋章
    public function delDecorate()
    {
        if (request()->isPost() && request()->isAjax()) {
            $meid = request()->post('meid');
            $medalInfo = Db::name('medal')->where('id', $meid)->where('purge_time', 0)->where('much_id', $this->much_id)->find();
            if (!$medalInfo) {
                return json(['code' => 0, 'msg' => '删除失败']);
            }
            Db::startTrans();
            try {
                Db::name('medal')->where('id', $meid)->where('purge_time', 0)->where('much_id', $this->much_id)->update(['status' => 0, 'purge_time' => time()]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '删除成功']);
        } else {
            $this->error('参数错误', 'manual/decorate');
        }
    }

    //等级列表
    public function level()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('user_level')
            ->where('level_name', 'like', "%{$hazy_name}%")
            ->where('much_id', $this->much_id)
            ->order('level_hierarchy', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $levelMax = Db::name('user_level')->where('much_id', $this->much_id)->max('level_hierarchy');
        $this->assign('levelMax', $levelMax);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //删除等级
    public function delLevel()
    {
        if (request()->isPost() && request()->isAjax()) {
            $leid = request()->post('leid');
            $levelInfo = Db::name('user_level')->where('id', $leid)->where('much_id', $this->much_id)->find();
            if (!$levelInfo) {
                return json(['code' => 0, 'msg' => '删除失败']);
            }
            $levelMax = Db::name('user_level')->where('much_id', $this->much_id)->max('level_hierarchy');
            if ($levelInfo['level_hierarchy'] != $levelMax) {
                return json(['code' => 0, 'msg' => '删除失败，请先删除最大等级']);
            }
            Db::startTrans();
            try {
                Db::name('user_level')->where('id', $leid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '删除成功']);
        } else {
            $this->error('参数错误', 'manual/propagate');
        }
    }

    //新增等级
    public function newLevel()
    {
        if (request()->isPost() && request()->isAjax()) {
            $randLock = md5(uniqid(mt_rand(), true));
            $nowTime = time();
            $checkLock = Cache::remember("emergeLevel_{$nowTime}_{$this->much_id}", $randLock, 60);
            if ($randLock == $checkLock) {
                $data = request()->post();
                $data['much_id'] = $this->much_id;
                $getLevelCount = Db::name('user_level')->where('much_id', $this->much_id)->count();
                if ($getLevelCount > 0) {
                    $getLevelMax = Db::name('user_level')->where('much_id', $this->much_id)->max('level_hierarchy');
                    $data['level_hierarchy'] = $getLevelMax + 1;
                } else {
                    $data['level_hierarchy'] = 0;
                }
                Db::startTrans();
                try {
                    Db::name('user_level')->insert($data);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
                return json(['code' => 1, 'msg' => '保存成功']);
            } else {
                return json(['code' => 0, 'msg' => '点击频率较快，请重试']);
            }
        } else {
            $levelCount = Db::name('user_level')->where('much_id', $this->much_id)->count();
            if ($levelCount > 0) {
                $levelMax = Db::name('user_level')->where('much_id', $this->much_id)->max('level_hierarchy');
                $levelMax++;
            } else {
                $levelMax = 0;
            }
            $this->assign('levelMax', $levelMax);
            return $this->fetch();
        }
    }

    //编辑勋章
    public function editLevel()
    {
        if (request()->isPost() && request()->isAjax()) {
            $conflux = request()->post();
            $leid = $conflux['leid'];
            $data = $conflux['setData'];
            Db::startTrans();
            try {
                Db::name('user_level')->where('id', $leid)->where('much_id', $this->much_id)->update($data);

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $leid = request()->get('leid');
            $list = Db::name('user_level')->where('id', $leid)->where('much_id', $this->much_id)->find();
            if ($list) {
                $this->assign('list', $list);
                return $this->fetch();
            } else {
                $this->error('参数错误', 'manual/level');
            }
        }
    }

    //用户日常任务
    public function dailyTask()
    {
        $uid = request()->get('uid');
        if ($uid) {
            $userInfo = Db::name('user')->where('id', $uid)->where('much_id', $this->much_id)->find();
            if ($userInfo) {
                $url = $this->defaultQuery();
                $searchDetail = request()->get('searchDetail', '');
                $egon = intval(request()->get('egon', 0));
                $page = request()->get('page', 1);
                $where['tl.user_id'] = $uid;
                $where['ta.task_name'] = ['like', "%{$searchDetail}%"];
                if ($egon != 0) {
                    $taskType = $egon - 1;
                    $where['ta.task_type'] = $taskType;
                }
                $where['tl.much_id'] = $this->much_id;
                $list = Db::name('task_logger')
                    ->alias('tl')
                    ->join('task ta', 'tl.task_id = ta.id', 'left')
                    ->where($where)
                    ->order(['id' => 'desc'])
                    ->field('tl.*,ta.task_name,ta.task_type,ta.task_cycle,ta.task_reward_type,ta.task_frequency')
                    ->paginate(10, false, ['query' => ['s' => $url, 'uid' => $uid, 'egon' => $egon, 'searchDetail' => $searchDetail]]);
                $this->assign('list', $list);
                $this->assign('egon', $egon);
                $this->assign('searchDetail', $searchDetail);
                $this->assign('userInfo', $userInfo);
                $this->assign('page', $page);
                return $this->fetch();
            } else {
                $this->error('参数错误', 'user/index');
            }
        } else {
            $this->error('参数错误', 'user/index');
        }
    }

    //任务排行榜
    public function taskLeaderboard()
    {
        $url = $this->defaultQuery();
        $hazy_name = trim(request()->get('hazy_name', ''));
        $hazy_name != '' && $hazy_name = emoji_encode($hazy_name);
        $hazy_egon = request()->get('egon', 0);
        switch ($hazy_egon) {
            //全部时间
            case 0:
                $where = [];
                break;
            //今日排行
            case 1:
                $todayTime = strtotime(date('Ymd'));
                $where['tl.complete_time'] = ['between time', [$todayTime, ($todayTime + 86400)]];
                break;
            //本周排行
            case 2:
                $where['tl.complete_time'] = ['>=', $this->this_monday()];
                break;
            //本月排行
            case 3:
                $where['tl.complete_time'] = ['>=', $this->month_firstday()];
                break;
            //本年排行
            case 4:
                $threeYear = mktime(0, 0, 0, 1, 1, date('Y'));
                $where['tl.complete_time'] = ['>=', $threeYear];
                break;
        }
        $where['us.user_nick_name'] = ['like', "%{$hazy_name}%"];
        $list = Db::name('task_logger')
            ->alias('tl')
            ->join('user us', 'tl.user_id = us.id', 'left')
            ->where($where)
            ->where('tl.much_id', $this->much_id)
            ->field('tl.*,us.user_head_sculpture,us.user_nick_name,us.user_wechat_open_id,count(tl.user_id) as fulfill')
            ->group('tl.user_id')
            //->fetchSql()
            ->order(['fulfill' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name]]);
        //dump($list);
        //exit();
        $this->assign('list', $list);
        $this->assign('hazy_name', emoji_decode($hazy_name));
        $this->assign('egon', $hazy_egon);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //获取本周一的时间戳
    private function this_monday($timestamp = 0, $is_return_timestamp = true)
    {
        static $cache;
        $id = $timestamp . $is_return_timestamp;
        if (!isset($cache[$id])) {
            if (!$timestamp)
                $timestamp = time();
            $monday_date = date('Y-m-d', $timestamp - 86400 * date('w', $timestamp) + (date('w', $timestamp) > 0 ? 86400 : -/*6*86400*/518400));
            if ($is_return_timestamp) {
                $cache[$id] = strtotime($monday_date);
            } else {
                $cache[$id] = $monday_date;
            }
        }
        return $cache[$id];
    }

    //获取本月的第一天
    private function month_firstday($timestamp = 0, $is_return_timestamp = true)
    {
        static $cache;
        $id = $timestamp . $is_return_timestamp;
        if (!isset($cache[$id])) {
            if (!$timestamp)
                $timestamp = time();
            $firstday = date('Y-m-d', mktime(0, 0, 0, date('m', $timestamp), 1, date('Y', $timestamp)));
            if ($is_return_timestamp) {
                $cache[$id] = strtotime($firstday);
            } else {
                $cache[$id] = $firstday;
            }
        }
        return $cache[$id];
    }

    //用户经验荣誉点获得记录
    public function expGloryRecord()
    {
        $uid = request()->get('uid');
        if ($uid) {
            $userInfo = Db::name('user')->where('id', $uid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
            if ($userInfo) {
                $url = $this->defaultQuery();
                $searchDetail = request()->get('searchDetail', '');
                $page = request()->get('page', 1);
                $list = Db::name('user_exp_glory_logger')
                    ->where('user_id', $uid)
                    ->where('dot_cap', 'like', "%{$searchDetail}%")
                    ->where('much_id', $this->much_id)
                    ->order(['id' => 'desc'])
                    ->paginate(10, false, ['query' => ['s' => $url, 'uid' => $uid, 'searchDetail' => $searchDetail]]);
                $this->assign('list', $list);
                $this->assign('searchDetail', $searchDetail);
                $this->assign('userInfo', $userInfo);
                $this->assign('page', $page);
                return $this->fetch();
            } else {
                $this->error('参数错误', 'user/index');
            }
        } else {
            $this->error('参数错误', 'user/index');
        }
    }

    //装扮列表
    public function deck()
    {
        $url = $this->defaultQuery();
        $page = request()->get('page', 1);
        $list = Db::name('special_nickname')
            ->where('purge_time', 0)
            ->where('much_id', $this->much_id)
            ->order('scores', 'asc')
            ->order('id', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url]]);
        $this->assign('list', $list);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //装扮排序
    public function deckSort()
    {
        $syid = request()->post('asyId');
        $scores = request()->post('dalue');
        Db::startTrans();
        try {
            Db::name('special_nickname')->where('id', $syid)->where('much_id', $this->much_id)->update(['scores' => $scores]);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
        }
        return json(['code' => 1, 'msg' => '保存成功']);
    }

    //装扮显示状态
    public function deckStatus()
    {
        $usid = request()->post('usid');
        $status = request()->post('status');
        Db::startTrans();
        try {
            Db::name('special_nickname')->where('id', $usid)->where('much_id', $this->much_id)->update(['status' => $status]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'msg' => 'error , ' . $e->getCode()]);
        }
        return json(['code' => 1, 'msg' => $status == 0 ? '状态已更改为隐藏' : '状态已更改为正常']);
    }

    //新增装扮
    public function newDeck()
    {
        if (request()->isPost() && request()->isAjax()) {
            $cluster = request()->post();
            $snInfo = Db::name('special_nickname')->where('special_style', "yl_style{$cluster['special_number']}")->where('much_id', $this->much_id)->find();
            if ($snInfo && intval($snInfo['purge_time']) === 0) {
                return json(['code' => 0, 'msg' => '当前装扮已添加，请先删除或尝试添加其他的装扮！']);
            }
            $data['special_name'] = '';
            switch (intval($cluster['special_number'])) {
                case 1:
                    $data['special_name'] = '扫描';
                    break;
                case 2:
                    $data['special_name'] = '抖动';
                    break;
                case 3:
                    $data['special_name'] = '抖音';
                    break;
                case 4:
                    $data['special_name'] = '霓虹';
                    break;
                case 5:
                    $data['special_name'] = '模糊';
                    break;
                case 6:
                    $data['special_name'] = '彩虹';
                    break;
                case 7:
                    $data['special_name'] = '闪烁';
                    break;
                case 8:
                    $data['special_name'] = '条刷';
                    break;
                default:
                    return json(['code' => 0, 'msg' => '非法数据']);
            }
            $data['special_style'] = "yl_style{$cluster['special_number']}";
            $data['unlock_outlay'] = abs($cluster['unlock_outlay']);
            $data['status'] = $cluster['status'];
            $data['scores'] = $cluster['scores'];
            $data['prepare_time'] = time();
            $data['purge_time'] = 0;
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                if ($snInfo && intval($snInfo['purge_time']) !== 0) {
                    Db::name('special_nickname')->where('id', $snInfo['id'])->where('much_id', $this->much_id)->update($data);
                } else {
                    Db::name('special_nickname')->insert($data);
                }

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            return $this->fetch();
        }
    }

    //删除装扮
    public function delDeck()
    {
        if (request()->isPost() && request()->isAjax()) {
            $meid = request()->post('meid');
            $medalInfo = Db::name('special_nickname')->where('id', $meid)->where('purge_time', 0)->where('much_id', $this->much_id)->find();
            if (!$medalInfo) {
                return json(['code' => 0, 'msg' => '删除失败']);
            }
            Db::startTrans();
            try {
                Db::name('special_nickname')->where('id', $meid)->where('purge_time', 0)->where('much_id', $this->much_id)->update(['status' => 0, 'purge_time' => time()]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '删除成功']);
        } else {
            $this->error('参数错误', 'manual/deck');
        }
    }
}