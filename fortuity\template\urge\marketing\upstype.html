{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-edit"></span> 编辑分类
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-form am-form-horizontal" style="width: 670px;height:350px;margin: 0px auto;box-shadow: 0px 0px 10px 0px black;">

                    <div class="am-form-group" style="padding: 50px 0px 0px 0px;margin-left: 30px;">
                        <label class="am-u-sm-3 am-form-label">分类名称</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="text" id="name" value="{$list.name}" placeholder="请输入分类名称">
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0px 0px 0px;margin-left: 30px;">
                        <label class="am-u-sm-3 am-form-label">状态</label>
                        <div class="am-u-sm-7 am-u-end">
                            <select id="status">
                                <option value="0" {if $list.status==0}selected{/if}>隐藏</option>
                                <option value="1" {if $list.status==1}selected{/if}>显示</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0px 30px 0px;margin-left: 30px;">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="number" id="scores" value="{$list.scores}" placeholder="请输入排序数字">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <div class="am-u-sm-7 am-u-sm-push-5">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>


    function excheck(name, scores) {
        if (name == '' || name == 'undefined' || name == null) {
            layer.msg('分类名称不能为空');
            return false;
        }
        if (scores > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            return false;
        }
        return true;
    }

    var slock = false;

    function holdSave() {
        if (!slock) {
            var name = $.trim($('#name').val());
            var status = $.trim($('#status').val());
            var scores = $.trim($('#scores').val());
            if (excheck(name, scores)) {
                slock = true;
                $.ajax({
                    type: "post",
                    url: "{:url('upstype')}",
                    data: {
                        'usid': '{$list.id}',
                        'name': name,
                        'status': status,
                        'scores': scores
                    },
                    dataType: 'json',
                    success: function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.href = "{:url('stype')}";
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                slock = false;
                            });
                        }
                    }
                });
            }
        }
    }
</script>
{/block}