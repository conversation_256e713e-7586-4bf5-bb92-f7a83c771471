{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-film {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .action-btn {display: inline-block;padding: 6px 12px;background: #fff;border: 1px solid #ddd;color: #23b7e5;border-radius: 3px;font-size: 12px;cursor: pointer;transition: all 0.3s;text-decoration: none;}
    .action-btn:hover {border-color: #23b7e5;color: #23b7e5;background-color: #f5fafd;text-decoration: none;}
    .action-btn.btn-success {background: #5cb85c;border-color: #5cb85c;color: #fff;}
    .action-btn.btn-success:hover {background: #449d44;border-color: #449d44;color: #fff;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .poster-image {width: 70px;height: auto;border-radius: 4px;border: 2px solid #e8e8e8;box-shadow: 0 2px 4px rgba(0,0,0,0.1);transition: all 0.3s;}
    .poster-image:hover {border-color: #23b7e5;box-shadow: 0 2px 8px rgba(35,183,229,0.3);transform: scale(1.05);}
    .drama-title {font-weight: 500;color: #333;}
    .director-name {color: #666;font-style: italic;}
    .type-name {font-weight: 500;color: #23b7e5;}
    .audit-status {display: inline-block;padding: 3px 8px;border-radius: 12px;font-size: 11px;font-weight: 500;}
    .audit-pending {background-color: #fff3cd;color: #856404;border: 1px solid #ffeaa7;}
    .audit-passed {background-color: #d4edda;color: #155724;border: 1px solid #c3e6cb;}
    .audit-rejected {background-color: #f8d7da;color: #721c24;border: 1px solid #f5c6cb;}
    .display-status {display: inline-block;padding: 3px 8px;border-radius: 12px;font-size: 11px;font-weight: 500;}
    .display-normal {background-color: #d4edda;color: #155724;border: 1px solid #c3e6cb;}
    .display-offline {background-color: #f8d7da;color: #721c24;border: 1px solid #f5c6cb;}

    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
    .modern-dropdown {position: relative;display: inline-block;}
    .modern-dropdown-btn {background: linear-gradient(135deg, #23b7e5 0%, #1a9bc0 100%);border: none;color: white;padding: 8px 16px;border-radius: 6px;font-size: 13px;cursor: pointer;transition: all 0.3s ease;box-shadow: 0 2px 4px rgba(35,183,229,0.2);display: flex;align-items: center;gap: 6px;}
    .modern-dropdown-btn:hover {background: linear-gradient(135deg, #1a9bc0 0%, #1587a8 100%);box-shadow: 0 4px 8px rgba(35,183,229,0.3);transform: translateY(-1px);}
    .modern-dropdown-btn:active {transform: translateY(0);box-shadow: 0 2px 4px rgba(35,183,229,0.2);}
    .modern-dropdown .am-dropdown-content {border: none;border-radius: 6px;box-shadow: 0 4px 12px rgba(0,0,0,0.15);margin-top: 4px;overflow: hidden;}
    .modern-dropdown .am-dropdown-content li a {padding: 10px 16px;color: #333;transition: all 0.2s ease;display: flex;align-items: center;gap: 8px;}
    .modern-dropdown .am-dropdown-content li a:hover {background-color: #f8f9fa;color: #23b7e5;}
    .modern-dropdown .am-dropdown-content li:first-child a {border-top-left-radius: 6px;border-top-right-radius: 6px;}
    .modern-dropdown .am-dropdown-content li:last-child a {border-bottom-left-radius: 6px;border-bottom-right-radius: 6px;}
</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-film"></span> 短剧信息
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="commonSearch();"></i>
                <input type="text" id="searchName" value="{$searchName}" placeholder="搜索短剧名称...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <button type="button" class="action-btn btn-success" onclick="commonAdd();">
                            <span class="am-icon-plus"></span> 新增短剧
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="10%">短剧编号</th>
                            <th width="10%">短剧海报</th>
                            <th width="20%">短剧名称</th>
                            <th width="10%">短剧导演</th>
                            <th width="10%">短剧类型</th>
                            <th width="10%">审核状态</th>
                            <th width="10%">显示状态</th>
                            <th width="10%">添加时间</th>
                            <th width="10%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                <strong>{$vo.id}</strong>
                            </td>
                            <td class="am-text-middle">
                                <img src="{$vo.poster_url}" onerror="this.src='static/disappear/default.png'" class="poster-image"/>
                            </td>
                            <td class="am-text-middle">
                                <span class="drama-title">{$vo.title}</span>
                            </td>
                            <td class="am-text-middle">
                                <span class="director-name">{if $vo.director}{$vo.director}{else}未知{/if}</span>
                            </td>
                            <td class="am-text-middle">
                                <span class="type-name">{$vo.type_name}</span>
                            </td>
                            <td class="am-text-middle">
                                {if $vo.status == 0}
                                <span class="audit-status audit-pending">待审核</span>
                                {elseif $vo.status == 1}
                                <span class="audit-status audit-passed">已通过</span>
                                {elseif $vo.status == 2}
                                <span class="audit-status audit-rejected">未通过</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.display_status == 0}
                                <span class="display-status display-offline">已下架</span>
                                {else}
                                <span class="display-status display-normal">正常</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s', $vo.create_time)}
                            </td>
                            <td class="am-text-middle">
                                <div class="modern-dropdown am-dropdown" data-am-dropdown>
                                    <button class="modern-dropdown-btn am-dropdown-toggle" data-am-dropdown-toggle>
                                        <span>功能列表</span>
                                        <span class="am-icon-caret-down"></span>
                                    </button>
                                        <ul class="am-dropdown-content" style="width: 120px;min-width: initial;">
                                            <li>
                                                <a href="javascript:void(0);" onclick="reviewDetail('{$vo.id}')" style="padding: 5px;color: #000;">
                                                    <span class="am-icon-eye"></span> 查看详情
                                                </a>
                                            </li>
                                            {if $vo.status!=0}
                                            <li>
                                                <a href="javascript:void(0);" @click="importChildContentToCsv('{$vo.id}')" style="padding: 5px;color: #000;">
                                                    <span class="am-icon-upload"></span> 导入数据
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" onclick="newChildContent('{$vo.id}')" style="padding: 5px;color: #000;">
                                                    <span class="am-icon-plus"></span> 新增视频
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" onclick="commonEdit('{$vo.id}')" style="padding: 5px;color: #000;">
                                                    <span class="am-icon-edit"></span> 编辑短剧
                                                </a>
                                            </li>
                                            {/if}
                                            {if $vo.status==0}
                                            <li>
                                                <a href="javascript:void(0);" onclick="auditCorrect('{$vo.id}',1)" style="padding: 5px;color: #000;">
                                                    <span class="am-icon-check"></span> 审核通过
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" onclick="auditCorrect('{$vo.id}',2)" style="padding: 5px;color: #000;">
                                                    <span class="am-icon-times"></span> 审核拒绝
                                                </a>
                                            </li>
                                            {/if}
                                            <li>
                                                <a href="javascript:void(0);" onclick="commonDelete('{$vo.id}')" style="padding: 5px;color: #000;">
                                                    <span class="am-icon-trash"></span> 删除信息
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
    <div style="display: none;">
        <input type="file" ref="fileInput" accept=".csv" @change="handleFileUpload" />
    </div>
</div>
{/block}
{block name="script"}
<script>

    new Vue({
        el: '#app',
        data: {
            fid: 0,
            file: null,
        },
        methods: {
            importChildContentToCsv(fid) {
                this.fid = fid;
                this.$refs.fileInput.click();
            },
            handleFileUpload(event) {
                const selectedFile = event.target.files[0];
                if (selectedFile && selectedFile.name.substring(selectedFile.name.lastIndexOf('.') + 1) !== 'csv') {
                    layer.msg('请选择正确的csv文件！');
                    event.target.files = null;
                    this.file = null;
                    return;
                }
                this.file = selectedFile;
                this.uploadCsv();
            },
            uploadCsv() {
                if (!this.file) {
                    layer.msg("未选择有效文件！");
                    return;
                }
                var formData = new FormData();
                formData.append('mixedFile', this.file);
                $.ajax({
                    type: "post",
                    url: "{:url('dramas/import_micro_series_content_to_data')}&fid=" + this.fid,
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (data) {
                        if (data.code === 1) {
                            layer.msg(data.msg, {icon: 1, time: 1600});
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2200});
                        }
                        this.file = null;
                        this.$refs.fileInput.value = null;
                    }.bind(this)
                });
            }
        }
    });

    var reviewDetail = function (fid) {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['550px', '600px'],
            scrollbar: true,
            closeBtn: true,
            shadeClose: true,
            content: ["{:url('dramas/micro_series_info_detail')}&fid=" + fid],
        });
    }

    function commonAdd() {
        location.href = "{:url('dramas/new_micro_series_info')}";
    }

    function commonEdit(fid) {
        location.href = "{:url('dramas/edit_micro_series_info')}&fid=" + fid;
    }

    function newChildContent(fid) {
        location.href = "{:url('dramas/new_micro_series_content')}&msiId=" + fid;
    }

    var auditCorrect = function (fid, process) {
        var twoCheck = function (fid, process, reaValue) {
            $.ajax({
                type: "post",
                url: "{:url('dramas/trial_micro_series_info')}",
                data: {'fid': fid, 'process': process, 'inject': reaValue},
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }
            });
        }
        switch (process) {
            case 1:
                layer.confirm('您确定要审核通过这条数据吗？', {
                    btn: ['确定', '取消'], title: '提示'
                }, function () {
                    twoCheck(fid, process);
                }, function (index) {
                    layer.close(index);
                });
                break;
            case 2:
                layer.prompt({
                    title: '请输入这条数据未通过审核的原因：',
                    formType: 2,
                    area: ['300px', '100px'],
                    btn: ['确定', '取消'],
                }, function (reaValue, index) {
                    if (reaValue.trim() === '') {
                        return false;
                    }
                    twoCheck(fid, process, reaValue);
                    layer.close(index);
                });
                break;
        }
    }

    function commonDelete(fid) {
        var confirmMessage = '您确定要删除这条数据吗';
        layer.confirm(confirmMessage, {
            btn: ['确定', '取消']
        }, function () {
            $.post("{:url('dramas/del_micro_series_info')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    function commonSearch() {
        var searchName = $.trim($('#searchName').val());
        if (searchName) {
            location.href = "{:url('dramas/micro_series_info')}&searchName=" + searchName + "&page={$page}";
        } else {
            location.href = "{:url('dramas/micro_series_info')}&page={$page}";
        }
    }
</script>
{/block}
