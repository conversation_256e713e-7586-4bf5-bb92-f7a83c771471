{extend name="/base"/}
{block name="main"}
<style>.am-table>thead:first-child>tr:first-child>th,.am-table>tbody>tr>td{text-align: center;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-users"></span> 游客列表
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索游客昵称...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="25%">游客信息</th>
                            <th width="25%">openid</th>
                            <th width="25%">第一次访问时间</th>
                            <th width="25%">最后一次访问时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                {if $vo.user_head_sculpture && $vo.user_head_sculpture !='/yl_welore/style/icon/default.png'}
                                <img src="{$vo.user_head_sculpture}" style="width: 50px;height: 50px;border-radius: 50%;">
                                {else}
                                <img src="{:urlBridging('static/disappear/tourist.png')}" style="width: 50px;height: 50px;border-radius: 50%;">
                                {/if}
                                <span title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </span>
                            </td>
                            <td class="am-text-middle">{$vo.user_wechat_open_id}</td>
                            <td class="am-text-middle"> {:date('Y-m-d H:i:s',$vo.user_reg_time)}</td>
                            <td class="am-text-middle"> {:date('Y-m-d H:i:s',$vo.user_last_time)}</td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>
    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('user/traveler')}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('user/traveler')}&page={$page}";
        }
    }
</script>
{/block}