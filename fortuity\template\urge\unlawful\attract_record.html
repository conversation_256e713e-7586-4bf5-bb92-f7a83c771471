{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-list-alt {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .user-info {display: flex;align-items: center;gap: 10px;justify-content: flex-start;}
    .user-avatar {width: 40px;height: 40px;border-radius: 50%;border: 2px solid #e8e8e8;}
    .user-link {color: #495057;text-decoration: none;font-weight: 500;}
    .user-link:hover {color: #343a40;text-decoration: underline;}
    .openid-text {font-family: monospace;font-size: 12px;color: #666;word-break: break-all;}
    .virtual-user {color: #6c757d;font-style: italic;}
    .reward-type {display: inline-block;padding: 3px 8px;border-radius: 12px;font-size: 11px;font-weight: 500;}
    .reward-points {background-color: #cce5ff;color: #004085;border: 1px solid #b3d7ff;}
    .reward-shells {background-color: #fff3cd;color: #856404;border: 1px solid #ffeaa7;}
    .reward-score {font-weight: 600;color: #e67e22;font-size: 14px;}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-list-alt"></span> 任务完成记录
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索用户信息...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="25%">用户信息</th>
                            <th width="25%">OpenID</th>
                            <th width="15%">奖励类型</th>
                            <th width="15%">奖励分数</th>
                            <th width="20%">奖励时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                <div class="user-info">
                                    <img src="{$vo.user_head_sculpture}" class="user-avatar" alt="用户头像">
                                    <div>
                                        {if $vo.uvirtual == 0}
                                        <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank" class="user-link">
                                            {$vo.user_nick_name|emoji_decode}
                                        </a>
                                        {else}
                                        <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}" class="user-link">
                                            {$vo.user_nick_name|emoji_decode}
                                        </a>
                                        {/if}
                                    </div>
                                </div>
                            </td>
                            <td class="am-text-middle">
                                {if $vo.user_wechat_open_id}
                                <span class="openid-text">{$vo.user_wechat_open_id}</span>
                                {else}
                                <span class="virtual-user">( 虚拟用户 )</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.reward_type == 0}
                                <span class="reward-type reward-points">积分</span>
                                {/if}
                                {if $vo.reward_type == 1}
                                <span class="reward-type reward-shells">贝壳</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                <span class="reward-score">{$vo.reward_code}</span>
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.reward_time)}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    function fuzzy() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('unlawful/attractRecord')}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('unlawful/attractRecord')}&page={$page}";
        }
    }

</script>
{/block}