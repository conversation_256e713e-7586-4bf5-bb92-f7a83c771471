<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover" />
    <meta name="format-detection" content="telephone=no,email=no,date=no,address=no" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="referrer" content="never">
    <title>绑定模板消息</title>
    <link rel="stylesheet" href="./static/layui/css/layui.css">
</head>
<body>
<div style="text-align: center;margin: 50px 0px 20px 0px;">
    <i class="layui-icon layui-icon-login-wechat" style="font-size: 80px; color: #33CC66;"></i>
</div>
{if condition="$id == 0"}
<div style="text-align: center;font-size: 20px;font-weight: 300;">已成功绑定模板消息</div>
{elseif condition="$id == 1"/}
<div style="text-align: center;font-size: 20px;font-weight: 300;color: #CC3333">未成功绑定，请稍后重试！</div>
<div style="padding: 10px 20px;text-align: center;font-size: 12px;font-weight: 300;color: #999999">{$msg}</div>
{else /}
<div style="text-align: center;font-size: 20px;font-weight: 300;color: #0099FF">应用未配置模板消息</div>
{/if}

<div style="text-align: center;font-size: 16px;font-weight: 300;margin-top: 10px">返回关注公众号接收消息通知</div>
<div style="text-align: center;margin: 40px;">
    <button onclick="open_url()" type="button" class="layui-btn layui-btn-normal">确定</button>
</div>
<script src="./static/layui/layui.js"></script>
<script src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"></script>
<script>
    function open_url(){
        wx.miniProgram.navigateBack();
    }
</script>
</body>
</html>