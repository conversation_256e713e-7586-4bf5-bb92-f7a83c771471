{extend name="/base"/}
{block name="main"}
<style>.am-modal-hd .am-close{top:-5px;right:-3px;}.w-e-menu{font-size:12px}.w-e-text,.w-e-text-container{height:500px !important}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 网盘设置
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-9 am-u-sm-offset-1">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">初始容量大小</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input id="diskSize" type="text" value="{$list.disk_size}" placeholder="请输入初始容量大小" oninput="grender(this,0)">
                            <small style="color: rgb(126, 134, 158);">
                                用户初始赠送的空间容量大小 单位 字节 ( byte ) 默认 20MB ( 20971520 bytes )
                                <i style="color: red;">已上传容量超过空间设置大小则无法上传</i>
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">文件上传大小限制</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input id="uploadSizeLimit" type="text" value="{$list.upload_size_limit}" placeholder="请输入文件上传大小限制" oninput="grender(this,0)">
                            <small style="color: rgb(126, 134, 158);">
                                文件上传大小限制 单位 字节 ( byte ) 默认 10MB ( 10485760 bytes )
                                <i style="color: red;">文件超过设置大小则无法上传</i>
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0 0 0 ;">
                        <label class="am-u-sm-3 am-form-label">禁止上传文件类型</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input id="uploadTypeLimited" type="text" value="{$list.upload_type_limited}" placeholder="请输入禁止上传文件类型">
                            <small style="color: rgb(126, 134, 158);">
                                禁止用户上传的文件类型 多个类型请用英文逗号进行分割 例如：zip,rar,7z <br/>
                                <i style="color: #005aff;">
                                    目前仅支持 gif,jpg,jpeg,bmp,png,mp3,mpeg,wav,ogg,avi,mkv,mp4,txt,doc,docx,xls,xlsx,ppt,pptx,pdf,zip,rar,7z 类型的文件上传
                                </i>
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 0 0 20px 0;">
                        <div class="am-u-sm-9 am-u-sm-offset-3" style="font-size: 14px;display: flex;flex-direction:column;justify-content: center;">
                            <div style="color: #7d0f0f;margin: 20px 0 15px 0;">
                                请前往
                                <a href="https://mp.weixin.qq.com/" target="_blank" style="color: #666;">
                                    [ 微信公众平台 ]
                                </a>
                                登录小程序 [ 左侧菜单->开发管理->开发设置->业务域名 ] 配置：业务域名
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 0 0 50px 0;">
                        <label class="am-u-sm-3 am-form-label">发帖附件图标是否隐藏</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select id="relPaperIconHide">
                                <option value="0" {if $list.rel_paper_icon_hide==0}selected{/if}>正常</option>
                                <option value="1" {if $list.rel_paper_icon_hide==1}selected{/if}>隐藏</option>
                            </select>
                            <small style="color: rgb(126, 134, 158);">
                                用户发帖时可选择是否携带附件发布 隐藏后无法选择附件
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">用户使用协议</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div id="detail" style="min-height:600px;">{$list.use_protocol}</div>
                            <span id="customizeGallery" style="display:none;" onclick="cuonice();"></span>
                        </div>
                    </div>
                    <div class="am-form-group am-u-sm-offset-1" style="text-align: center;">
                        <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script type="text/javascript" src="./static/wangEditor/wangEditor.min.js?time={:time()}"></script>
<link rel="stylesheet" href="./static/wangEditor/wangEditor-fullscreen-plugin.css?time={:time()}">
<script type="text/javascript" src="./static/wangEditor/wangEditor-fullscreen-plugin.js?time={:time()}"></script>
<script>

    var E = window.wangEditor;
    var editor = new E('#detail');
    editor.customConfig.uploadImgServer = true;
    editor.create();
    E.fullscreen.init('#detail');


    var grender = function (obj, limit) {
        if (limit === 2) {
            obj.value = obj.value.replace(/[^\d.]/g, "");
            obj.value = obj.value.replace(/^\./g, "");
            obj.value = obj.value.replace(/\.{2,}/g, ".");
            obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        } else {
            obj.value = Number((obj.value.match(/^\d+(?:\.\d{0})?/)));
        }
    }

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
    }

    function holdSave() {
        var setData = {};
        setData['diskSize'] = Number($('#diskSize').val());
        setData['uploadSizeLimit'] = Number($('#uploadSizeLimit').val());
        setData['uploadTypeLimited'] = $.trim($('#uploadTypeLimited').val());
        setData['relPaperIconHide'] = Number($('#relPaperIconHide').val());
        setData['protocol'] = $.trim(editor.txt.html());
        $.ajax({
            type: "post",
            url: "{:url('cloud/dictate')}",
            data: {'setData': setData},
            dataType: 'json',
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            }
        });
    }
</script>
{/block}