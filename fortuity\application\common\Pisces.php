<?php

namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

use think\Db;

#模板验证
class Pisces extends Gyration {

    //请求
    public static function slothful($much_id) {
        $combinationInfo = Db::name('combination')->where('much_id', $much_id)->find();
        if (!$combinationInfo) {
            $combinationInfo = [
                'home' => Remotely::templetEncode('0'),
                'plaza' => Remotely::templetEncode('0'),
                'goods' => Remotely::templetEncode('0'),
                'user' => Remotely::templetEncode('0'),
                'much_id' => $much_id
            ];
            Db::startTrans();
            try {
                $combinationInfo['id'] = Db::name('combination')->insertGetId($combinationInfo);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
            cache('market_' . $much_id, $combinationInfo);
        } else {
            $contrar = Suspense::fluorescent();
            $data['randCode'] = $contrar['rand_code'];
            $data['nucleus'] = Suspense::getRandomCode();
            $data['quality'] = 0;
            $data['home'] = $combinationInfo['home'];
            $data['plaza'] = $combinationInfo['plaza'];
            $data['goods'] = $combinationInfo['goods'];
            $data['user'] = $combinationInfo['user'];
            try {
                $markURL = 'https://geek.inotnpc.com/index.php?s=/unveil/aptness/heeled.shtml';
                $contrar = Suspense::fluorescent();
                $surge['randCode'] = $contrar['rand_code'];
                $surge['nucleus'] = Suspense::getRandomCode();
                $surge['quality'] = 0;
                $result = json_decode(self::_requestPost($markURL, $data), true);
            } catch (\Exception $e) {
                $result = null;
            }
            if ($result !== null) {
                Db::startTrans();
                try {
                    Db::name('combination')->where('much_id', $much_id)->update($result);
                    cache('market_' . $much_id, $result);
                    Db::commit();
                } catch (\Exception $e) {
                    cache('market_' . $much_id, $combinationInfo);
                    Db::rollback();
                }
            } else {
                cache('market_' . $much_id, $combinationInfo);
            }
        }
    }
}