{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-adn"></span> 流量主
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-form am-form-horizontal" style="width: 670px;height:790px;margin: 0px auto;box-shadow: 0px 0px 10px 0px black;">
                    <div class="am-form-group" style="padding-top:40px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">圈子广告</label>
                        <div class="am-u-sm-8 am-u-end" style="margin-top:3px;">
                            <div style="width:100%;display:flex;align-items:center;">
                                <label>
                                    <input type="radio" name="adstory" value="0" {if $list.adstory==0}checked{/if}>
                                    <span style="font-size:14px;font-weight:initial;">关闭</span>
                                </label>
                                <label style="margin-left:20px;">
                                    <input type="radio" name="adstory" value="1"  {if $list.adstory==1}checked{/if}>
                                    <span style="font-size:14px;font-weight:initial;">开启</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding-top:10px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">帖子广告</label>
                        <div class="am-u-sm-8 am-u-end" style="margin-top:3px;">
                            <div style="width:100%;display:flex;align-items:center;">
                                <label>
                                    <input type="radio" name="adsper" value="0" {if $list.adsper==0}checked{/if}>
                                    <span style="font-size:14px;font-weight:initial;">关闭</span>
                                </label>
                                <label style="margin-left:20px;">
                                    <input type="radio" name="adsper" value="1"  {if $list.adsper==1}checked{/if}>
                                    <span style="font-size:14px;font-weight:initial;">开启</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0px 0px 0px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">广告相隔行数</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="number" id="isolate" value="{$list.isolate}" placeholder="请输入广告相隔行数">
                            <small>每相隔多少行 ( 数据 ) 显示一条广告</small>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">Banner ID</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="adunitId" value="{$list.adunit_id}" placeholder="请输入Banner流量主ID">
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0px 0px 0px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">激励式 ID</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="incentiveId" value="{$list.incentive_id}" placeholder="请输入激励式流量主ID">
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0px 0px 0px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">激励次数限制</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="number" id="incentiveDuct" value="{$list.incentive_duct}" placeholder="请输入激励式广告次数限制">
                            <small>用户每日浏览激励式广告的奖励次数限制 0为不限制 默认每日5次</small>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding-top:10px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">贴片广告</label>
                        <div class="am-u-sm-8 am-u-end" style="margin-top:3px;">
                            <div style="width:100%;display:flex;align-items:center;">
                                <label>
                                    <input type="radio" name="prePostTwig" value="0" {if $list.pre_post_twig==0}checked{/if}>
                                    <span style="font-size:14px;font-weight:initial;">关闭</span>
                                </label>
                                <label style="margin-left:20px;">
                                    <input type="radio" name="prePostTwig" value="1"  {if $list.pre_post_twig==1}checked{/if}>
                                    <span style="font-size:14px;font-weight:initial;">开启</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">视频贴片 ID</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="prePostId" value="{$list.pre_post_id}" placeholder="请输入视频贴片流量主ID">
                        </div>
                    </div>

                    <div class="am-form-group" style="padding-top:10px;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">原生模版广告</label>
                        <div class="am-u-sm-8 am-u-end" style="margin-top:3px;">
                            <div style="width:100%;display:flex;align-items:center;">
                                <label>
                                    <input type="radio" name="latticeTwig" value="0" {if $list.lattice_twig==0}checked{/if}>
                                    <span style="font-size:14px;font-weight:initial;">关闭</span>
                                </label>
                                <label style="margin-left:20px;">
                                    <input type="radio" name="latticeTwig" value="1"  {if $list.lattice_twig==1}checked{/if}>
                                    <span style="font-size:14px;font-weight:initial;">开启</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0;margin-left:50px;">
                        <label class="am-u-sm-3 am-form-label">原生模版广告 ID</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="latticeId" value="{$list.lattice_id}" placeholder="请输入原生模版广告流量主ID">
                            <small>仅在圈子内展示</small>
                        </div>
                    </div>

                    <div class="am-form-group" style="margin-top:20px;">
                        <div class="am-u-sm-7 am-u-sm-push-5">
                            <button type="button" class="am-btn am-btn-secondary" style="border-radius: 5px;" onclick="holdSave();">保存设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>


    var holdSave = function () {

        var rangeData = {};
        rangeData['adstory'] = $('[name=adstory]:checked').val();
        rangeData['adsper'] = $('[name=adsper]:checked').val();
        rangeData['prePostTwig'] = $('[name=prePostTwig]:checked').val();
        rangeData['isolate'] = Number($('#isolate').val().match(/^\d+(?:\.\d{0,0})?/));
        rangeData['adunitId'] = $.trim($('#adunitId').val());
        rangeData['prePostId'] = $.trim($('#prePostId').val());
        rangeData['incentiveDuct'] = Number($('#incentiveDuct').val().match(/^\d+(?:\.\d{0,0})?/));
        rangeData['incentiveId'] = $.trim($('#incentiveId').val());
        rangeData['latticeTwig'] = $('[name=latticeTwig]:checked').val();
        rangeData['latticeId'] = $.trim($('#latticeId').val());

        $.ajax({
            type: "post",
            url: "{:url('systems/proclaim')}",
            data: rangeData,
            dataType: 'json',
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }
        });
    }
</script>
{/block}