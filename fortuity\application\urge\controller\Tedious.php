<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\Util;
use app\common\Playful;
use app\common\Remotely;
use app\common\Tools;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

class Tedious extends Base
{
    public function emoji()
    {
        //$page = input('get.page', 1);
        $type = input('get.type');
        //查询有几个表情
        $absLocalRes = ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'expression';
        //$chunk_result = [];
        $arr = [];
        if (is_dir($absLocalRes)) {
            $arr = array_merge(array_diff(scandir($absLocalRes), ['.', '..']));
            //$chunk_result = array_chunk($arr, 10);
        }
        //$emojiList = $chunk_result[$page - 1];
        $this->assign('emojiList', $arr);
        $this->assign('type', $type);
        return $this->fetch();
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    /*
     * 公共商品规格
     */
    private function common_shop_specs($listRows = 10)
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $list = Db::name('shop_attribute')
            ->where('at_name', 'like', "%{$hazy_name}%")
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate($listRows, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        if ($page > 1 && $list->count() < 1) {
            $this->emptyDataRedirect(['hazy_name' => $hazy_name]);
        }
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 商品规格
     */
    public function shop_specs()
    {
        return $this->common_shop_specs();
    }

    /*
     * 添加商品规格
     */
    public function add_shop_specs()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['at_name'] = trim(input('post.atName'));
            if ($data['at_name'] === '') {
                return json(['code' => 0, 'msg' => '规格名称不能为空']);
            }
            $data['create_time'] = time();
            $data['much_id'] = $this->much_id;
            $saInfo = Db::name('shop_attribute')->where('at_name', $data['at_name'])->where('much_id', $this->much_id)->find();
            if ($saInfo) {
                return json(['code' => 0, 'msg' => '规格名称已存在，请勿重复添加！']);
            }
            Db::startTrans();
            try {
                Db::name('shop_attribute')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 修改商品规格
     */
    public function update_shop_specs()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $data['at_name'] = trim(input('post.atName'));
            $saInfo = Db::name('shop_attribute')->where('id', '<>', $fid)->where('at_name', $data['at_name'])->where('much_id', $this->much_id)->find();
            if ($saInfo) {
                return json(['code' => 0, 'msg' => '规格名称已存在，请勿重复保存！']);
            }
            Db::startTrans();
            try {
                Db::name('shop_attribute')->where('id', $fid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 删除商品规格
     */
    public function del_shop_specs()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            Db::startTrans();
            try {
                Db::name('shop_attribute')->where('id', $fid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 选择商品规格
     */
    public function select_shop_specs()
    {
        return $this->common_shop_specs(5);
    }

    /*
     * 抽奖列表
     */
    public function contest_list()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5bm46L+Q5oq95aWW'))) {
            abort(404);
        }
        $url = $this->defaultQuery();
        $fid = trim(input('get.fid'));
        $lotteryName = trim(input('get.lotteryName'));
        $startTime = trim(input('get.startTime'));
        $endTime = trim(input('get.endTime'));
        $isWinning = trim(input('get.isWinning'));

        $when = [];
        if (intval($fid) !== 0) {
            $when[] = function ($query) use ($fid) {
                $query->where('id', '=', $fid);
            };
        }
        if ($lotteryName !== '') {
            $when[] = function ($query) use ($lotteryName) {
                $query->where('lottery_name', 'like', "%{$lotteryName}%");
            };
        }
        if (intval($startTime) !== 0) {
            $when[] = function ($query) use ($startTime) {
                $query->where('start_time', '>=', intval(floatval($startTime) / 1000));
            };
        }
        if (intval($endTime) !== 0) {
            $when[] = function ($query) use ($endTime) {
                $query->where('end_time', '<=', intval(floatval($endTime) / 1000));
            };
        }
        if ($isWinning !== '' && intval($isWinning) !== -1) {
            $when[] = function ($query) use ($isWinning) {
                $query->where('is_winning', '=', $isWinning);
            };
        }

        $list = Db::name('sweepstake_list')
            ->where($when)
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->orderRaw('CASE WHEN draw_time < UNIX_TIMESTAMP() THEN draw_time ELSE UNIX_TIMESTAMP() + 1 END DESC, start_time ASC, end_time ASC')
            ->paginate(10, false, ['query' => ['s' => $url, 'fid' => $fid, 'lotteryName' => $lotteryName, 'startTime' => $startTime, 'endTime' => $endTime, 'isWinning' => $isWinning]])
            ->each(function ($item) {
                $item['participant_num'] = Db::name('sweepstake_participate')->where('sp_id', $item['id'])->where('much_id', $this->much_id)->count();
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('fid', $fid);
        $this->assign('lotteryName', $lotteryName);
        $this->assign('startTime', $startTime);
        $this->assign('endTime', $endTime);
        $this->assign('isWinning', $isWinning);
        $page = request()->get('page', 1);
        if ($page > 1 && $list->count() < 1) {
            $this->emptyDataRedirect(['lotteryName' => $lotteryName, 'startTime' => $startTime, 'endTime' => $endTime]);
        }
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 新增或编辑抽奖列表
     */
    public function new_or_edit_contest_list()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5bm46L+Q5oq95aWW'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $postData = input('post.');
            $fid = intval($postData['fid']);
            $slInfo = Db::name('sweepstake_list')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->field('id')->find();
            $data['lottery_name'] = trim($postData['lotteryName']);
            $data['prize_list'] = json_encode($postData['prizeList'], 320);
            $data['free_entry_count'] = intval($postData['freeEntryCount']);
            $data['video_entry_count'] = intval($postData['videoEntryCount']);
            $data['participant_num_limit'] = intval($postData['participantNumLimit']);
            $data['is_group'] = intval($postData['isGroup']);
            $data['extract_type'] = intval($postData['extractType']);
            $data['random_extract_range_start'] = intval($postData['randomExtractRangeStart']);
            $data['random_extract_range_end'] = intval($postData['randomExtractRangeEnd']);
            $data['start_time'] = intval(floatval($postData['startTime']) / 1000);
            $data['end_time'] = intval(floatval($postData['endTime']) / 1000);
            $data['draw_time'] = intval(floatval($postData['drawTime']) / 1000);
            $data['campaign_desc'] = $this->safe_html($postData['campaignDesc']);
            if ($fid === 0) {
                $data['is_del'] = 0;
                $data['much_id'] = $this->much_id;
            }
            Db::startTrans();
            try {
                if (!$slInfo) {
                    Db::name('sweepstake_list')->insert($data);
                } else {
                    Db::name('sweepstake_list')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->update($data);
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $fid = intval(input('get.fid'));
            $slInfo = Db::name('sweepstake_list')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->field('id,is_del,much_id', true)->find();
            if ($slInfo) {
                $slInfo['prize_list'] = json_decode($slInfo['prize_list'], true);
                $slInfo['start_time'] = $slInfo['start_time'] * 1000;
                $slInfo['end_time'] = $slInfo['end_time'] * 1000;
                $slInfo['draw_time'] = $slInfo['draw_time'] * 1000;
                $this->assign('isEdit', 1);
                $this->assign('list', base64_encode(rawurlencode(json_encode(Tools::HumpUnderlineConversion($slInfo, 1), true))));
            }
            return $this->fetch();
        }
    }

    /*
     * 抽奖列表立即开奖
     */
    public function now_draw_contest_list()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5bm46L+Q5oq95aWW'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $slInfo = Db::name('sweepstake_list')->where('id', $fid)->where('is_winning', 0)->where('is_del', 0)->where('much_id', $this->much_id)->field('id')->find();
            if (!$slInfo) {
                return json(['code' => 0, 'msg' => '抽奖信息不存在或已开奖！']);
            }
            $util = new Util();
            $result = $util->insKai(['id' => $fid, 'now' => 1, 'much_id' => $this->much_id]);
            if ($result['code'] > 0) {
                Db::startTrans();
                try {
                    Db::name('sweepstake_list')->where('id', $fid)->where('is_del', 0)->update(['is_winning' => 1, 'draw_time' => time()]);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
                return json(['code' => 1, 'msg' => '操作成功！']);
            }
            return json($result);
        } else {
            abort(404);
        }
    }

    /*
     * 抽奖列表查看开奖结果
     */
    public function draw_contest_list_result()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5bm46L+Q5oq95aWW'))) {
            abort(404);
        }
        $fid = intval(input('get.fid'));
        $slInfo = Db::name('sweepstake_winning')->where('sp_id', $fid)->where('much_id', $this->much_id)->field('id,sp_id,much_id', true)->find();
        if ($slInfo) {
            $slInfo['prize_outcome'] = base64_encode(rawurlencode($slInfo['prize_outcome']));
            $this->assign('list', $slInfo);
            return $this->fetch();
        } else {
            abort(404);
        }
    }

    /*
     * 删除抽奖列表
     */
    public function delete_contest_list()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5bm46L+Q5oq95aWW'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            Db::startTrans();
            try {
                Db::name('sweepstake_list')->where('id', $fid)->where('is_del', 0)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 抽奖参与列表
     */
    public function involved_contest()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5bm46L+Q5oq95aWW'))) {
            abort(404);
        }
        $url = $this->defaultQuery();
        $uid = trim(input('get.uid'));
        $lotteryName = trim(input('get.lotteryName'));
        $luckyNumber = trim(input('get.luckyNumber'));
        $isAward = trim(input('get.isAward'));
        $isPayoutPrizes = trim(input('get.isPayoutPrizes'));
        $startTime = trim(input('get.startTime'));
        $endTime = trim(input('get.endTime'));

        $when = [];
        if (intval($uid) !== 0) {
            $when[] = function ($query) use ($uid) {
                $query->where('user_id', '=', intval($uid));
            };
        }
        if ($lotteryName !== '') {
            $swList = Db::name('sweepstake_list')->where('lottery_name', 'like', "%{$lotteryName}%")->where('much_id', $this->much_id)->field('id')->select();
            $fids = [];
            for ($i = 0; $i < count($swList); $i++) {
                $fids[] = $swList[$i]['id'];
            }
            $when[] = function ($query) use ($fids) {
                $query->where('id', 'in', $fids);
            };
        }
        if ($luckyNumber !== '') {
            $isFirstOneEnglish = ctype_alpha($luckyNumber[0]);
            $awardCategories = 0;
            if ($isFirstOneEnglish) {
                $awardCategories = abs(ord(strtoupper($luckyNumber[0])) - 65);
            }
            $when[] = function ($query) use ($luckyNumber, $isFirstOneEnglish, $awardCategories) {
                if ($isFirstOneEnglish) {
                    $luckyNumber = substr($luckyNumber, 1);
                    $query->where('award_categories', '=', $awardCategories)->where('lucky_number', 'like', "%{$luckyNumber}%");
                } else {
                    $query->where('lucky_number', 'like', "%{$luckyNumber}%");
                }
            };
        }
        if ($isAward !== '' && intval($isAward) !== -1) {
            $when[] = function ($query) use ($isAward) {
                $query->where('is_award', '=', $isAward);
            };
        }
        if ($isPayoutPrizes !== '' && intval($isPayoutPrizes) !== -1) {
            $when[] = function ($query) use ($isPayoutPrizes) {
                $query->where('is_payout_prizes', '=', $isPayoutPrizes);
            };
        }
        if (intval($startTime) !== 0) {
            $swList = Db::name('sweepstake_list')->where('start_time', '>=', intval(floatval($startTime) / 1000))->where('much_id', $this->much_id)->field('id')->select();
            $fids = [];
            for ($i = 0; $i < count($swList); $i++) {
                $fids[] = $swList[$i]['id'];
            }
            $when[] = function ($query) use ($fids) {
                $query->where('id', 'in', $fids);
            };
        }
        if (intval($endTime) !== 0) {
            $swList = Db::name('sweepstake_list')->where('end_time', '<=', intval(floatval($endTime) / 1000))->where('much_id', $this->much_id)->field('id')->select();
            $fids = [];
            for ($i = 0; $i < count($swList); $i++) {
                $fids[] = $swList[$i]['id'];
            }
            $when[] = function ($query) use ($fids) {
                $query->where('id', 'in', $fids);
            };
        }

        $list = Db::name('sweepstake_participate')
            ->where($when)
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'uid' => $uid, 'lotteryName' => $lotteryName, 'luckyNumber' => $luckyNumber, $isAward => 'isAward', $isPayoutPrizes => 'isPayoutPrizes', 'startTime' => $startTime, 'endTime' => $endTime]])
            ->each(function ($item) {
                $item['slInfo'] = Db::name('sweepstake_list')->where('id', $item['sp_id'])->where('much_id', $this->much_id)->field('lottery_name,is_group,start_time,end_time,draw_time')->find();
                $item['user'] = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('uid', $uid);
        $this->assign('lotteryName', $lotteryName);
        $this->assign('luckyNumber', $luckyNumber);
        $this->assign('isAward', $isAward);
        $this->assign('isPayoutPrizes', $isPayoutPrizes);
        $this->assign('startTime', $startTime);
        $this->assign('endTime', $endTime);
        $page = request()->get('page', 1);
        if ($page > 1 && $list->count() < 1) {
            $this->emptyDataRedirect(['uid' => $uid, 'startTime' => $startTime, 'endTime' => $endTime]);
        }
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 抽奖参与中奖用户设置派奖
     */
    public function deliver_rewards_contest_involved()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5bm46L+Q5oq95aWW'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $action = intval(input('post.action'));
            Db::startTrans();
            try {
                Db::name('sweepstake_participate')->where('id', $fid)->where('much_id', $this->much_id)->update(['is_payout_prizes' => $action !== 1, 'update_time' => time()]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 抽奖参与列表获取备注
     */
    public function get_contest_involved_remark()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5bm46L+Q5oq95aWW'))) {
            abort(404);
        }
        if (request()->isAjax()) {
            $fid = intval(input('get.fid'));
            $remark = Db::name('sweepstake_participate')->where('id', $fid)->where('much_id', $this->much_id)->value('memo');
            return json(['info' => $remark]);
        } else {
            abort(404);
        }
    }

    /*
     * 抽奖参与列表修改备注
     */
    public function update_contest_involved_remark()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5bm46L+Q5oq95aWW'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid'));
            $content = $this->safe_html(trim(input('post.content')));
            Db::startTrans();
            try {
                Db::name('sweepstake_participate')->where('id', $fid)->where('much_id', $this->much_id)->update(['memo' => $content, 'update_time' => time()]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 抽奖配置信息
     */
    public function contest_config()
    {
        if (!Remotely::isUnLockProperty(base64_decode('5bm46L+Q5oq95aWW'))) {
            abort(404);
        }
        if (request()->isPost() && request()->isAjax()) {
            $postData = input('post.item/a');
            $data['custom_title'] = $postData['customTitle'];
            $data['ad_1'] = $postData['AD1'];
            Db::startTrans();
            try {
                $scInfo['id'] = Db::name('sweepstake_config')->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $scInfo = Db::name('sweepstake_config')->where('much_id', $this->much_id)->find();
            if (!$scInfo) {
                $scInfo = ['custom_title' => '幸运抽奖', 'ad_1' => '', 'much_id' => $this->much_id];
                Db::startTrans();
                try {
                    $scInfo['id'] = Db::name('sweepstake_config')->insertGetId($scInfo);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
            }
            $this->assign('list', $scInfo);
            return $this->fetch();
        }
    }
}