{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-gift {margin-right: 5px;color: #e91e63;}
    .tax-form-container {max-width: 700px;margin: 40px auto;background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);border-radius: 16px;border: 1px solid #e8e8e8;padding: 60px 50px;box-shadow: 0 8px 32px rgba(0,0,0,0.08);position: relative;overflow: hidden;}
    .tax-form-container::before {content: '';position: absolute;top: 0;left: 0;right: 0;height: 4px;background: linear-gradient(90deg, #23b7e5 0%, #e91e63 100%);}
    .form-group {margin-bottom: 40px;}
    .form-label {font-size: 16px;color: #333;font-weight: 600;margin-bottom: 15px;display: flex;align-items: center;gap: 8px;}
    .form-label::before {content: '💰';font-size: 18px;}
    .input-section {background: #fff;border-radius: 12px;padding: 25px;border: 1px solid #e8e8e8;box-shadow: 0 2px 8px rgba(0,0,0,0.04);margin-bottom: 25px;}
    .input-container {position: relative;display: flex;align-items: center;gap: 15px;}
    .tax-input {flex: 1;height: 48px;padding: 0 16px;border: 2px solid #e8e8e8;border-radius: 8px;background: #fff;transition: all 0.3s;font-size: 16px;font-weight: 500;}
    .tax-input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 3px rgba(35,183,229,0.1);outline: none;}
    .percent-label {color: #23b7e5;font-size: 20px;font-weight: 600;min-width: 30px;background: #f0f8ff;padding: 8px 12px;border-radius: 6px;}
    .help-text {margin-top: 20px;padding: 25px;background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);border-radius: 12px;border: 1px solid #e3f2fd;position: relative;}
    .help-text::before {content: '💡';position: absolute;top: 15px;left: 20px;font-size: 20px;}
    .help-text {padding-left: 55px;}
    .help-text h4 {margin: 0 0 15px 0;font-size: 15px;color: #23b7e5;font-weight: 600;}
    .help-text p {margin: 0 0 10px 0;font-size: 14px;color: #555;line-height: 1.6;}
    .help-text p:last-child {margin-bottom: 0;}
    .highlight-text {color: #e91e63;font-weight: 600;background: rgba(233,30,99,0.1);padding: 2px 6px;border-radius: 4px;}
    .save-btn {background: linear-gradient(135deg, #23b7e5 0%, #1a9bc0 100%);color: #fff;border: none;padding: 16px 32px;border-radius: 8px;font-size: 16px;font-weight: 600;cursor: pointer;transition: all 0.3s;min-width: 160px;box-shadow: 0 4px 16px rgba(35,183,229,0.3);}
    .save-btn:hover {background: linear-gradient(135deg, #1a9bc0 0%, #17a2b8 100%);transform: translateY(-2px);box-shadow: 0 8px 24px rgba(35,183,229,0.4);}
    .save-btn:active {transform: translateY(0);}
    .btn-container {text-align: center;margin-top: 40px;}
    @media (max-width: 768px) {
        .tax-form-container {margin: 0 10px;padding: 20px;}
        .input-container {flex-direction: column;align-items: stretch;gap: 5px;}
        .percent-label {text-align: center;}
    }
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-gift"></span> 礼物税率
        </div>
    </div>
    <div class="tpl-block">
        <div class="tax-form-container">
            <div class="form-group">
                <label class="form-label">平台扣除礼物税率</label>
                <div class="input-section">
                    <div class="input-container">
                        <input type="number" class="tax-input" id="taxing" value="{$list.taxing*100}" placeholder="请输入礼物税率（0-100）">
                        <span class="percent-label">%</span>
                    </div>
                </div>
                <div class="help-text">
                    <h4>💰 收益计算说明</h4>
                    <p><strong>计算方式（例如当前税率为 50%）：</strong></p>
                    <p>获赠{$defaultNavigate.currency}（税后）= 获赠{$defaultNavigate.currency} - 获赠{$defaultNavigate.currency} × 50%（税率）</p>
                    <p class="highlight-text">⚠️ 用户最终所获赠的{$defaultNavigate.confer}将保留小数点后两位，其余四舍五入</p>
                </div>
            </div>
            <div class="btn-container">
                <button type="button" class="save-btn" onclick="holdSave();">保存设置</button>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    var selock = false;

    function holdSave() {
        if (!selock) {
            var taxing = Number($('#taxing').val().match(/^\d+(?:\.\d{0,2})?/));
            if (taxing == 0) {
                $('#taxing').val('0');
            }
            if (taxing > 100 || taxing < 0) {
                layer.msg('很抱歉，礼物税率不能小于 0% 或者大于 100% ');
                return;
            }
            selock = true;
            $.ajax({
                type: "post",
                url: "{:url('marketing/taxing')}",
                data: {
                    'usid': '{$list.id}',
                    'taxing': taxing
                },
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                            selock = false;
                        });
                    }
                }
            });
        }
    }
</script>
{/block}