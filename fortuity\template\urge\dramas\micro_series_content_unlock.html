{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-unlock-alt {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .user-link {color: #495057;text-decoration: none;font-weight: 500;}
    .user-link:hover {color: #343a40;text-decoration: underline;}
    .drama-link {color: #23b7e5;text-decoration: none;font-weight: 500;}
    .drama-link:hover {color: #1a9bc0;text-decoration: underline;}
    .episode-number {font-weight: 600;color: #333;}
    .unlock-type {display: inline-block;padding: 3px 8px;border-radius: 12px;font-size: 11px;font-weight: 500;}
    .unlock-free {background-color: #d4edda;color: #155724;border: 1px solid #c3e6cb;}
    .unlock-shell {background-color: #fff3cd;color: #856404;border: 1px solid #ffeaa7;}
    .unlock-points {background-color: #cce5ff;color: #004085;border: 1px solid #b3d7ff;}
    .unlock-ads {background-color: #f8d7da;color: #721c24;border: 1px solid #f5c6cb;}
    .price-info {font-weight: 600;color: #e67e22;font-size: 14px;}
    .profit-ratio {font-weight: 600;color: #27ae60;font-size: 14px;}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-unlock-alt"></span> 短剧解锁记录
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="commonSearch();"></i>
                <input type="text" id="searchName" value="{$searchName|default=''}" placeholder="搜索用户或短剧名称...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="15%">用户信息</th>
                            <th width="20%">短剧名称</th>
                            <th width="10%">集数</th>
                            <th width="15%">解锁类型</th>
                            <th width="10%">解锁价格</th>
                            <th width="15%">平台抽成</th>
                            <th width="15%">解锁时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                {if $vo.user.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user.user_wechat_open_id}&page=1" title="{$vo.user.user_nick_name|emoji_decode}" target="_blank" class="user-link">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&searchName={$vo.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user.user_nick_name|emoji_decode}" class="user-link">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                <a href="{:url('dramas/micro_series_info')}&searchName={$vo.msi_title}" target="_self" class="drama-link">
                                    {$vo.msi_title}
                                </a>
                            </td>
                            <td class="am-text-middle">
                                <span class="episode-number">第{$vo.msc_episode_number}集</span>
                            </td>
                            <td class="am-text-middle">
                                {switch $vo.unlock_type}
                                {case 0}
                                <span class="unlock-type unlock-free">每日免费</span>
                                {/case}
                                {case 1}
                                <span class="unlock-type unlock-shell">贝壳解锁</span>
                                {/case}
                                {case 2}
                                <span class="unlock-type unlock-points">积分解锁</span>
                                {/case}
                                {case 3}
                                <span class="unlock-type unlock-ads">广告解锁</span>
                                {/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                <span class="price-info">{$vo.unlock_price}</span>
                            </td>
                            <td class="am-text-middle">
                                <span class="profit-ratio">{$vo.charged_profit_rake_ratio * 100}%</span>
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.unlock_time)}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>

<script>
    function commonSearch() {
        var searchName = $.trim($('#searchName').val());
        if (searchName) {
            location.href = "{:url('dramas/micro_series_content_unlock')}&searchName=" + searchName + "&page={$page|default=1}";
        } else {
            location.href = "{:url('dramas/micro_series_content_unlock')}&page={$page|default=1}";
        }
    }
</script>
{/block}