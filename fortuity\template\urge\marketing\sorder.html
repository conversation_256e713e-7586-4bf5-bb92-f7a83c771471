{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:86px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.resth button{margin-right:5px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}.cust-aver-img{width:82px;height:82px;position:absolute;top:11px;border:1px solid #cccccc;border-radius:3px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-reorder"></span> 商品订单
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div id="search-list" style="display: flex;flex-wrap: wrap;width: 100%;min-height: 38px;">
                <div class="search-wide-pitch search-inline">
                    <label class="search-label">订单编号</label>
                    <div class="search-input-inline">
                        <input type="text" name="orderNumber" value="{$orderNumber}" class="search-input">
                    </div>
                </div>
                <div class="search-wide-pitch search-inline">
                    <label class="search-label">商品名称</label>
                    <div class="search-input-inline">
                        <input type="text" name="name" value="{$name}" class="search-input">
                    </div>
                </div>
                <div class="search-wide-pitch search-inline">
                    <label class="search-label">UID</label>
                    <div class="search-input-inline">
                        <input type="text" name="uid" value="{$uid}" class="search-input">
                    </div>
                </div>
                <button class="search-wide-pitch search-btn" onclick="turtle();">
                    <i class="am-icon-search"></i> 搜 索
                </button>
            </div>
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-btn-toolbar" style="margin: 10px 0px 5px 20px;">
                    <div class="am-btn-group am-btn-group-xs resth">
                        <a href="{:url('marketing/sorder')}&egon=0&orderNumber={$orderNumber}&name={$name}&uid={$uid}" class="cust-btn cust-btn-one {if $egon==0}cust-btn-activate{/if}">全部</a>
                        <a href="{:url('marketing/sorder')}&egon=1&orderNumber={$orderNumber}&name={$name}&uid={$uid}" class="cust-btn {if $egon==1}cust-btn-activate{/if}">待发货</a>
                        <a href="{:url('marketing/sorder')}&egon=2&orderNumber={$orderNumber}&name={$name}&uid={$uid}" class="cust-btn {if $egon==2}cust-btn-activate{/if}">已发货</a>
                        <a href="{:url('marketing/sorder')}&egon=3&orderNumber={$orderNumber}&name={$name}&uid={$uid}" class="cust-btn {if $egon==3}cust-btn-activate{/if}">待退款</a>
                        <a href="{:url('marketing/sorder')}&egon=4&orderNumber={$orderNumber}&name={$name}&uid={$uid}" class="cust-btn {if $egon==4}cust-btn-activate{/if}">已完成</a>
                        <a href="{:url('marketing/sorder')}&egon=5&orderNumber={$orderNumber}&name={$name}&uid={$uid}" class="cust-btn {if $egon==5}cust-btn-activate{/if}">已退款</a>
                        <a href="{:url('marketing/sorder')}&egon=6&orderNumber={$orderNumber}&name={$name}&uid={$uid}" class="cust-btn {if $egon==6}cust-btn-activate{/if}">已取消</a>
                    </div>
                </div>
            </div>
            <!--
            <div class="am-u-sm-6 am-u-md-5" style="text-align: right;">
                {if egon==5}
                <button type="button" class="am-btn am-btn-default am-btn-xs am-btn-danger" onclick="rebatch('0');">
                    批量删除
                </button>
                {else}
                <button type="button" class="am-btn am-btn-default am-btn-xs am-btn-danger" onclick="rebatch('1');">
                    批量彻底删除
                </button>
                {/if}
            </div>
            -->
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover table-main">
                        <thead>
                        <tr>
                            <th width="11.11%">订单编号</th>
                            <th width="11.11%">商品缩略图</th>
                            <th width="11.11%">商品名称</th>
                            <th width="11.11%">购买用户</th>
                            <th width="11.11%">付款价格</th>
                            <th width="11.11%">支付状态</th>
                            <th width="11.11%">交易状态</th>
                            <th width="11.11%">创建时间</th>
                            <th width="11.11%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>{$vo.order_number}</td>
                            <td style="position: relative;">
                                <img src="{$vo.product_img}" style="width: 78px;height: 78px;border-radius: 3px;margin: 1.5px">
                                <div class="cust-aver-img"></div>
                            </td>
                            <td style="white-space:nowrap;">
                                <a href="{:url('marketing/shop')}&egon=0&hazy_name={$vo.product_name}&page=1" title="{$vo.product_name}" target="_blank">
                                    {$vo.product_name|subtext=16}
                                </a>
                            </td>
                            <td>
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode|subtext=10}
                                </a>
                            </td>
                            <td>
                                {$vo.actual_price} ( {switch $vo.pay_type}{case 0}贝壳{/case}{case 1}积分{/case}{case 2}微信支付{/case}{/switch} )
                            </td>
                            <td>
                                {if $vo.pay_type==2 && $vo.pay_status==0}
                                <span style="color: red;">未支付</span>
                                {else}
                                <span style="color: green">已支付</span>
                                {/if}
                            </td>
                            <td>
                                {if $vo.status==0}
                                <span style="background: indianred; color: white;padding: 5px;border-radius: 3px;">待发货</span>
                                {elseif $vo.status==1}
                                <span style="background: darkseagreen; color: white;padding: 5px;border-radius: 3px;">已发货</span>
                                {elseif $vo.status==2}
                                <span style="background: darkorange; color: white;padding: 5px;border-radius: 3px;">待退款</span>
                                {elseif $vo.status==3}
                                {if $vo.pay_type==2 && $vo.pay_status==0}
                                <span style="background: coral; color: white;padding: 5px;border-radius: 3px;">已取消</span>
                                {else}
                                <span style="background: palevioletred; color: white;padding: 5px;border-radius: 3px;">已退款</span>
                                {/if}
                                {elseif $vo.status==4}
                                <span style="background: seagreen; color: white;padding: 5px;border-radius: 3px;">已完成</span>
                                {/if}
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.buy_time)}</td>
                            <td>
                                <button type="button" class="am-btn am-btn-default am-btn-xs am-text-secondary" onclick="unute('{$vo.id}');">订单详情
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
    <textarea id="editor" style="width: 600px;display: none;"></textarea>
</div>
{/block}
{block name="script"}
<script>


    function unute(usid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('marketing/seorder')}&usid=" + usid);
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    function turtle() {
        var searchInput = $('#search-list .search-input');
        var n = 0;
        for (var i = 0; i < searchInput.length; i++) {
            if (searchInput[i].value.trim() !== '') {
                n++;
            }
        }
        if (n > 0) {
            location.href = `{:url('marketing/sorder')}&egon={$egon}&${searchInput.serialize()}&page={$page}`;
        } else {
            location.href = "{:url('marketing/sorder')}&egon={$egon}&page={$page}";
        }
    }

</script>
{/block}