{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.customize-span{padding:3px 10px;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-photo"></span> 图片库
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}"
                           placeholder="搜索图片类型...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs" style="margin-left:-2px;">
                        <span class="customize-span" onclick="saloof();">
                            <span class="am-icon-adn"></span> 新增图库
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover table-main">
                        <thead>
                        <tr>
                            <th width="20%">排序</th>
                            <th width="20%">图片库名称</th>
                            <th width="20%">图片数量</th>
                            <th width="20%">状态</th>
                            <th width="20%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="gclassify" id="vo"}
                        <tr>
                            <td>
                                <input type="text" class="scfixed" id="sfixed-{$vo.id}" value="{$vo.scores}" data-score="{$vo.scores}" style="width: 50px;margin-top: 8px;" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                            </td>
                            <td>{$vo.name}</td>
                            <td>{$vo.count}</td>
                            <td>
                                {if $vo.status == 0}
                                    <span style="color: red;cursor: pointer;" onclick="eturvy('{$vo.id}','1');">隐藏</span>
                                {else}
                                    <span style="color: lightgreen;cursor: pointer;" onclick="eturvy('{$vo.id}','0');">正常</span>
                                {/if}
                            </td>
                            <td>
                                <div class="am-btn-toolbar">
                                    <div class="am-btn-group am-btn-group-xs">
                                        <button type="button" class="am-btn am-btn-default am-btn-xs" onclick="superImgs('{$vo.id}');">
                                            <span class="am-icon-pencil-square-o"></span> 管理
                                        </button>
                                        <button type="button" class="am-btn am-btn-default am-btn-xs am-text-danger am-hide-sm-only" onclick="navlintDel('{$vo.id}','{$vo.name}');">
                                            <span class="am-icon-trash-o"></span>
                                            删除
                                        </button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$gclassify->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    function supre(asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            var repair = exalter(asyId, dalue);
            if (repair.code > 0) {
                layer.msg(repair.msg, {icon: 1, time: 800});
                $(domId).attr('data-score', dalue);
            } else {
                layer.msg(repair.msg, {icon: 5, time: 1600});
            }
        }
    }

    function exalter(asyId, dalue) {
        var straw = [];
        $.ajax({
            type: "post",
            url: "{:url('images/sfrieng')}",
            data: {
                asyId: asyId,
                dalue: dalue
            },
            async: false,
            success: function (data) {
                straw = data;
            }
        });
        return straw;
    }

    function eturvy(euid, status) {
        $.post("{:url('images/eturvys')}", {'euid': euid, 'status': status}, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 1000});
            }
        }, 'json');
    }

    function saloof() {
        layer.open({
            type: 2,
            anim:2,
            scrollbar: true,
            area: ['550px', '230px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/newImgs')}", 'no']
        });
    }

    var lock = false;
    function navlintDel(mid, vname) {
        if (!lock) {
            lock = true;
            var shint = '您确定要 <span style="color: red">删除</span> <span style="color: blue;">' + vname + '</span> 图库吗？';
            layer.confirm(shint, {
                btn: ['确定', '取消'], 'title': '删除提示'
            }, function () {
                $.post(
                    "{:url('images/unImagesSify')}",
                    {'suid': mid},
                    function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2200}, function () {
                                lock = false;
                            });
                        }
                    }, 'json');
            }, function () {
                lock = false;
            });
        }
    }

    function superImgs(usid) {
        layer.open({
            type: 2,
            anim:2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/superviseImages')}&gclasid=" + usid, 'no']
        });
    }
    

    function fuzzy() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name)
            location.href = "{:url('images/index')}&hazy_name=" + fz_name + "&page={$page}";
        else
            location.href = "{:url('images/index')}&page={$page}";
    }

</script>
{/block}