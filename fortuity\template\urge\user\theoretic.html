{extend name="/base"/}
{block name="main"}
<style>.tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}.tpl-portlet-components:after {content: "";display: table;clear: both;}.am-form {position: relative;overflow: visible;}.portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}.caption {font-size: 16px;color: #23b7e5;font-weight: 500;}.caption .am-icon-user {margin-right: 5px;color: #23b7e5;}.tpl-portlet-input {position: relative;}.tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}.tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}.tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}.customize-span {display: inline-block;padding: 7px 15px;background: #23b7e5;color: white;border-radius: 3px;font-size: 13px;cursor: pointer;transition: all 0.3s;}.customize-span:hover {background: #1a9fd4;box-shadow: 0 2px 5px rgba(26,159,212,0.2);}.customize-span .am-icon-adn {margin-right: 5px;}.am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}.am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}.am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}.am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}.am-table > tbody > tr:hover > td {background-color: #f5fafd;}.user-avatar {width: 36px;height: 36px;border-radius: 50%;object-fit: cover;border: 1px solid #eee;}.action-btn {display: inline-block;padding: 4px 8px;background: #fff;border: 1px solid #ddd;color: #23b7e5;border-radius: 3px;font-size: 12px;cursor: pointer;transition: all 0.3s;}.action-btn:hover {border-color: #23b7e5;color: #23b7e5;background-color: #f5fafd;}.action-btn .am-icon-pencil-square-o {margin-right: 3px;}.am-btn-group {display: inline-flex;border-radius: 3px;overflow: visible;box-shadow: 0 1px 2px rgba(0,0,0,0.05);}.am-btn-group > .am-btn:first-child {margin-top: 0;border-top-right-radius: 0;border-bottom-right-radius: 0;}.am-btn-group > .am-btn {padding: 4px 10px;background: #fafafa;border: 1px solid #e8e8e8;color: #666;font-size: 12px;position: relative;height: 28px;line-height: 20px;}.am-dropdown {position: static;}.am-dropdown-toggle {padding: 4px 8px !important;background: #fafafa;border: 1px solid #e8e8e8;border-left: none;height: 28px;font-size: 12px;color: #666;}.am-dropdown-toggle:hover, .am-btn-group > .am-btn:hover {background: #f5f5f5;}.am-dropdown-content {position: absolute;top: 100%;left: 0;z-index: 1020 !important;display: none;min-width: 120px;padding: 0;margin: 0;margin-top: 5px;text-align: left;background-color: #fff;border: 1px solid #eee;border-radius: 3px;box-shadow: 0 3px 8px rgba(0,0,0,0.1);}.am-dropdown.am-active .am-dropdown-content {display: block !important;visibility: visible !important;opacity: 1 !important;}.am-dropdown-content li {border-bottom: 1px solid #f3f3f3;list-style: none;}.am-dropdown-content li:last-child {border-bottom: none;}.am-dropdown-content li a {padding: 8px 10px !important;text-align: center;font-size: 12px;color: #666 !important;transition: all 0.2s;display: block;text-decoration: none;}.am-dropdown-content li a:hover {background-color: #f5fafd;color: #23b7e5 !important;}.am-pagination {margin: 10px 0;}.am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}.am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}.am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}.am-modal-dialog {border-radius: 4px;overflow: hidden;box-shadow: 0 5px 15px rgba(0,0,0,0.2);}.am-modal-hd {background: #f9f9f9;padding: 10px 15px;border-bottom: 1px solid #eee;}.am-modal-bd {padding: 15px;}.am-form-group {margin-bottom: 15px;}.am-form-label {font-size: 13px;color: #666;font-weight: normal;}.tpl-form-input {height: 32px;padding: 6px 10px;font-size: 13px;border: 1px solid #e8e8e8;border-radius: 3px;transition: all 0.3s;}.tpl-form-input:focus {border-color: #23b7e5;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}.confirm-btn {background: #23b7e5;color: white;border: none;border-radius: 3px;padding: 6px 15px;font-size: 13px;cursor: pointer;transition: all 0.3s;}.confirm-btn:hover {background: #1a9fd4;box-shadow: 0 2px 5px rgba(26,159,212,0.2);}.am-btn-group.am-btn-group-sm {position: relative;}.am-btn-group.am-btn-group-sm .am-dropdown-content {position: absolute;top: 100%;left: auto;right: 0;}.tpl-block {position: relative;overflow: visible;}body {padding-bottom: 50px;}.am-u-sm-12 {overflow: visible;}.am-table tr:last-child .am-dropdown-content {bottom: auto;top: auto;margin-top: -120px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-user"></span> 虚拟用户
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索用户名...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <span class="customize-span" onclick="saloof();">
                        <span class="am-icon-adn"></span> 新增虚拟用户
                    </span>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped">
                        <thead>
                        <tr>
                            <th width="6%">UID</th>
                            <th width="6%">用户头像</th>
                            <th width="11%">用户昵称</th>
                            <th width="10%">用户性别</th>
                            <th width="10%">用户等级</th>
                            <th width="12%">随机码 ( 部分场景可替代openid使用 )</th>
                            <th width="15%">社交功能</th>
                            <th width="10%">添加时间</th>
                            <th width="10%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>{$vo.id}</td>
                            <td>
                                <img src="{$vo.user_head_sculpture}" class="user-avatar">
                            </td>
                            <td>
                                <span title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode|subtext=10}
                                </span>
                            </td>
                            <td>{if $vo.gender==2} 女 {else} 男 {/if}</td>
                            <td>Lv.{$vo.level}</td>
                            <td>{$vo.user_wechat_open_id}</td>
                            <td>
                                <div class="am-btn-group am-btn-group-sm" style="display: inline-flex;justify-content: center;position: relative;">
                                    <button class="am-btn" style="z-index: 1;">功能列表</button>
                                    <div class="am-dropdown" data-am-dropdown>
                                        <button class="am-btn am-dropdown-toggle" data-am-dropdown-toggle style="z-index: 1;">
                                            <span class="am-icon-caret-down"></span>
                                        </button>
                                        <ul class="am-dropdown-content" style="width: 120px;min-width: 120px;z-index: 1021;">
                                            <li>
                                                <a href="javascript:void(0);" onclick="urelease('0','{$vo.id}');">
                                                    发布帖子
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" onclick="urelease('3','{$vo.id}');">
                                                    回复帖子
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" onclick="urelease('4','{$vo.id}');">
                                                    发布秘密
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" onclick="urelease('5','{$vo.id}');">
                                                    回复秘密
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" class="euModalOpen" data-euid="{$vo.id}" data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 400, height: 295}">
                                                    赠送礼物
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.user_reg_time)}</td>
                            <td>
                                <span class="action-btn" onclick="uploof('{$vo.id}');">
                                    <span class="am-icon-pencil-square-o"></span> 编辑
                                </span>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
        <div class="am-modal-dialog">
            <div class="am-modal-hd">
                <span>赠送礼物</span>
                <a id="euModalClose" href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <input id="virtual-user" type="hidden" value="0"/>
            <div class="am-modal-bd am-form">
                <div class="am-form-group">
                    <label class="am-u-sm-4 am-form-label">受赠用户</label>
                    <div class="am-u-sm-8">
                        <input type="text" id="user-openid" oninput="extolled(this);" class="tpl-form-input" placeholder="请输入用户openid">
                        <span id="sehred" style="position: absolute;left: 0px; color: blue;font-size: 12px;">　</span>
                    </div>
                </div>
                <div class="am-form-group">
                    <label class="am-u-sm-4 am-form-label">礼物列表</label>
                    <div class="am-u-sm-8">
                        <select id="tribute-number" class="tpl-form-input">
                            {volist name="tribute" id="vo"}
                            <option value="{$vo.id}">{$vo.tr_name} {$vo.tr_conch} ( {$defaultNavigate.currency} )</option>
                            {/volist}
                        </select>
                    </div>
                </div>
                <div class="am-form-group">
                    <label class="am-u-sm-4 am-form-label">礼物数量</label>
                    <div class="am-u-sm-8">
                        <input type="text" id="tribute-quantity" oninput="digitalCheck(this);" class="tpl-form-input" placeholder="请输入赠送礼物数量">
                    </div>
                </div>
                <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top:15px;">
                    <button type="button" class="confirm-btn" onclick="sendGifts();">确定赠送</button>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    !function () {
        $('.euModalOpen').click(function () {
            var euid = $(this).attr('data-euid');
            $('#virtual-user').val(euid);
        });
        $('#euModalClose').click(function () {
            $('#virtual-user').val('0');
        });
        
        // 为最后几行的下拉菜单添加向上显示功能
        $('.am-table tr:nth-last-child(-n+3) .am-dropdown').each(function() {
            $(this).on('open.dropdown.amui', function() {
                $(this).find('.am-dropdown-content').css({
                    'bottom': '100%',
                    'top': 'auto',
                    'margin-bottom': '5px',
                    'margin-top': '0'
                });
            });
        });
    }();

    var saloof = function () {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('user/rutheoretic')}");
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    var uploof =function (uid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('user/editMaterial')}&uid=" + uid);
        dynamicUrl.attr('target', '_blank');
        dynamicUrl.get(0).click();
    }

    var urelease = function (ecosa, usid) {
        switch (ecosa) {
            case '0':
                location.href = "{:url('user/reticraphic')}&usid=" + usid;
                break;
            case '3':
                location.href = "{:url('user/reticrpaper')}&usid=" + usid;
                break;
            case '4':
                location.href = "{:url('resolve/release_mini_secrets')}&fid=" + usid;
                break;
            case '5':
                location.href = "{:url('resolve/reply_mini_secrets')}&fid=" + usid;
                break;
        }
    }

    var extolled = function (obj) {
        obj.value = $.trim(obj.value);
        if (obj.value != '') {
            $.getJSON("{:url('compass/getopenid')}&virtual=1", {"openid": obj.value}, function (data) {
                if (data.name != '') {
                    $('#sehred').css('color', 'blue');
                    $('#sehred').text(data.name);
                } else {
                    $('#sehred').css('color', 'red');
                    $('#sehred').text('\u53d7\u8d60\u7528\u6237\u0020\u006f\u0070\u0065\u006e\u0069\u0064\u0020\u586b\u5199\u9519\u8bef');
                }
            });
        }
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var sendGifts = function () {
        var sehredInfo = $.trim($('#sehred').text());
        if (sehredInfo == '\u53d7\u8d60\u7528\u6237\u0020\u006f\u0070\u0065\u006e\u0069\u0064\u0020\u586b\u5199\u9519\u8bef') {
            layer.msg('\u53d7\u8d60\u7528\u6237\u0020\u006f\u0070\u0065\u006e\u0069\u0064\u0020\u586b\u5199\u9519\u8bef');
            return;
        }
        layer.confirm('您确定要赠送礼物吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            var virtualUser = $.trim($('#virtual-user').val());
            var userOpenid = $.trim($('#user-openid').val());
            var tributeNumber = $.trim($('#tribute-number').val());
            var tributeQuantity = $.trim($('#tribute-quantity').val());
            $.ajaxSettings.async = false;
            $.post("{:url('user/virtualSendGifts')}", {
                'virtualUser': virtualUser,
                'userOpenid': userOpenid,
                'tributeNumber': tributeNumber,
                'tributeQuantity': tributeQuantity
            }, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    function fuzzy() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('user/theoretic')}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('user/theoretic')}&page={$page}";
        }
    }
</script>
{/block}