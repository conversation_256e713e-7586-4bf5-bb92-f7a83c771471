{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-tags {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .action-btn {display: inline-block;padding: 6px 12px;background: #fff;border: 1px solid #ddd;color: #23b7e5;border-radius: 3px;font-size: 12px;cursor: pointer;transition: all 0.3s;text-decoration: none;}
    .action-btn:hover {border-color: #23b7e5;color: #23b7e5;background-color: #f5fafd;text-decoration: none;}
    .action-btn.btn-success {background: #5cb85c;border-color: #5cb85c;color: #fff;}
    .action-btn.btn-success:hover {background: #449d44;border-color: #449d44;color: #fff;}
    .action-btn.btn-danger {background: #d9534f;border-color: #d9534f;color: #fff;}
    .action-btn.btn-danger:hover {background: #c9302c;border-color: #c9302c;color: #fff;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .sort-input {width: 50px;padding: 4px 6px;border: 1px solid #e8e8e8;border-radius: 3px;text-align: center;font-size: 12px;transition: all 0.3s;}
    .sort-input:focus {border-color: #23b7e5;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .status-badge {display: inline-block;padding: 3px 8px;border-radius: 12px;font-size: 11px;font-weight: 500;}
    .status-normal {background-color: #d4edda;color: #155724;border: 1px solid #c3e6cb;}
    .status-hidden {background-color: #f8d7da;color: #721c24;border: 1px solid #f5c6cb;}
    .category-name {font-weight: 500;color: #333;}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-tags"></span> 短剧类型
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="commonSearch();"></i>
                <input type="text" id="fz_name" value="{$searchName}" placeholder="搜索类型名称...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <button type="button" class="action-btn btn-success" onclick="commonAdd();">
                            <span class="am-icon-plus"></span> 新增类型
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="20%">排序</th>
                            <th width="20%">分类名称</th>
                            <th width="20%">状态</th>
                            <th width="20%">添加时间</th>
                            <th width="20%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                <input type="text" class="sort-input" id="sort-fixed-{$vo.id}" value="{$vo.sort}" data-sort="{$vo.sort}" onblur="commonSort('{$vo.id}','#sort-fixed-{$vo.id}');">
                            </td>
                            <td class="am-text-middle">
                                <span class="category-name">{$vo.name}</span>
                            </td>
                            <td class="am-text-middle">
                                {if $vo.status == 0}
                                <span class="status-badge status-hidden">隐藏</span>
                                {else}
                                <span class="status-badge status-normal">正常</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.create_time)}
                            </td>
                            <td class="am-text-middle">
                                <button type="button" class="action-btn" onclick="commonEdit('{$vo.id}');" style="margin-right: 5px;">
                                    <span class="am-icon-edit"></span> 编辑
                                </button>
                                <button type="button" class="action-btn btn-danger" onclick="commonDelete('{$vo.id}','{$vo.name}');">
                                    <span class="am-icon-trash"></span> 删除
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>
    function commonSort(fid, sortFixedId) {
        var newSortFixedId = $.trim($(sortFixedId).val());
        var oldSortFixedId = $.trim($(sortFixedId).attr('data-sort'));
        if (parseInt(newSortFixedId) > 2147483646) {
            layer.msg('排序数值太大');
            $(sortFixedId).val(oldSortFixedId);
            return false;
        }
        if (newSortFixedId !== oldSortFixedId) {
            var result = commonUpdateSort(fid, newSortFixedId);
            if (result.code > 0) {
                layer.msg(result.msg, {icon: 1, time: 800});
                $(sortFixedId).attr('data-sort', newSortFixedId);
            } else {
                layer.msg(result.msg, {icon: 5, time: 1600});
            }
        }
    }

    function commonUpdateSort(fid, newSortFixedId) {
        var result = [];
        $.ajax({
            type: "post",
            url: "{:url('dramas/micro_series_type_sort')}",
            data: {fid, sort: newSortFixedId},
            async: false,
            success: function (data) {
                result = data;
            }
        });
        return result;
    }


    function commonAdd() {
        location.href = "{:url('dramas/new_micro_series_type')}";
    }

    function commonEdit(fid) {
        location.href = "{:url('dramas/edit_micro_series_type')}&fid=" + fid;
    }


    function commonDelete(fid, vname) {
        var shiny = '您确定要删除短剧类型 <span style="color: blue;">' + vname + '</span> 吗';
        layer.confirm(shiny, {
            btn: ['确定', '取消']
        }, function () {
            $.post("{:url('dramas/del_micro_series_type')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    function commonSearch() {
        var searchName = $.trim($('#fz_name').val());
        if (searchName) {
            location.href = "{:url('dramas/micro_series_type')}&searchName=" + searchName + "&page={$page}";
        } else {
            location.href = "{:url('dramas/micro_series_type')}&page={$page}";
        }
    }

</script>
{/block}