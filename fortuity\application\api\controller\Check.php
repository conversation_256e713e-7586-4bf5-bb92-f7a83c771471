<?php


namespace app\api\controller;


use think\Controller;
use think\Db;

class Check extends Controller
{
    public function check_token()
    {
        $data = input('param.');
        //判断参数
        if (empty($data['openid'])) {
            return $this->json_rewrite(['status' => 'no', 'msg' => '未找到用户！']);
        }
        //判断参数
        if (empty($data['token'])) {
            return $this->json_rewrite(['status' => 'no', 'msg' => '未找到用户！']);
        }
        //查询openid的用户
        $user = Db::name('user')->where('user_wechat_open_id', $data['openid'])->find();
        if (empty($user)) {
            return $this->json_rewrite(['status' => 'no', 'msg' => '未找到用户！']);
        } else {
            if (strcmp($user['token'], $data['token']) == 0) {
                return $this->json_rewrite(['status' => 'success', 'msg' => '匹配！','info'=>$user]);
            } else {
                return $this->json_rewrite(['status' => 'no', 'msg' => 'token不匹配！']);
            }
        }
    }

    /**
     * 列表加密
     */
    public function json_rewrite($arr)
    {

        return base64_encode(rawurlencode(json_encode($arr)));

    }
}