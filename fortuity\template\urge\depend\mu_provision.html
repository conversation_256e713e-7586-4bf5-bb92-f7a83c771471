{extend name="/base"/}
{block name="main"}
<style>.w-e-panel-container{width:100% !important;position:absolute !important;left:0 !important;margin:0 !important;}</style>
<div id="app" class="tpl-portlet-components" v-cloak>
    <div class="portlet-title">
        <div class="caption font-green bold">
            <template v-if="{$retrofit} == 0">
                <span class="am-icon-code"></span> 新增表单
            </template>
            <template v-if="{$retrofit} == 1">
                <span class="am-icon-code"></span> 编辑表单
            </template>
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="container">
                    <div class="container-left">
                        <div class="container-left-top">
                            工具栏<span class="prompt">( 移动拖拽或左键双击 )</span>
                        </div>
                        <div class="container-left-content">
                            <draggable class="container-left-content-draggable" v-model="flexForm" :options="{ group: {name: 'cloneForm', pull:'clone', put:false}, sort:false }" @end="extensionData">
                                <transition-group>
                                    <div v-for="(item,index) in flexForm" class="controlName" :key="index" @dblclick="customFormAppend(item)">
                                        <i :class="item.icon"> {{item.text}} </i>
                                    </div>
                                </transition-group>
                            </draggable>
                        </div>
                    </div>
                    <div class="container-center">
                        <div class="container-center-top">
                            <div class="container-center-top-left">
                                <button class="container-center-top-button" @click="selectSidebar(0)">表单信息</button>
                                <button class="container-center-top-button" @click="selectSidebar(1)">表单属性</button>
                            </div>
                            <div class="container-center-top-right">
                                <button class="container-center-top-button container-center-top-button-success" @click="holdSave">
                                    保存数据
                                </button>
                            </div>
                        </div>
                        <div class="container-center-content">
                            <draggable class="container-center-content-draggable" v-model="form.customForm" animation="300" :options="{ group: {name: 'cloneForm', pull:'clone'}, sort:true }" @end="preventOffset">
                                <transition-group>
                                    <div v-for="(item,index) in form.customForm" :key="index" @click="selectComponent(index)" class="content-form" :class="{'content-form-activate':selectComponentIndex == index}">
                                        <div v-if="item.dataType == 'text'" class="content-form-child" :class="contentFormChildStyle" @click="selectSidebar(2)">
                                            <label class="content-label" :style="{ width: decent.labelWidth + 'px'}">
                                                <span>{{item.text}}</span>
                                                <span v-if="item.required" class="required">*</span>
                                            </label>
                                            <div class="content-box">
                                                <input :name="item.name" class="content-box-input" type="text" v-model="item.value" :placeholder="item.placeholder">
                                            </div>
                                            <div v-if="selectComponentIndex == index" class="content-form-activate-close" title="移除此组件" @click.stop="removeComponent(index)">
                                                <div class="content-form-activate-close-child">
                                                    <i class="am-icon-trash-o"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if="item.dataType == 'textarea'" class="content-form-child" :class="contentFormChildStyle" @click="selectSidebar(3)">
                                            <label class="content-label" :style="{ width: decent.labelWidth + 'px'}">
                                                <span>{{item.text}}</span>
                                                <span v-if="item.required" class="required">*</span>
                                            </label>
                                            <div class="content-box">
                                                <textarea :name="item.name" class="content-box-textarea" :style="{'height' : item.height + 'px'}" v-model="item.value" :placeholder="item.placeholder"></textarea>
                                            </div>
                                            <div v-if="selectComponentIndex == index" class="content-form-activate-close" title="移除此组件" @click.stop="removeComponent(index)">
                                                <div class="content-form-activate-close-child">
                                                    <i class="am-icon-trash-o"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if="item.dataType == 'radio'" class="content-form-child" :class="contentFormChildStyle" @click="selectSidebar(4)">
                                            <label class="content-label" :style="{ width: decent.labelWidth + 'px'}">
                                                <span>{{item.text}}</span>
                                                <span v-if="item.required" class="required">*</span>
                                            </label>
                                            <div class="content-box">
                                                <div v-for="cItem in item.childItems"  class="content-box-radio">
                                                    <label class="content-box-radio-label">
                                                        <input :name="item.name" type="radio" class="content-box-radio-input" v-model="item.value" :value="cItem.value">
                                                        <span class="text">{{cItem.label}}</span>
                                                    </label>
                                                </div>
                                            </div>
                                            <div v-if="selectComponentIndex == index" class="content-form-activate-close" title="移除此组件" @click.stop="removeComponent(index)">
                                                <div class="content-form-activate-close-child">
                                                    <i class="am-icon-trash-o"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if="item.dataType == 'checkbox'" class="content-form-child" :class="contentFormChildStyle" @click="selectSidebar(5)">
                                            <label class="content-label" :style="{ width: decent.labelWidth + 'px'}">
                                                <span>{{item.text}}</span>
                                                <span v-if="item.required" class="required">*</span>
                                            </label>
                                            <div class="content-box">
                                                <div v-for="cItem in item.childItems" class="content-box-checkbox">
                                                    <label class="content-box-checkbox-label">
                                                        <input :name="item.name" type="checkbox" class="content-box-checkbox-input" v-model="item.value" :value="cItem.value">
                                                        <span class="text">{{cItem.label}}</span>
                                                    </label>
                                                </div>
                                            </div>
                                            <div v-if="selectComponentIndex == index" class="content-form-activate-close" title="移除此组件" @click.stop="removeComponent(index)">
                                                <div class="content-form-activate-close-child">
                                                    <i class="am-icon-trash-o"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if="item.dataType == 'select'" class="content-form-child" :class="contentFormChildStyle" @click="selectSidebar(6)">
                                            <label class="content-label" :style="{ width: decent.labelWidth + 'px'}">
                                                <span>{{item.text}}</span>
                                                <span v-if="item.required" class="required">*</span>
                                            </label>
                                            <div class="content-box">
                                                <select :name="item.name" class="content-box-select" v-model="item.value">
                                                    <option v-for="cItem in item.childItems" :value="cItem.value">{{cItem.label}}</option>
                                                </select>
                                            </div>
                                            <div v-if="selectComponentIndex == index" class="content-form-activate-close" title="移除此组件" @click.stop="removeComponent(index)">
                                                <div class="content-form-activate-close-child">
                                                    <i class="am-icon-trash-o"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if="item.dataType == 'image'" class="content-form-child" :class="contentFormChildStyle" @click="selectSidebar(7)">
                                            <label class="content-label" :style="{ width: decent.labelWidth + 'px'}">
                                                <span>{{item.text}}</span>
                                                <span v-if="item.required" class="required">*</span>
                                            </label>
                                            <div class="content-box content-box-image-attach">
                                                <i class="content-box-image am-icon-image"></i>
                                                <span>最大上传图片数量 ( 0 / {{item.count}} )</span>
                                            </div>
                                            <div v-if="selectComponentIndex == index" class="content-form-activate-close" title="移除此组件" @click.stop="removeComponent(index)">
                                                <div class="content-form-activate-close-child">
                                                    <i class="am-icon-trash-o"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if="item.dataType == 'submit'" class="content-form-child" :class="contentFormChildStyle" @click="selectSidebar(8)">
                                            <div class="content-box content-box-button-flex">
                                                <button class="container-center-top-button content-box-button">{{item.text}}</button>
                                            </div>
                                            <div v-if="selectComponentIndex == index" class="content-form-activate-close" title="移除此组件" @click.stop="removeComponent(index)">
                                                <div class="content-form-activate-close-child">
                                                    <i class="am-icon-trash-o"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </transition-group>
                            </draggable>
                        </div>
                    </div>
                    <div class="container-right">
                        <div class="container-right-top">
                            <span v-if="selectRightSidebarIndex != -1">
                                <template v-if="selectRightSidebarIndex == 0">
                                    表单信息
                                </template>
                                <template v-else-if="selectRightSidebarIndex == 1">
                                    表单属性
                                </template>
                                <template v-else>
                                    组件设置
                                </template>
                            </span>
                            <button v-if="selectRightSidebarIndex != -1" class="container-right-top-button" @click="selectSidebar(-1)">
                                <template v-if="selectRightSidebarIndex == 0">
                                    关闭信息
                                </template>
                                <template v-else-if="selectRightSidebarIndex == 1">
                                    关闭属性
                                </template>
                                <template v-else>
                                    关闭
                                </template>
                            </button>
                        </div>
                        <div class="container-right-content">
                            <div v-show="selectRightSidebarIndex != -1">
                                <div v-show="selectRightSidebarIndex == 0">
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>认证名称</span>
                                        </label>
                                        <div class="content-box">
                                            <input v-model="form.atName" class="content-box-input" type="text" placeholder="请输入认证名称 例如：企业认证">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>认证图标</span>
                                            <small style="margin-left:5px">( 头像右下角小图标 )</small>
                                        </label>
                                        <div class="content-box">
                                            <img :src="form.atIcon" onerror="this.src='static/disappear/default.png'" @click="changePicture" style="width:80px;height:80px;cursor:pointer;">
                                            <small style="margin-left:10px;color:#bf4040;">( 建议大小 100*100px )</small>
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>表单状态</span>
                                        </label>
                                        <div class="content-box">
                                            <select v-model="form.status" class="content-box-select container-right-form-select">
                                                <option value="1">正常</option>
                                                <option value="0">关闭认证</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>赠送会员天数</span>
                                        </label>
                                        <div class="content-box">
                                            <input v-model="form.handselDay" class="content-box-input" type="number">
                                            <small class="content-box-remind">用户认证成功后赠送会员天数</small>
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>表单排序</span>
                                        </label>
                                        <div class="content-box">
                                            <input v-model="form.scores" class="content-box-input" type="number">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>认证说明</span>
                                        </label>
                                        <div class="content-box">
                                            <div id="detail" style="min-height: 420px;">{$list.introduction}</div>
                                            <span id="customizeGallery" style="display:none;" @click="cuonice"></span>
                                        </div>
                                    </div>
                                </div>
                                <template v-if="selectRightSidebarIndex == 1">
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>组件标签宽度 ( px )</span>
                                        </label>
                                        <div class="content-box">
                                            <input v-model="decent.labelWidth" class="content-box-input" type="text">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>标签对齐方式</span>
                                        </label>
                                        <div class="content-box">
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="decent.labelAlign" value="0">
                                                    <span class="text">右侧对齐</span>
                                                </label>
                                            </div>
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="decent.labelAlign" value="1">
                                                    <span class="text">顶部对齐</span>
                                                </label>
                                            </div>
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="decent.labelAlign" value="2">
                                                    <span class="text">左侧对齐</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-if="selectRightSidebarIndex == 2">
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>控件名称</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].text">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>提示语句</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].placeholder">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>默认内容</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].value">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>是否必填</span>
                                        </label>
                                        <div class="content-box container-right-form-radio">
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="form.customForm[selectComponentIndex].required" :value="true">
                                                    <span class="text container-right-form-radio-text">必填</span>
                                                </label>
                                            </div>
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="form.customForm[selectComponentIndex].required" :value="false">
                                                    <span class="text container-right-form-radio-text">选填</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-if="selectRightSidebarIndex == 3">
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>控件名称</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].text">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>提示语句</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].placeholder">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>默认内容</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].value">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>控件高度 ( px )</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].height">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>是否必填</span>
                                        </label>
                                        <div class="content-box container-right-form-radio">
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="form.customForm[selectComponentIndex].required" :value="true">
                                                    <span class="text container-right-form-radio-text">必填</span>
                                                </label>
                                            </div>
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="form.customForm[selectComponentIndex].required" :value="false">
                                                    <span class="text container-right-form-radio-text">选填</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-if="selectRightSidebarIndex == 4">
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>控件名称</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].text">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>默认选择</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].value">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>单选框选项（一行一个选项）</span>
                                        </label>
                                        <div class="content-box">
                                            <textarea class="content-box-textarea" v-model="form.customForm[selectComponentIndex].refrain" @input="customOption"></textarea>
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>是否必填</span>
                                        </label>
                                        <div class="content-box container-right-form-radio">
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="form.customForm[selectComponentIndex].required" :value="true">
                                                    <span class="text container-right-form-radio-text">必填</span>
                                                </label>
                                            </div>
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="form.customForm[selectComponentIndex].required" :value="false">
                                                    <span class="text container-right-form-radio-text">选填</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-if="selectRightSidebarIndex == 5">
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>控件名称</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].text">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>默认勾选</span>
                                        </label>
                                        <div class="content-box">
                                            <textarea class="content-box-textarea" v-model="form.customForm[selectComponentIndex].abstain" @input="customOption"></textarea>
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>多选框选项（一行一个选项）</span>
                                        </label>
                                        <div class="content-box">
                                            <textarea class="content-box-textarea" v-model="form.customForm[selectComponentIndex].refrain" @input="customOption"></textarea>
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>是否必填</span>
                                        </label>
                                        <div class="content-box container-right-form-radio">
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="form.customForm[selectComponentIndex].required" :value="true">
                                                    <span class="text container-right-form-radio-text">必填</span>
                                                </label>
                                            </div>
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="form.customForm[selectComponentIndex].required" :value="false">
                                                    <span class="text container-right-form-radio-text">选填</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-if="selectRightSidebarIndex == 6">
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>控件名称</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].text">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>默认选择</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].value">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>下拉框选项（一行一个选项）</span>
                                        </label>
                                        <div class="content-box">
                                            <textarea class="content-box-textarea" v-model="form.customForm[selectComponentIndex].refrain" @input="customOption"></textarea>
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>是否必填</span>
                                        </label>
                                        <div class="content-box container-right-form-radio">
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="form.customForm[selectComponentIndex].required" :value="true">
                                                    <span class="text container-right-form-radio-text">必填</span>
                                                </label>
                                            </div>
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="form.customForm[selectComponentIndex].required" :value="false">
                                                    <span class="text container-right-form-radio-text">选填</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-if="selectRightSidebarIndex == 7">
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>控件名称</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].text">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>最大上传图片数量</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].count">
                                        </div>
                                    </div>
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>是否必填</span>
                                        </label>
                                        <div class="content-box container-right-form-radio">
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="form.customForm[selectComponentIndex].required" :value="true">
                                                    <span class="text container-right-form-radio-text">必填</span>
                                                </label>
                                            </div>
                                            <div class="content-box-radio">
                                                <label class="content-box-radio-label">
                                                    <input type="radio" class="content-box-radio-input" v-model="form.customForm[selectComponentIndex].required" :value="false">
                                                    <span class="text container-right-form-radio-text">选填</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-if="selectRightSidebarIndex == 8">
                                    <div class="container-right-form">
                                        <label class="content-label container-right-form-label">
                                            <span>控件名称</span>
                                        </label>
                                        <div class="content-box">
                                            <input type="text" class="content-box-input" v-model="form.customForm[selectComponentIndex].text">
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<link rel="stylesheet" href="assets/css/drag-form.css?time={:time()}">
<script src="assets/js/sortable.min.js"></script>
<script src="assets/js/vuedraggable.umd.min.js"></script>
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js"></script>
<script>

    var vm = new Vue({
        el: '#app',
        components: {
            vuedraggable,
        },
        data: {
            selectRightSidebarIndex: -1,
            closeRightSidebarIndex: false,
            selectComponentIndex: -1,
            flexForm: [
                {
                    icon: 'am-icon-text-height',
                    text: '文本框',
                    required: true,
                    dataType: 'text',
                    placeholder: '',
                    value: ''
                },
                {
                    icon: 'am-icon-list-alt',
                    text: '多行文本框',
                    required: true,
                    dataType: 'textarea',
                    height: 100,
                    placeholder: '',
                    value: ''
                },
                {
                    icon: 'am-icon-dot-circle-o',
                    text: '单选框',
                    required: true,
                    dataType: 'radio',
                    childItems: [{'label': '选项一', value: '选项一'}, {'label': '选项二', value: '选项二'}],
                    refrain: '选项一\n选项二',
                    value: '选项一'
                },
                {
                    icon: 'am-icon-check-square-o',
                    text: '多选框',
                    required: true,
                    dataType: 'checkbox',
                    childItems: [{'label': '选项一', value: '选项一'}, {'label': '选项二', value: '选项二'}, {
                        'label': '选项三',
                        value: '选项三'
                    }],
                    value: ['选项一', '选项二'],
                    refrain: '选项一\n选项二\n选项三',
                    abstain: '选项一\n选项二'
                },
                {
                    icon: 'am-icon-list-ol',
                    text: '下拉选择框',
                    required: true,
                    dataType: 'select',
                    childItems: [{'label': '选项一', value: '选项一'}, {'label': '选项二', value: '选项二'}, {
                        'label': '选项三',
                        value: '选项三'
                    }, {'label': '选项四', value: '选项四'}],
                    refrain: '选项一\n选项二\n选项三\n选项四',
                    value: '选项一'
                },
                {icon: 'am-icon-image', text: '图片框', required: true, dataType: 'image', count: 9},
                {icon: 'am-icon-circle', text: '提交按钮', dataType: 'submit'},
            ],
            flexFormCount: 0,
            acid: Number('{$list.id}'),
            decent: {
                labelWidth: 100,
                labelAlign: 0
            },
            form: {
                atName: '{$list.at_name}',
                atIcon: '{$list.at_icon}',
                handselDay: Number('{$list.handsel_day}'),
                customForm: [],
                introduction: '',
                status: Number('{$list.status}'),
                scores: Number('{$list.scores}')
            },
            E: [],
            editor: []
        },
        created: function () {
            this.flexFormCount = this.flexForm.length;
            var customForm = '{$list.custom_form}';
            if (customForm !== '') {
                var data = JSON.parse(decodeURIComponent(atob('{$list.custom_form}')));
                this.decent = data[0];
                this.form.customForm = data[1];
                for (var i = 0; i < this.form.customForm.length; i++) {
                    this.form.customForm[i]['required'] = this.form.customForm[i]['required'] === 'true';
                }
            }
        },
        mounted: function () {
            this.E = window.wangEditor;
            this.editor = new this.E('#detail');
            this.editor.customConfig.uploadImgServer = true;
            this.editor.create();
            this.E.fullscreen.init('#detail');
            this.$nextTick(() => {
                $('#' + this.editor.textElemId).parent().height('400px');
            });
        },
        computed: {
            contentFormChildStyle: function () {
                var labelAlign = Number(this.decent.labelAlign);
                if (labelAlign === 1) {
                    return {
                        'content-form-child-1': true
                    }
                }
                if (labelAlign === 2) {
                    return {
                        'content-form-child-2': true
                    }
                }
            }
        },
        methods: {
            selectSidebar: function (index) {
                setTimeout(() => {
                    if (index >= -1 && index <= 1) {
                        this.selectComponentIndex = -1;
                        this.closeRightSidebarIndex = false;
                    }
                    if (!this.closeRightSidebarIndex) {
                        this.selectRightSidebarIndex = index;
                    }
                }, 0);
            },
            selectComponent: function (index) {
                if (this.selectComponentIndex === index) {
                    this.closeRightSidebarIndex = true;
                    this.selectRightSidebarIndex = -1;
                    this.selectComponentIndex = -1;
                } else {
                    this.selectComponentIndex = index;
                    this.closeRightSidebarIndex = false;
                }
            },
            repeatCheck(arr, type) {
                var yard = 0;
                if (arr.dataType === 'submit') {
                    Object.values(this.form.customForm).forEach(item => {
                        if (item.dataType === 'submit') {
                            ++yard;
                        }
                    });
                }
                if (type === 0) {
                    return yard > 0;
                } else {
                    return yard > 1;
                }
            },
            customFormAppend: function (item) {
                var repeat = this.repeatCheck(item, 0);
                if (!repeat) {
                    let newItem = JSON.parse(JSON.stringify(item));
                    newItem['name'] = item['dataType'] + '_' + Math.round(new Date() / 1000);
                    this.form.customForm.push(newItem);
                } else {
                    layer.msg('提交按钮只允许添加一个');
                }
            },
            extensionData: function (e) {
                var newIndex = Number(e.newDraggableIndex);
                if (typeof (this.form.customForm[newIndex]) !== 'undefined') {
                    var repeat = this.repeatCheck(this.form.customForm[newIndex], 1);
                    if (!repeat) {
                        this.$set(this.form.customForm, newIndex, JSON.parse(JSON.stringify(this.form.customForm[newIndex])));
                        this.form.customForm[newIndex]['name'] = this.form.customForm[newIndex]['dataType'] + '_' + Math.round(new Date() / 1000);
                    } else {
                        this.form.customForm.splice(newIndex, 1);
                        layer.msg('提交按钮只允许添加一个');
                    }
                }
            },
            preventOffset: function (e) {
                if (this.flexFormCount !== this.flexForm.length) {
                    this.flexForm.splice(e.newDraggableIndex, 1);
                }
            },
            removeComponent(index) {
                this.closeRightSidebarIndex = true;
                this.selectRightSidebarIndex = -1;
                this.selectComponentIndex = -1;
                this.form.customForm.splice(index, 1);
            },
            changePicture: function () {
                layer.open({
                    type: 2,
                    anim: 2,
                    scrollbar: true,
                    area: ['900px', '600px'],
                    title: false,
                    closeBtn: 0,
                    shadeClose: true,
                    content: ["{:url('images/dialogimages')}&gclasid=0&pictureIndex=0", 'no']

                });
            },
            cuonice: function () {
                layer.open({
                    type: 2,
                    anim: 2,
                    scrollbar: true,
                    area: ['900px', '600px'],
                    title: false,
                    closeBtn: 0,
                    shadeClose: true,
                    content: ["{:url('images/dialogImages')}&gclasid=0&pictureIndex=-1", 'no']
                });
            },
            customOption() {
                Object.values(this.form.customForm).forEach((item, index) => {
                    if (item.dataType === 'radio' || item.dataType === 'checkbox' || item.dataType === 'select') {
                        var newChildItems = [];
                        var childItems = item.refrain.split("\n");
                        for (var i = 0; i < childItems.length; i++) {
                            newChildItems.push({'label': childItems[i], 'value': childItems[i]});
                        }
                        this.form.customForm[index].childItems = newChildItems;
                        if (typeof (item.abstain) !== 'undefined') {
                            this.form.customForm[index].value = item.abstain.split("\n");
                        }
                    }
                });
            },
            holdSave: function () {
                this.form.atName = this.form.atName.trim();
                if (this.form.atName === '') {
                    layer.msg('请在表单信息里输入认证名称');
                    return;
                }
                this.form.atIcon = this.form.atIcon.trim();
                if (this.form.atIcon === '') {
                    layer.msg('请在表单信息里选择认证图标');
                    return;
                }
                this.form.introduction = this.editor.txt.html();
                $.ajax({
                    type: "post",
                    url: "{:url('depend/muProvision')}",
                    data: {acid: this.acid, decent: this.decent, form: this.form},
                    dataType: 'json',
                    success: function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.href = "{:url('depend/provision')}";
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000});
                        }
                    }
                });
            }
        }
    });


    var sutake = function (eurl, pictureIndex, type) {
        switch (type) {
            case 0:
                vm.form.atIcon = eurl;
                break;
            case 1:
                vm.editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
                break;
        }
        layer.closeAll();
    }

</script>
{/block}