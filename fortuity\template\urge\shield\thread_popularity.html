{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-code {margin-right: 5px;}
    
    .am-btn-toolbar .am-btn-primary { background-color: #23b7e5; border-color: #23b7e5; border-radius: 3px; }
    .action-btn {display: inline-block; padding: 4px 10px; background: #fff; border: 1px solid #ddd; color: #666; border-radius: 3px; font-size: 12px; cursor: pointer; transition: all 0.3s;}
    .action-btn:hover {border-color: #23b7e5; color: #23b7e5; background-color: #f5fafd;}
    .action-btn.danger {color: #dd514c;}
    .action-btn.danger:hover {border-color: #dd514c; color: #dd514c; background-color: #fff8f8;}

    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-top: 15px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .am-table a { color: #23b7e5; }

    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a, .am-pagination > .am-active > a:hover {background-color: #23b7e5;border-color: #23b7e5;color:#fff;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
    
    /* ElementUI override */
    #app [v-cloak] { display: none; }
    .el-dialog__title { font-size: 16px; font-weight: 500; }
    .el-dialog__header { border-bottom: 1px solid #f0f0f0; padding: 15px 20px; }
    .el-dialog__body { padding: 25px 30px; }
    .el-form-item__label { font-weight: 500; }
    .el-button--primary { background-color: #23b7e5; border-color: #23b7e5; }
    .el-button--primary:hover { background-color: #49c5ec; border-color: #49c5ec; }
</style>
<div id="app" class="tpl-portlet-components" v-cloak>
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-code"></span> 热帖置顶
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-btn-toolbar">
                    <button type="button" class="am-btn am-btn-primary am-btn-sm" @click="newInfo">
                        <span class="am-icon-plus"></span> 新增置顶
                    </button>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="10%">帖子ID</th>
                            <th width="15%">发帖用户</th>
                            <th width="30%">发帖 标题 / 内容</th>
                            <th width="15%">置顶开始时间</th>
                            <th width="15%">置顶结束时间</th>
                            <th width="15%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>{$vo.id}</td>
                            <td>
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1"
                                   title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1"
                                   target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td>
                                <a href="{:url('essay/setails')}&uplid={$vo.id}" target="_blank">
                                    {if $vo.study_title}
                                    {$vo.study_title|emoji_decode|subtext=20}
                                    {elseif $vo.study_content}
                                    {$vo.study_content|emoji_decode|subtext=20|$expressionHtml}
                                    {else}
                                    无
                                    {/if}
                                </a>
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.put_top_start_time)}</td>
                            <td>{:date('Y-m-d H:i:s',$vo.put_top_end_time)}</td>
                            <td>
                                <button type="button" class="action-btn" @click="editInfo('{$vo.id}')">
                                    <span class="am-icon-pencil-square-o"></span> 编辑
                                </button>
                                <button type="button" class="action-btn danger" @click="deleteInfo('{$vo.id}')">
                                    <span class="am-icon-trash-o"></span> 删除
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <el-dialog :title="customTitle" :visible.sync="dialogVisible" width="450px" :close-on-click-modal="false">
        <el-form label-width="120px" @submit.native.prevent>
            <el-form-item label="置顶帖子编号">
                <el-input-number v-model="paperId" controls-position="right" :min="1" style="width: 100%;" :disabled="isDisabled"></el-input-number>
            </el-form-item>
            <el-form-item label="置顶开始时间">
                <el-date-picker style="width: 100%;"
                                v-model="putTopStartTime"
                                type="datetime"
                                value-format="timestamp"
                                placeholder="选择开始时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="置顶结束时间">
                <el-date-picker style="width: 100%;"
                                v-model="putTopEndTime"
                                type="datetime"
                                value-format="timestamp"
                                placeholder="选择结束时间">
                </el-date-picker>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="holdSave" :loading="isSaving">确 定</el-button>
        </span>
    </el-dialog>
</div>
{/block}
{block name="script"}
<script>
    new Vue({
        el: '#app',
        data() {
            return {
                dialogVisible: false,
                isSaving: false,
                customTitle: '新增置顶',
                isDisabled: false,
                paperId: undefined,
                putTopStartTime: '',
                putTopEndTime: ''
            }
        },
        methods: {
            newInfo() {
                this.customTitle = '新增置顶';
                this.isDisabled = false;
                this.paperId = undefined;
                this.putTopStartTime = '';
                this.putTopEndTime = '';
                this.dialogVisible = true;
            }, 
            editInfo(fid) {
                var loadIndex = layer.load(1, { shade: [0.1,'#fff'] });
                $.get("{:url('shield/get_thread_popularity_info')}&fid=" + fid, result => {
                    layer.close(loadIndex);
                    if (result.code > 0) {
                        this.isDisabled = true;
                        this.paperId = result.data.paperId;
                        this.putTopStartTime = result.data.putTopStartTime;
                        this.putTopEndTime = result.data.putTopEndTime;
                        this.customTitle = '编辑置顶';
                        this.dialogVisible = true;
                    } else {
                        layer.msg(result.msg, {icon: 5});
                    }
                });
            }, 
            deleteInfo(fid) {
                layer.confirm('您确定要删除这条数据吗？', {
                    btn: ['确定', '取消'], title: '提示'
                }, (index) => {
                    layer.close(index);
                    var loadIndex = layer.load(1, { shade: [0.1,'#fff'] });
                    $.post("{:url('shield/delete_thread_popularity_info')}", {'fid': fid}, (data) => {
                        layer.close(loadIndex);
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1600}, () => location.reload());
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000});
                        }
                    }, 'json');
                });
            }, 
            holdSave() {
                if (!this.paperId) {
                    layer.msg('请输入置顶帖子编号');
                    return;
                }
                if (!this.putTopStartTime) {
                    layer.msg('请选择置顶开始时间');
                    return;
                }
                if (!this.putTopEndTime) {
                    layer.msg('请选择置顶结束时间');
                    return;
                }
                if (this.putTopStartTime >= this.putTopEndTime) {
                    layer.msg('结束时间必须大于开始时间');
                    return;
                }
                
                var setData = {
                    paperId: this.paperId,
                    putTopStartTime: this.putTopStartTime,
                    putTopEndTime: this.putTopEndTime
                };

                this.isSaving = true;
                $.post("{:url('shield/thread_popularity')}", setData, (data) => {
                    this.isSaving = false;
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, () => {
                            this.dialogVisible = false;
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json').fail(() => {
                    this.isSaving = false;
                    layer.msg('请求失败，请稍后再试', {icon: 5});
                });
            }
        }
    });
</script>
{/block}