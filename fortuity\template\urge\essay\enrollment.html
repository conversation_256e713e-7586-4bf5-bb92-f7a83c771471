{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;text-align:center;}.am-table > thead:first-child > tr:first-child > th{text-align:center;}.customize-span{padding:3px 10px;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-location-arrow"></span> 报名详情
        </div>

        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索用户...">
                </div>
            </div>
        </div>

    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr>
                            <th width="20%">用户信息</th>
                            <th width="20%">预留信息</th>
                            <th width="20%">报名时间</th>
                            <th width="20%">是否验证</th>
                            <th width="20%">验证时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode|subtext=15}
                                </a>
                            </td>
                            <td>{$vo.rand_captcha}</td>
                            <td>{:date('Y-m-d H:i:s',$vo.partake_time)}</td>
                            <td>{if $vo.is_write_off}已验证{else}未验证{/if}</td>
                            <td>{if $vo.write_off_time}{:date('Y-m-d H:i:s',$vo.write_off_time)}{else}——{/if}</td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('essay/enrollment')}&prid={$prid}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('essay/enrollment')}&prid={$prid}&page={$page}";
        }
    }

</script>
{/block}