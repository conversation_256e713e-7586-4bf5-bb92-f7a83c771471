{extend name="/base"/}
{block name="main"}
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;position:relative;}.tpl-portlet-components:after{content:"";display:table;clear:both;}.am-form{position:relative;overflow:visible;}.portlet-title{display:flex;justify-content:space-between;align-items:center;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid #f0f0f0;}.caption{font-size:16px;color:#23b7e5;font-weight:500;}.caption .am-icon-circle-o{margin-right:5px;color:#23b7e5;}.tpl-portlet-input{position:relative;}.tpl-portlet-input input{height:32px;width:200px;padding:0 30px 0 10px;border:1px solid #e8e8e8;border-radius:4px;background:#fafafa;transition:all 0.3s;}.tpl-portlet-input input:focus{border-color:#23b7e5;background:#fff;box-shadow:0 0 0 2px rgba(35,183,229,0.1);}.tpl-portlet-input .am-icon-search{position:absolute;right:10px;top:50%;transform:translateY(-50%);color:#999;cursor:pointer;}.action-btn{display:inline-block;padding:4px 8px;background:#fff;border:1px solid #ddd;color:#23b7e5;border-radius:3px;font-size:12px;cursor:pointer;transition:all 0.3s;}.action-btn:hover{border-color:#23b7e5;color:#23b7e5;background-color:#f5fafd;}.am-table{border:1px solid #f0f0f0;border-radius:4px;font-size:13px;margin-bottom:15px;}.am-table > thead:first-child > tr:first-child > th{background-color:#f9f9f9;border-bottom:1px solid #eee;color:#333;font-weight:500;font-size:13px;text-align:center;padding:10px 8px;}.am-table > tbody > tr > td{padding:10px 8px;border-top:1px solid #f3f3f3;text-align:center;vertical-align:middle;color:#666;line-height:1.6;position:relative;}.am-table > tbody > tr:hover > td{background-color:#f5fafd;}.am-pagination{margin:10px 0;}.am-pagination > li > a{color:#666;background-color:#fff;border:1px solid #e8e8e8;margin:0 3px;border-radius:3px;}.am-pagination > .am-active > a{background-color:#23b7e5;border-color:#23b7e5;}.am-pagination > li > a:hover{background-color:#f5fafd;border-color:#e8e8e8;color:#23b7e5;}.filter-btn-group .am-btn{margin-right:10px;border-radius:3px;}.filter-btn-group .am-btn.active{background-color:#23b7e5;color:white;border-color:#23b7e5;}.am-badge-danger{color:#fff;background-color:#dd514c;}.shild-actions-container{display:flex;flex-wrap:wrap;justify-content:center;gap:8px;padding:10px 15px;}.shild-actions-container a{margin:0 25px;}.am-table tbody tr.am-table-striped-odd > td{background-color:#f9f9f9;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-circle-o"></span> 圈子列表
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索圈子...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <a href="javascript:void(0);" class="am-btn am-btn-success am-btn-sm" onclick="saloof();">
                        <span class="am-icon-plus"></span> 新增圈子
                    </a>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-6" style="display: flex; justify-content: flex-end;">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-sm filter-btn-group">
                        <a href="{:url('compass/fence')}&egon=0" class="am-btn am-btn-default {if $egon==0}active{/if}">正常显示</a>
                        <a href="{:url('compass/fence')}&egon=1" class="am-btn am-btn-default {if $egon==1}active{/if}">回收站</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-hover">
                        <thead>
                        <tr>
                            {if $egon==0}
                            <th width="7%" class="text-center">
                                排序
                            </th>
                            {/if}
                            <th width="7%" class="text-center">ID</th>
                            <th width="7%" class="text-center">图标</th>
                            <th width="10%" class="text-center">名称</th>
                            <th width="8%" class="text-center">类型</th>
                            <th width="7%" class="text-center">状态</th>
                            <th width="12%" class="text-center">创建时间</th>
                            <th width="8%" class="text-center">发帖限制</th>
                            <th width="8%" class="text-center">浏览限制</th>
                            <th width="8%" class="text-center">浏览权限</th>
                            {if $egon==0}
                            <th width="8%" class="text-center">链接</th>
                            {/if}
                            <th width="10%" class="text-center">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            {if $egon==0}
                            <td>
                                <input type="text" class="scfixed" id="sfixed-{$vo.id}" value="{$vo.scores}" data-score="{$vo.scores}" style="width: 50px; text-align: center;" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                            </td>
                            {/if}
                            <td>{$vo.id}</td>
                            <td>
                                <img src="{$vo.realm_icon}" onerror="this.src='static/disappear/default.png'" style="width: 40px;height:40px; border-radius: 50%;"/>
                            </td>
                            <td title="{$vo.realm_name}">
                                {$vo.realm_name|emoji_decode|subtext=8}
                            </td>
                            <td>
                                <a href="{:url('compass/fence')}&egon={$egon}&nid={$vo.neid}" target="_blank">
                                {$vo.name}
                                </a>
                            </td>
                            <td>
                                {if $vo.status == 0}
                                <span class="am-badge am-badge-warning">暂停</span>
                                {else}
                                <span class="am-badge am-badge-success">正常</span>
                                {/if}
                            </td>
                            <td title="{:date('Y-m-d H:i:s',$vo.rising_time)}">
                                {:date('Y-m-d H:i',$vo.rising_time)}
                            </td>
                            <td>Lv.{$vo.release_level}</td>
                            <td>Lv.{$vo.visit_level}</td>
                            <td>
                                {switch $vo.attention}
                                {case 0}<span class="am-text-success">所有用户</span>{/case}
                                {case 1}<span class="am-text-warning">审核加入</span>{/case}
                                {case 2}<span class="am-text-secondary">会员用户</span>{/case}
                                {case 3}<span class="am-text-primary">用户关注</span>{/case}
                                {/switch}
                            </td>
                            {if $egon==0}
                            <td>
                                <button type="button" class="action-btn" onclick="exerox(this);">
                                    <input type="hidden" value="/yl_welore/pages/packageA/circle_info/index?id={$vo.id}"> 复制
                                </button>
                            </td>
                            <td>
                                <button class="action-btn" onclick="gains(this,'{$vo.id}');" title="更多操作">
                                    <span class="am-icon-angle-down"></span>
                                </button>
                            </td>
                            {else}
                            <td>
                                <button type="button" class="action-btn am-btn-secondary" onclick="delGroup('{$vo.id}','1');">恢复</button>
                                <button type="button" class="action-btn am-btn-danger" onclick="delGroup('{$vo.id}','2');">彻底删除</button>
                            </td>
                            {/if}
                        </tr>
                        <tr id="kshatri-{$vo.id}" style="display: none;">
                            <td colspan="12" style="padding: 0; background-color: #fff;">
                                <div class="shild-actions-container">
                                    <a href="{:url('compass/dominator')}&tyid={$vo.id}" class="action-btn" target="_blank">管理团队</a>
                                    <a href="{:url('compass/savour')}&hazy_bering={$vo.id}&egon=0" class="action-btn" target="_blank">关注审核</a>
                                    <a href="{:url('essay/index')}&hazy_name={$vo.realm_name}&page=1" class="action-btn" target="_self">查看帖子</a>
                                    <a href="{:url('leading/muted')}&tyid={$vo.id}" class="action-btn" target="_blank">禁言列表</a>
                                    <a href="{:url('compass/topping')}&tory_id={$vo.id}" class="action-btn" target="_blank">置顶帖子</a>
                                    <a href="{:url('compass/essence')}&tory_id={$vo.id}" class="action-btn" target="_blank">推荐帖子</a>
                                    <a href="{:url('compass/uplfence')}&uplid={$vo.id}" class="action-btn" target="_blank">编辑圈子</a>
                                    <a href="javascript:void(0);" class="action-btn" onclick="delGroup('{$vo.id}','0');">删除圈子</a>
                                    <a href="javascript:void(0);" class="action-btn" onclick="showQrCode('{$vo.id}');">二维码</a>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>
    $(function() {
        // Custom table striping logic
        $('tbody tr:not([id^="kshatri-"])').each(function(index) {
            if (index % 2 != 1) {
                $(this).addClass('am-table-striped-odd');
            }
        });
    });

    var gains = function (obj, suid) {
        var icon_span = $(obj).find('span');
        var is_open = icon_span.hasClass('am-icon-angle-up');

        // Close all other open rows and reset their icons
        $('.am-icon-angle-up').removeClass('am-icon-angle-up').addClass('am-icon-angle-down');
        $('tr[id^="kshatri-"]').hide();

        if (!is_open) {
            // If it was closed, open it and set the icon to 'up'
            icon_span.removeClass('am-icon-angle-down').addClass('am-icon-angle-up');
            $('#kshatri-' + suid).show();
        }
        // If it was already open, the code above has already closed it and reset the icon.
    }

    var supre = function (asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            var repair = exalter(asyId, dalue);
            if (repair.code > 0) {
                layer.msg(repair.msg, {icon: 1, time: 800});
                $(domId).attr('data-score', dalue);
            } else {
                layer.msg(repair.msg, {icon: 5, time: 1600});
            }
        }
    }

    var exalter = function (asyId, dalue) {
        var straw = new Array();
        $.ajax({
            type: "post",
            url: "{:url('utsun')}",
            data: {
                asyId: asyId,
                dalue: dalue
            },
            async: false,
            success: function (data) {
                straw = data;
            }
        });
        return straw;
    }

    var exerox = function (obj) {
        var linkUrl = $(obj).children('input').eq(0).val();
        var oInput = document.createElement('input');
        oInput.value = linkUrl;
        document.body.appendChild(oInput);
        oInput.select(); // 选择对象
        var carried = document.execCommand("Copy"); // 执行浏览器复制命令
        oInput.className = 'oInput';
        oInput.style.display = 'none';
        if (carried) {
            layer.alert('\u94fe\u63a5\u5730\u5740\u5df2\u6210\u529f\u590d\u5236\u5230\u526a\u8d34\u677f\uff0c\u8bf7\u4f7f\u7528\u9f20\u6807\u53f3\u952e\u6216\u952e\u76d8\u7684\u0020\u0043\u0074\u0072\u006c\u002b\u0056\u0020\u7ec4\u5408\u952e\u0020\u8fdb\u884c\u7c98\u8d34\uff01', {'title': '\u7cfb\u7edf\u63d0\u793a'});
        } else {
            layer.alert('\u590d\u5236\u94fe\u63a5\u5730\u5740\u5931\u8d25\uff0c\u8bf7\u6839\u636e\u8df3\u8f6c\u94fe\u63a5\u4e0b\u65b9\u63d0\u793a\u624b\u52a8\u66f4\u6539\uff01', {'title': '\u7cfb\u7edf\u63d0\u793a'});
        }
    }


    var saloof = function () {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('compass/rulfence')}");
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    var delGroup = function (tyid, restore) {
        if (restore == 0) {
            var unsolved = '您确定要删除此圈子吗？';
        } else if (restore == 1) {
            var unsolved = '您确定要恢复此圈子吗？';
        } else if (restore == 2) {
            var unsolved = '您确定要彻底删除此圈子吗 ( <span style="color:red;">数据不可恢复</span> ) ？';
        }
        layer.confirm(unsolved, {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("{:url('compass/delLogicFlock')}", {'tyid': tyid, 'restore': restore}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    var showQrCode = function (fid) {
        $.get("{:url('tissue/createQrCode')}&pagePath=yl_welore/pages/packageA/circle_info/index@id=" + fid, function (data) {
            try {
                var msg = JSON.parse(data);
                layer.open({
                    title: "errcode：" + msg.errcode,
                    content: msg.errmsg
                });
            } catch (e) {
                layer.open({
                    type: 1,
                    title: false,
                    shadeClose: true,
                    closeBtn: 1,
                    area: ['430px', '430px'],
                    content: "<img src=\"{:url('tissue/createQrCode')}&pagePath=yl_welore/pages/packageA/circle_info/index@id=" + fid + "\">",
                });
            }
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('compass/fence')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('compass/fence')}&egon={$egon}&page={$page}";
        }
    }

</script>
{/block}