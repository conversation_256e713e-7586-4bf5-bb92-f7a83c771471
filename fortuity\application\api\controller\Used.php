<?php

namespace app\api\controller;

use app\api\service\Alternative;
use app\api\service\Util;
use think\Db;

class Used extends Base
{
    /**
     * 添加二手交易
     */
    public function InsLost()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('67a15129-6f67-b9d9-c428-d07b0a52124b', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $util = new Util();
        if ($this->user_info['tourist'] == 1) {
            return $this->json_rewrite(['code' => 1, 'msg' => '请登录后发布！']);
        }
        if ($data['release_index'] == -1) {
            return $this->json_rewrite(['code' => 1, 'msg' => '请选择发布类型']);
        }
        if (empty($data['lost_name'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '请填写物品名称']);
        }
        if (empty($data['lost_address'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '请填写交易地点']);
        }
        if (empty($data['lost_phone'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '请填写联系方式']);
        }
        if (empty($data['lost_desc'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '请填写物品描述']);
        }
        //判断敏感词
        $lost_name = $util->get_check_msg($data['lost_name'], $data['much_id'], $data['openid']);
        if ($lost_name['status'] == 'error') {
            return $this->json_rewrite($lost_name);
        }
        $lost_address = $util->get_check_msg($data['lost_address'], $data['much_id'], $data['openid']);
        if ($lost_address['status'] == 'error') {
            return $this->json_rewrite($lost_address);
        }
        $lost_desc = $util->get_check_msg($data['lost_desc'], $data['much_id'], $data['openid']);
        if ($lost_desc['status'] == 'error') {
            return $this->json_rewrite($lost_desc);
        }
        $config = Db::name('used_goods_item_config')->where('much_id', $data['much_id'])->find();
        $sql_content = emoji_encode($data['lost_desc']);
        //组合html
        $img_arr = json_decode($data['ImgArr'], true);
        foreach ($img_arr as $k => $v) {
            $sql_content .= "<img src='{$v}' >";
        }
        $item['user_id'] = $this->user_info['id'];
        $item['release_type'] = $data['release_index'];
        $item['item_type'] = $data['type'];
        $item['item_name'] = emoji_encode($data['lost_name']);
        $item['secondhand_address'] = emoji_encode($data['lost_address']);
        $item['item_detail'] = $sql_content;
        $item['item_status'] = 1;
        $item['top_time'] = 0;
        $item['audit_status'] = empty($config) ? 0 : $config['is_auto_audit'];
        $item['contact_details'] = $data['lost_phone'];
        if (is_numeric($data['lost_money'])) {
            $item['item_price'] = bcadd(abs($data['lost_money']), 0, 2);
        }else{
            $lost_moeny = $util->get_check_msg($data['lost_money'], $data['much_id'], $data['openid']);
            if ($lost_moeny['status'] == 'error') {
                return $this->json_rewrite($lost_moeny);
            }
            $item['item_price'] = $data['lost_money'];
        }

        $item['create_time'] = time();
        $item['much_id'] = $data['much_id'];
        // 启动事务
        Db::startTrans();
        try {
            $res = Db::name('used_goods_item')->insertGetId($item);
            if (!$res) {
                Db::rollback();
                return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:1']);
            }
            //置顶天数
            $top_day = abs(intval($data['top_day']));
            //选择天数，并且开启置顶
            if ($top_day > 0 && $config['top_twig'] == 1) {
                //查询当前用户详情
                $user = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->field('fraction,conch')->find();
                //计算支付金额
                $price = bcmul($top_day, $config['top_price'], 2);
                //更新置顶
                $time = bcadd(bcmul($top_day, 86400), time());
                //支付方式为贝壳支付
                if ($config['price_type'] == 0) {
                    //判断当前用户余额
                    if (bccomp($user['conch'], $price, 2) == -1) {
                        Db::rollback();
                        return $this->json_rewrite(['code' => 1, 'msg' => '余额不足！']);
                    }
                    //明细表增加数据
                    $user_amount = $util->user_amount($this->user_info['id'], 2, $price, $user['fraction'], $user['fraction'], $user['conch'], bcsub($user['conch'], $price, 2), 0, $config['custom_title']."置顶{$top_day}天", $data['much_id']);
                    if (!$user_amount) {
                        Db::rollback();
                        return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:2']);
                    }
                    //更新用户信息
                    $user_update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['conch' => bcsub($user['conch'], $price, 2)]);
                    if (!$user_update) {
                        Db::rollback();
                        return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:3']);
                    }
                    $req = Db::name('used_goods_item')->where('id', $res)->update(['top_time' => $time]);
                    if (!$req) {
                        Db::rollback();
                        return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:7']);
                    }
                }
                //支付方式为积分支付
                if ($config['price_type'] == 1) {
                    //判断当前用户余额
                    if (bccomp($user['fraction'], $price, 2) == -1) {
                        Db::rollback();
                        return $this->json_rewrite(['code' => 1, 'msg' => '余额不足！']);
                    }
                    //明细表增加数据
                    $user_amount = $util->user_amount($this->user_info['id'], 2, $price, $user['fraction'], bcsub($user['fraction'], $price, 2), $user['conch'], $user['conch'], 1, $config['custom_title']."置顶{$top_day}天", $data['much_id']);
                    if (!$user_amount) {
                        Db::rollback();
                        return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:4']);
                    }
                    //更新用户信息
                    $user_update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['fraction' => bcsub($user['fraction'], $price, 2)]);
                    if (!$user_update) {
                        Db::rollback();
                        return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:5']);
                    }

                    $req = Db::name('used_goods_item')->where('id', $res)->update(['top_time' => $time]);
                    if (!$req) {
                        Db::rollback();
                        return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:6']);
                    }
                }
                //支付方式为微信支付
                if ($config['price_type'] == 2) {
                    Db::commit();
                    return $this->json_rewrite(['code' => 2, 'msg' => '微信支付！', 'item' => ['id' => $res, 'top_day' => $top_day]]);
                }
                //贝壳或者积分
                //贝壳或者积分置顶表增加数据
                $top = Db::name('used_goods_item_top')->insert(['is_pay' => 1, 'usl_id' => 0, 'ugi_id' => $res, 'user_id' => $this->user_info['id'], 'top_day' => $top_day, 'pay_type' => $config['price_type'], 'pay_price' => $price, 'add_time' => time(), 'much_id' => $data['much_id']]);
                if (!$top) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:7！']);
                }
            }
            Db::commit();
            return $this->json_rewrite(['code' => 0, 'msg' => $item['audit_status'] == 0 ? '请等待审核！' : '发布成功！']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:' . $e->getMessage()]);
        }

    }

    /**
     * 类型
     */
    public function getLostType()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('67a15129-6f67-b9d9-c428-d07b0a52124b', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $list = Db::name('used_goods_item_type')
            ->where('much_id', $data['much_id'])
            ->where('status', 1)
            ->where('is_del', 0)
            ->order('sort')
            ->field('name as realm_name,id')
            ->select();
        return $this->json_rewrite($list);
    }

    /**
     * 配置
     */
    public function getLostConfig()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('67a15129-6f67-b9d9-c428-d07b0a52124b', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $config = Db::name('used_goods_item_config')->where('much_id', $data['much_id'])->field('top_twig,price_type,top_price,custom_title,help_document')->find();
        if (empty($config)) {
            $config['top_twig'] = 0;
        }
        $config['design'] = $this->design;
        //查询我有多少积分
        $config['fraction'] = $this->user_info['fraction'];
        $config['conch'] = $this->user_info['conch'];
        $config['help_document']=emoji_decode($config['help_document']);
        return $this->json_rewrite($config);
    }

    /**
     * 获取二手交易
     */
    public function getLostList()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('67a15129-6f67-b9d9-c428-d07b0a52124b', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $where = [];
        if ($data['type_id'] != 0) {
            $where['i.item_type'] = ['eq', $data['type_id']];
        }
        if ($data['current'] == 'tab2') {
            $where['i.user_id'] = ['eq', $this->user_info['id']];
            //$where['i.item_status'] = ['eq', 1];
        } else {
            $where['i.audit_status'] = ['eq', 1];
        }
        $title = emoji_encode($data['search']);
        $where['i.item_name|i.item_detail'] = ['like', '%' . $title . '%'];
        //未置顶的
        $list = Db::name('used_goods_item')->alias('i')
            ->join('used_goods_item_type t', 't.id=i.item_type')
            ->where('i.much_id', $data['much_id'])
            ->where('i.top_time', 0)
            ->where($where)
            ->where('t.status', 1)
            ->where('i.is_del', 0)
            ->where('t.is_del', 0)
            ->page($data['page'], 15)
            ->order('i.id desc')
            ->field('i.item_price,i.user_id,i.id,i.release_type,i.item_name,i.item_detail,i.item_status,i.create_time,i.audit_status,i.audit_reason,i.secondhand_address')
            ->select();
        //置顶的
        $top_list = Db::name('used_goods_item')->alias('i')
            ->join('used_goods_item_type t', 't.id=i.item_type')
            ->where('i.much_id', $data['much_id'])
            ->whereTime('top_time', '>', date('Y-m-d H:i:s', time()))
            ->where($where)
            ->where('item_status', 1)
            ->where('t.status', 1)
            ->where('t.is_del', 0)
            ->where('i.is_del', 0)
            ->page($data['page'], 15)
            ->order('i.id asc')
            ->field('i.item_price,i.user_id,i.id,i.release_type,i.item_name,i.item_detail,i.item_status,i.create_time,i.audit_status,i.audit_reason,i.secondhand_address')
            ->select();
        if (!empty($top_list) && $data['page'] == 1) {
            foreach ($top_list as $k => $v) {
                $v['is_top'] = 1;
                array_unshift($list, $v);
            }

        }
        if (empty($list[0])) {
            return $this->json_rewrite([]);
        }
        $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';

        foreach ($list as $k => $v) {
            preg_match_all($preg, emoji_decode($v['item_detail']), $match);
            if (!empty($match[0])) {
                $img = array();
                foreach ($match[1] as $a => $b) {
                    $img[$a] = htmlspecialchars_decode($b);
                }
                $list[$k]['image_part'] = $img;
            } else {
                $list[$k]['image_part'] = [];
            }
            $list[$k]['create_time'] = formatTime($v['create_time']);
            $list[$k]['item_detail'] = strip_tags(emoji_decode($v['item_detail']));
            $list[$k]['item_name'] = emoji_decode($v['item_name']);
            $list[$k]['secondhand_address'] = emoji_decode($v['secondhand_address']);
            $list[$k]['item_detail'] = Alternative::ExpressionHtml($list[$k]['item_detail']);
            $user = Db::name('user')->where('id', $v['user_id'])->field('id,user_head_sculpture,user_nick_name')->find();
            $user['user_nick_name'] = emoji_decode($user['user_nick_name']);
            $list[$k]['user_info'] = $user;
            switch ($v['release_type']) {
                case "0":
                    $release_type = "出售";
                    break;
                case "1":
                    $release_type = "求购";
                    break;
                case "2":
                    $release_type = "租聘";
                    break;
                case "3":
                    $release_type = "置换";
                    break;
                case "4":
                    $release_type = "定制";
                    break;
            }
            $list[$k]['release_type_name'] = $release_type;

        }
        return $this->json_rewrite($list);
    }

    /**
     * 获取详情
     */
    public function LostInfo()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('67a15129-6f67-b9d9-c428-d07b0a52124b', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $info = Db::name('used_goods_item')->alias('i')
            ->join('used_goods_item_type t', 't.id=i.item_type')
            ->where('i.audit_status', 1)
            ->where('i.much_id', $data['much_id'])
            ->where('t.status', 1)
            ->where('i.is_del', 0)
            ->where('t.is_del', 0)
            ->where('i.id', $data['id'])
            ->field('i.item_price,i.top_time,t.name,i.user_id,i.id,i.release_type,i.item_type,i.secondhand_address,i.contact_details,i.item_name,i.item_detail,i.item_status,i.create_time')
            ->find();
        if (empty($info)) {
            return $this->json_rewrite(['code' => 1, 'msg' => '内容不见了！']);
        }
        $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
        preg_match_all($preg, emoji_decode($info['item_detail']), $match);
        if (!empty($match[0])) {
            $img = array();
            foreach ($match[1] as $a => $b) {
                $img[$a] = htmlspecialchars_decode($b);
            }
            $info['image_part'] = $img;
        } else {
            $info['image_part'] = [];
        }
        //查询置顶是否已到期
        if (bccomp(time(), $info['top_time']) == 1) {
            $info['top_time'] = 0;
        } else {
            $info['top_time'] = date('Y-m-d H:i:s', $info['top_time']);
        }

        $info['create_time'] = formatTime($info['create_time']);
        $info['item_detail'] = strip_tags(emoji_decode($info['item_detail']));
        $info['item_name'] = emoji_decode($info['item_name']);
        $info['item_detail'] = Alternative::ExpressionHtml($info['item_detail']);
        $info['user_info'] = Db::name('user')->where('id', $info['user_id'])->field('id,user_head_sculpture,user_nick_name')->find();
        $info['user_info']['user_nick_name'] = emoji_decode($info['user_info']['user_nick_name']);
        $info['config'] = Db::name('used_goods_item_config')->where('much_id', $data['much_id'])->field('top_twig,price_type,top_price')->find();

        return $this->json_rewrite(['code' => 0, 'info' => $info]);
    }
    /**
     * 二手回复
     */
    public function LostDoSubmit()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('67a15129-6f67-b9d9-c428-d07b0a52124b', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $util = new Util();
        if ($this->user_info['tourist'] == 1) {
            return $this->json_rewrite(['code' => 1, 'msg' => '请登录后发布！']);
        }
        if (empty($data['content'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '请填写回复内容！']);
        }
        $check_content = $util->get_check_msg($data['content'], $data['much_id'], $data['openid']);
        if ($check_content['status'] == 'error') {
            return $this->json_rewrite($check_content);
        }
        //查询招领帖子
        $info = Db::name('used_goods_item')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();

        $config = Db::name('used_goods_item_config')->where('much_id', $data['much_id'])->find();
        $item['user_id'] = $this->user_info['id'];
        $item['ugi_id'] = $data['id'];
        $item['content'] = $this->safe_html(emoji_encode($data['content']));
        $item['is_secrecy'] = $data['is_secrecy'];
        $item['reply_time'] = time();
        $item['audit_status'] = empty($config) ? 0 : $config['reply_is_auto_audit'];
        $item['is_del'] = 0;
        $item['much_id'] = $data['much_id'];
        $img = json_decode($data['img_arr'], true);
        foreach ($img as $k => $v) {
            $item['content'] .= "<img src='{$v}' >";
        }
        $res = Db::name('used_goods_item_reply')->insert($item);
        if (!$res) {
            return $this->json_rewrite(['code' => 1, 'msg' => '发布失败，请稍后重试！']);
        }
        if ($item['audit_status'] == 1) {
            $util->add_template([
                'much_id' => $data['much_id'],
                'at_id' => 'YL0009',
                'user_id' => $info['user_id'],
                'page' => 'yl_welore/pages/packageF/lost_info/index?id=' . $info['id'],
                'keyword1' => $config['custom_title'].'帖子评论通知',
                'keyword2' => date('Y年m月d日 H:i:s', time())
            ]);
        }
        return $this->json_rewrite(['code' => 0, 'msg' => $item['audit_status'] == 1 ? '发布成功！' : '请等待审核！']);
    }
    /**
     * 更改我的回复
     */
    public function UpdateIsSecrecyDo()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('67a15129-6f67-b9d9-c428-d07b0a52124b', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $rep_info = Db::name('used_goods_item_reply')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        if ($rep_info['user_id'] != $this->user_info['id']) {
            return $this->json_rewrite(['code' => 1, 'msg' => '系统错误：1！']);
        }
        $res = Db::name('used_goods_item_reply')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['is_secrecy' => $data['key'] == 0 ? 1 : 0]);
        if (!$res) {
            return $this->json_rewrite(['code' => 1, 'msg' => '系统错误：2！']);
        }
        return $this->json_rewrite(['code' => 0, 'msg' => '修改成功！']);
    }
    /**
     * 获取回复
     */
    public function LostInfoReply()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('67a15129-6f67-b9d9-c428-d07b0a52124b', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $lost_info = Db::name('used_goods_item')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        $list = Db::name('used_goods_item_reply')
            ->where('ugi_id', $data['id'])
            ->where('audit_status', 1)
            ->where('is_del', 0)
            ->order('reply_time desc')
            ->page($data['page'], 15)
            ->select();
        $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
        foreach ($list as $k => $v) {
            if ($lost_info['user_id'] != $this->user_info['id'] && $v['is_secrecy'] == 1 && $v['user_id'] != $this->user_info['id']) {
                unset($list[$k]);
                //array_splice($list,$k,1);
                continue;
            }
            preg_match_all($preg, emoji_decode($v['content']), $match);
            if (!empty($match[0])) {
                $img = array();
                foreach ($match[1] as $a => $b) {
                    $img[$a] = htmlspecialchars_decode($b);
                }
                $list[$k]['image_part'] = $img;
            } else {
                $list[$k]['image_part'] = [];
            }
            $list[$k]['reply_time'] = date('Y-m-d H:i:s', $v['reply_time']);
            $list[$k]['content'] = strip_tags(emoji_decode($v['content']));
            $list[$k]['content'] = Alternative::ExpressionHtml($list[$k]['content']);
            $user = Db::name('user')->where('id', $v['user_id'])->field('id,user_head_sculpture,user_nick_name')->find();
            $user['user_nick_name'] = emoji_decode($user['user_nick_name']);
            $list[$k]['user_info'] = $user;
        }
        rsort($list);
        return $this->json_rewrite($list);
    }

    /**
     * 删除回复
     */
    public function DelReplyDo()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('67a15129-6f67-b9d9-c428-d07b0a52124b', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        //判断是否是自己发的
        $check = Db::name('used_goods_item_reply')
            ->where('id', $data['reply_id'])
            ->where('ugi_id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        //查询是否是楼主
        $check2 = Db::name('lost_item')
            ->where('id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if ($check['user_id'] != $this->user_info['id'] && $check2['user_id'] != $this->user_info['id']) {
            return $this->json_rewrite(['code' => 1, 'msg' => '系统错误：1！']);
        }
        $del = Db::name('used_goods_item_reply')
            ->where('id', $data['reply_id'])
            ->where('ugi_id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->update(['is_del' => 1]);
        if ($del == false) {
            return $this->json_rewrite(['code' => 1, 'msg' => '系统错误：2！']);
        }
        return $this->json_rewrite(['code' => 0, 'msg' => '删除成功！']);
    }

    /**
     * 二手置顶
     */
    public function PaySubmit()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('67a15129-6f67-b9d9-c428-d07b0a52124b', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $util = new Util();
        $config = Db::name('used_goods_item_config')->where('much_id', $data['much_id'])->find();
        $lost_info = Db::name('used_goods_item')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        //置顶天数
        $top_day = abs(intval($data['top_day']));
        if (empty($top_day) || $top_day == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '系统错误！']);
        }
        //选择天数，并且开启置顶
        if ($top_day > 0 && $config['top_twig'] == 1) {
            //查询当前用户详情
            $user = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->field('fraction,conch')->find();
            //计算支付金额
            $price = bcmul($top_day, $config['top_price'], 2);
            //更新置顶
            if (bccomp(time(), $lost_info['top_time']) == 1) {
                $time = bcadd(bcmul($top_day, 86400), time());
            } else {
                $time = bcadd(bcmul($top_day, 86400), $lost_info['top_time']);
            }

            //支付方式为贝壳支付
            if ($config['price_type'] == 0) {
                //判断当前用户余额
                if (bccomp($user['conch'], $price, 2) == -1) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '余额不足！']);
                }
                //明细表增加数据
                $user_amount = $util->user_amount($this->user_info['id'], 2, $price, $user['fraction'], $user['fraction'], $user['conch'], bcsub($user['conch'], $price, 2), 0, $config['custom_title']."置顶{$top_day}天", $data['much_id']);
                if (!$user_amount) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:2']);
                }
                //更新用户信息
                $user_update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['conch' => bcsub($user['conch'], $price, 2)]);
                if (!$user_update) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:3']);
                }
                $req = Db::name('used_goods_item')->where('id', $data['id'])->update(['top_time' => $time]);
                if (!$req) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:7']);
                }
            }
            //支付方式为积分支付
            if ($config['price_type'] == 1) {
                //判断当前用户余额
                if (bccomp($user['fraction'], $price, 2) == -1) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '余额不足！']);
                }
                //明细表增加数据
                $user_amount = $util->user_amount($this->user_info['id'], 2, $price, $user['fraction'], bcsub($user['fraction'], $price, 2), $user['conch'], $user['conch'], 1, $config['custom_title']."置顶{$top_day}天", $data['much_id']);
                if (!$user_amount) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:4']);
                }
                //更新用户信息
                $user_update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['fraction' => bcsub($user['fraction'], $price, 2)]);
                if (!$user_update) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:5']);
                }

                $req = Db::name('used_goods_item')->where('id', $data['id'])->update(['top_time' => $time]);
                if (!$req) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:6']);
                }
            }
            //支付方式为微信支付
            if ($config['price_type'] == 2) {
                return $this->json_rewrite(['code' => 2, 'msg' => '微信支付！']);
            }
            //贝壳或者积分置顶表增加数据
            $top = Db::name('used_goods_item_top')->insert(['is_pay'=>1,'usl_id' => 0, 'ugi_id' => $data['id'], 'user_id' => $this->user_info['id'], 'top_day' => $top_day, 'pay_type' => $config['price_type'], 'pay_price' => $price, 'add_time' => time(), 'much_id' => $data['much_id']]);
            if (!$top) {
                return $this->json_rewrite(['code' => 1, 'msg' => '发布失败:7！']);
            }
            return $this->json_rewrite(['code' => 0, 'msg' => '置顶成功！']);
        } else {
            return $this->json_rewrite(['code' => 1, 'msg' => '系统未开启置顶:8']);
        }
    }
    /**
     * 删除详情
     */
    public function DelInfoDo()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('67a15129-6f67-b9d9-c428-d07b0a52124b', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        //判断是否是自己发的
        $check = Db::name('used_goods_item')
            ->where('id', $data['id'])
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if (empty($check)) {
            return $this->json_rewrite(['code' => 1, 'msg' => '系统错误：1！']);
        }
        $del = Db::name('used_goods_item')
            ->where('id', $data['id'])
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->update(['is_del' => 1]);
        if ($del == false) {
            return $this->json_rewrite(['code' => 1, 'msg' => '系统错误：2！']);
        }
        return $this->json_rewrite(['code' => 0, 'msg' => '删除成功！']);
    }
    /**
     * 完结内容
     */
    public function SetStatusDo()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('67a15129-6f67-b9d9-c428-d07b0a52124b', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        //判断是否是自己发的
        $check = Db::name('used_goods_item')
            ->where('id', $data['id'])
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if (empty($check)) {
            return $this->json_rewrite(['code' => 1, 'msg' => '系统错误：1！']);
        }
        $del = Db::name('used_goods_item')
            ->where('id', $data['id'])
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->update(['item_status' => 2,'top_time'=>0]);
        if ($del == false) {
            return $this->json_rewrite(['code' => 1, 'msg' => '系统错误：2！']);
        }
        return $this->json_rewrite(['code' => 0, 'msg' => '成功！']);
    }

}