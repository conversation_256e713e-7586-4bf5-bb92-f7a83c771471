<?php

namespace app\api\controller;

use app\api\service\TmplService;
use app\api\service\Util;
use app\common\FluxibleInfo;
use app\common\MagicTrick;
use app\common\Pisces;
use app\common\Suspense;
use think\Cache;
use think\Db;
use think\Request;

require EXTEND_PATH . "Wxpay/WxPay.Api.php";

class Pay extends Base
{


    public function index()
    {
        $rs = ['status' => 'success', 'code' => 0, 'msg' => '兑换成功'];
        $data = input('param.');
        $info = Db::name('user_honorary')->where('much_id', $data['much_id'])->find();
        $user_info = Db::name('user')->where('user_wechat_open_id', $data['openid'])->find();
        $data['uid']=$user_info['id'];
        $design = Db::name('design')->where('much_id', $data['much_id'])->find();

        if ($info['chop_type'] == 0) {

            if (abs($data['time']) == 1 && $info['first_discount'] == 1 && $user_info['vip_end_time'] == '') {
                $money = sprintf("%.1f", $info['discount_scale'] * $info['hono_price']);
            } else {
                $money = abs($data['time']) * $info['hono_price'];
            }

            $msg = '兑换会员（' . abs($data['time']) . '个月）';

        } else {
            $pay_list = json_decode($info['define_price'], true);

            $money = $pay_list[$data['pay_index']]['price'];

            if ($pay_list[$data['pay_index']]['time'] != $data['time']) {
                return $this->json_rewrite(['status' => 'error', 'code' => 2, 'msg' => '兑换天数有误，请重新选择！']);
            }

            $msg = '兑换会员（' . abs($data['time']) . '天）';
        }


        if (bccomp($user_info['conch'], $money, 2) == -1) {
            $rs = ['status' => 'error', 'code' => 1, 'msg' => $design['currency'] . '不足！'];
            return $this->json_rewrite($rs);
        }


        //流水号
        $ins['user_id'] = $data['uid'];
        $ins['category'] = '2';
        $ins['ruins_time'] = time();
        $ins['solution'] = $msg;
        $ins['much_id'] = $data['much_id'];
        $ins['finance'] = -$money;
        $ins['evaluate'] = 0;

        $ins['poem_fraction'] = $user_info['fraction'];
        $ins['poem_conch'] = $user_info['conch'];

        $ins['surplus_conch'] = bcsub($user_info['conch'], $money, 2);
        $ins['surplus_fraction'] = $user_info['fraction'];
        //return $this->json_rewrite($ins);

        Db::startTrans();
        try {
            $db_ins = Db::name('user_amount')->insert($ins);
            if ($db_ins) {
                //减贝壳
                $dec = Db::name('user')->where('id', $data['uid'])->update(['conch' => $ins['surplus_conch']]);
                if (!$dec) {
                    Db::rollback();
                    $rs = ['status' => 'error', 'code' => 2, 'msg' => '兑换失败！'];
                    return $this->json_rewrite($rs);
                }

                if ($info['chop_type'] == 0) {
                    if ($user_info['vip_end_time'] < time()) {
                        //用户已经不是会员  当前时间+ n个月的秒
                        $vip_end_time = time() + (86400 * $data['time'] * 30);
                    } else {
                        //用户是会员  到期时间+ n个月的秒
                        $vip_end_time = $user_info['vip_end_time'] + (86400 * $data['time'] * 30);
                    }
                } else {
                    if ($user_info['vip_end_time'] < time()) {
                        //用户已经不是会员  当前时间+ n个月的秒
                        $vip_end_time = time() + (86400 * $data['time']);
                    } else {
                        //用户是会员  到期时间+ n天的秒
                        $vip_end_time = $user_info['vip_end_time'] + (86400 * $data['time']);
                    }
                }


                $better_logger = Db::name('user_better_logger')->insertGetId(['user_id' => $data['uid'], 'duration' => $data['time'], 'buy_price' => $money, 'buy_time' => time(), 'much_id' => $data['much_id']]);
                if (!$better_logger) {
                    Db::rollback();
                    $rs = ['status' => 'error', 'code' => 2, 'msg' => '兑换失败！'];
                    return $this->json_rewrite($rs);
                }
                $up_vip = Db::name('user')->where('id', $data['uid'])->update(['vip_end_time' => $vip_end_time]);
                if (!$up_vip) {
                    Db::rollback();
                    $rs = ['status' => 'error', 'code' => 2, 'msg' => '兑换失败！'];
                    return $this->json_rewrite($rs);
                }
            } else {
                Db::rollback();
                $rs = ['status' => 'error', 'code' => 2, 'msg' => '兑换失败！'];
                return $this->json_rewrite($rs);
            }
            // 提交事务
            Db::commit();
            $util=new Util();
            $util->add_user_smail($data['uid'],$msg,$data['much_id'],0,0);
            $tmplData = [
                'much_id' => $data['much_id'],
                'at_id' => 'YL0009',
                'user_id' => $data['uid'],
                'page' => 'yl_welore/pages/user_smail/index',
                'keyword1' => '扣除'.$design['currency'].'（-'.$money.'）',
                'keyword2' => date('Y年m月d日 H:i:s', time())
            ];
            $util->add_template($tmplData);
            FluxibleInfo::SendDetectPost([
                'type' => 2,
                'user_better_logger_id' => $better_logger,
                'vip_end_time' => $vip_end_time,
                'user_id' => $this->user_info['id'],
                'much_id' => $data['much_id'],
            ], $data['much_id']);
            return $this->json_rewrite($rs);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $rs = ['status' => 'error', 'code' => 2, 'msg' => '网络不稳定，请重试！' . $e->getMessage()];
            return $this->json_rewrite($rs);
        }


        //return $this->pay($data,$getConfig,$msg,$money,$data['time']);
    }
    /*
     * 求职招聘
     */
    public function do_employment_pay(){
        $data = input('param.');
        //查询用户
        $user = Db::name('user')->where('user_wechat_open_id', $data['openid'])->where('token', $data['token'])->find();
        if (empty($user)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '参数错误！']);
        }
        $getConfig = cache('fatal_' . $data['much_id']);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $data['much_id'])->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $data['much_id'], $getConfig);
            }
        }
        define("APPID", $getConfig['app_id']);
        define("MCHID", $getConfig['app_mchid']);
        define("KEY", $getConfig['app_key']);
        define("APPSECRET", $getConfig['app_secret']);
        //查询当前置顶配置
        $config = Db::name('employment_item_config')->where('much_id', $data['much_id'])->find();
        //计算支付金额
        //置顶天数
        $top_day = abs(intval($data['top_day']));
        $price = bcmul($top_day, $config['top_price'], 2);
        $data['pay_type'] = 5;
        $data['top_day'] = $top_day;
        $data['uid'] = $user['id'];
        if(empty($top_day)||$top_day==0){
            return json_encode(['return_msg'=>'Error']);
        }
        return $this->pay($data, $getConfig, $config['custom_title']."置顶{$top_day}天", abs($price));
    }
    /**
     * 二手交易
     */
    public function do_used_pay()
    {
        $data = input('param.');
        //查询用户
        $user = Db::name('user')->where('user_wechat_open_id', $data['openid'])->where('token', $data['token'])->find();
        if (empty($user)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '参数错误！']);
        }
        $getConfig = cache('fatal_' . $data['much_id']);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $data['much_id'])->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $data['much_id'], $getConfig);
            }
        }
        define("APPID", $getConfig['app_id']);
        define("MCHID", $getConfig['app_mchid']);
        define("KEY", $getConfig['app_key']);
        define("APPSECRET", $getConfig['app_secret']);
        //查询当前置顶配置
        $config = Db::name('used_goods_item_config')->where('much_id', $data['much_id'])->find();
        //计算支付金额
        //置顶天数
        $top_day = abs(intval($data['top_day']));
        $price = bcmul($top_day, $config['top_price'], 2);
        $data['pay_type'] = 4;
        $data['top_day'] = $top_day;
        $data['uid'] = $user['id'];
        if(empty($top_day)||$top_day==0){
            return json_encode(['return_msg'=>'Error']);
        }
        return $this->pay($data, $getConfig, $config['custom_title']."置顶{$top_day}天", abs($price));
    }
    /**
     * 失物招领置顶支付
     */
    public function do_lost_pay()
    {
        $data = input('param.');
        //查询用户
        $user = Db::name('user')->where('user_wechat_open_id', $data['openid'])->where('token', $data['token'])->find();
        if (empty($user)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '参数错误！']);
        }
        $getConfig = cache('fatal_' . $data['much_id']);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $data['much_id'])->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $data['much_id'], $getConfig);
            }
        }
        define("APPID", $getConfig['app_id']);
        define("MCHID", $getConfig['app_mchid']);
        define("KEY", $getConfig['app_key']);
        define("APPSECRET", $getConfig['app_secret']);
        //查询当前置顶配置
        $config = Db::name('lost_item_config')->where('much_id', $data['much_id'])->find();
        //计算支付金额
        //置顶天数
        $top_day = abs(intval($data['top_day']));
        $price = bcmul($top_day, $config['top_price'], 2);
        $data['pay_type'] = 3;
        $data['top_day'] = $top_day;
        $data['uid'] = $user['id'];
        if(empty($top_day)||$top_day==0){
            return json_encode(['return_msg'=>'Error']);
        }
        return $this->pay($data, $getConfig, "失物招领置顶{$top_day}天", abs($price));
    }

    /**
     * 购买商品
     */
    public function do_goods_pay()
    {
        $data = input('param.');
        //查询用户
        $user = Db::name('user')->where('user_wechat_open_id', $data['openid'])->where('token', $data['token'])->find();
        if (empty($user)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '参数错误！']);
        }
        $getConfig = cache('fatal_' . $data['much_id']);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $data['much_id'])->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $data['much_id'], $getConfig);
            }
        }
        define("APPID", $getConfig['app_id']);
        define("MCHID", $getConfig['app_mchid']);
        define("KEY", $getConfig['app_key']);
        define("APPSECRET", $getConfig['app_secret']);
        //查询当前订单
        $order = Db::name('shop_order')->where('id', $data['order_id'])
            ->where('much_id', $data['much_id'])
            ->where('user_id', $user['id'])
            ->where('status', 0)
            ->where('trash', 0)
            ->find();
        if (empty($order)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '订单不见了']);
        }
        $data['pay_type'] = 2;
        $data['uid'] = $user['id'];
        //计算价格
        return $this->pay($data, $getConfig, '购买商品' . $order['product_name'], abs($order['actual_price']));
    }

    /**
     * 充值
     */
    public function do_pay()
    {
        $data = input('param.');
        //查询用户
        $user = Db::name('user')->where('user_wechat_open_id', $data['openid'])->where('token', $data['token'])->find();
        if (empty($user)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '参数错误！']);
        }
        if (empty($data['much_id'])) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '参数错误！']);
        }
        $getConfig = cache('fatal_' . $data['much_id']);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $data['much_id'])->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $data['much_id'], $getConfig);
            }
        }
        define("APPID", $getConfig['app_id']);
        define("MCHID", $getConfig['app_mchid']);
        define("KEY", $getConfig['app_key']);
        define("APPSECRET", $getConfig['app_secret']);

        $design = Db::name('design')->where('much_id', $data['much_id'])->find();
        $msg = '充值' . substr(sprintf("%.3f", $data['money']), 0, -1) . $design['currency'];
        $data['pay_type'] = 0;
        $data['uid'] = $user['id'];
        return $this->pay($data, $getConfig, $msg, substr(sprintf("%.3f", $data['money']), 0, -1));
    }

    /**
     * 抽小程序支付
     */
    public function tape_smoke_pay()
    {
        $data = input('param.');
        $user = Db::name('user')->where('user_wechat_open_id', $data['openid'])->where('token', $data['token'])->find();
        if (empty($user)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '参数错误！']);
        }
        $getConfig = cache('fatal_' . $data['much_id']);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $data['much_id'])->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $data['much_id'], $getConfig);
            }
        }
        define("APPID", $getConfig['app_id']);
        define("MCHID", $getConfig['app_mchid']);
        define("KEY", $getConfig['app_key']);
        define("APPSECRET", $getConfig['app_secret']);

        //获取纸条配置
        $config = Db::name('feeling_stipulate')->where('much_id', $data['much_id'])->find();
        //查询是否有抽取限制
        if (!empty($config) && intval($config['pick_limit']) > 0) {
            //查询我今日抽取的
            $count = Db::name('user_feeling')
                ->where('user_id', $user['id'])
                ->where('much_id', $data['much_id'])
                ->whereTime('pull_time', 'today')
                ->count();
            if ($count >= $config['pick_limit']) {
                return $this->json_rewrite(['code' => 1, 'msg' => '今日抽取小纸条已达上限']);
            }
        }
        $money = 0;
        if (!empty($config)) {
            if (intval($data['gender']) == 1) {
                $money = abs($config['pick_price_male']);
            } else {
                $money = abs($config['pick_price_female']);
            }
        }
        $data['pay_type'] = 1;
        //判断有没有可抽的纸条
        $tape = new Tape();
        $smoke = $tape->rand_tape($data);
        if (empty($smoke)) {
            return $this->json_rewrite(['code' => 1, 'msg' => '没有抽到合适的小纸条']);
        }
        $data['uid'] = $user['id'];
        return $this->pay($data, $getConfig, '抽了一张小纸条', abs($money));
    }

    /**
     * 放入小纸条支付
     */
    public function tape_pay()
    {
        $data = input('param.');
        //查询用户
        $user = Db::name('user')->where('user_wechat_open_id', $data['openid'])->where('token', $data['token'])->find();
        if (empty($user)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '参数错误！']);
        }
        $getConfig = cache('fatal_' . $data['much_id']);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $data['much_id'])->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $data['much_id'], $getConfig);
            }
        }
        define("APPID", $getConfig['app_id']);
        define("MCHID", $getConfig['app_mchid']);
        define("KEY", $getConfig['app_key']);
        define("APPSECRET", $getConfig['app_secret']);

        //获取纸条配置
        $config = Db::name('feeling_stipulate')->where('much_id', $data['much_id'])->find();
        //查询是否有放入限制
        if (!empty($config) && $config['throw_limit'] > 0) {
            //查询我今日放入的
            $count = Db::name('feeling')
                ->where('user_id', $user['id'])
                ->where('much_id', $data['much_id'])
                ->whereTime('create_time', 'today')
                ->count();
            if ($count >= $config['throw_limit']) {
                return $this->json_rewrite(['code' => 1, 'msg' => '今日放入小纸条已达上限']);
            }
        }
        $money = 0;
        if (!empty($config)) {
            if ($data['f_key'] == 1) {
                $money = $config['throw_price_male'];
            } else {
                $money = $config['throw_price_female'];
            }
        }
        $data['pay_type'] = 1;
        $data['uid'] = $user['id'];
        return $this->pay($data, $getConfig, '放了一张小纸条', abs($money));
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        $data = input('param.');
        $unfamiliar = Suspense::silence();
        $rhododendron = MagicTrick::pinafore();
        $foreign = MagicTrick::headpiece();
        $roseate = false;
        if ($rhododendron == $foreign) {
            Cache::clear();
            $roseate = true;
        }
        if ($roseate) {
            $abnormal = Suspense::silence();
        } else {
            $abnormal = $unfamiliar;
        }
        if ($unfamiliar !== $abnormal) {
            $rhododendron = MagicTrick::pinafore();
            Pisces::slothful($data['much_id']);
        }
        $external = MagicTrick::chapeau();
        if ($rhododendron != $external) {
            echo 'error';
            exit();
        }
    }

    /**
     * 支付
     */
    public function pay($data, $getConfig, $msg, $money)
    {
        //订单号
        $order_no = time() . rand(10000, 999999);
        //     初始化值对象
        $input = new \WxPayUnifiedOrder();
        $input->SetAttach($data['pay_type'] . '');
        //     文档提及的参数规范：商家名称-销售商品类目
        $input->SetBody($msg);
        //     订单号应该是由小程序端传给服务端的，在用户下单时即生成，demo中取值是一个生成的时间戳
        $input->SetOut_trade_no($order_no);
        //     费用应该是由小程序端传给服务端的，在用户下单时告知服务端应付金额，demo中取值是1，即1分钱
        $moeny = intval(bcmul($money, 100));
        $input->SetTotal_fee($moeny);
        //$input->SetTotal_fee(1);
        //     支付类型
        $input->SetTrade_type("JSAPI");
        //     由小程序端传给服务端
        $input->SetOpenid($data['openid']);
        //     回调地址
        $input->SetNotify_url($getConfig['pay_react']);
        //     向微信统一下单，并返回order，它是一个array数组
        $order = \WxPayApi::unifiedOrder($input);
        //     json化返回给小程序端
        header("Content-Type: application/json");
        $order['app_info'] = ['app_key' => $getConfig['app_key']];
        $order['number'] = $order_no;
        $res=Db::name('user_serial')->insertGetId(['add_time' => time(), 'money' => $money, 'user_id' => $data['uid'], 'single_mark' => $order_no, 'much_id' => $data['much_id']]);
        if ($data['pay_type'] == 2) {
            Db::name('shop_order')->where('id', $data['order_id'])->update(['order_number' => $order_no]);
        }
        if ($data['pay_type'] == 3) {
            Db::name('lost_item_top')->insert(['add_time'=>time(),'usl_id' => $res,'li_id'=>$data['lost_id'],'user_id'=> $data['uid'],'top_day'=>$data['top_day'],'pay_type'=>2,'pay_price'=>$money, 'much_id' => $data['much_id']]);
        }
        if ($data['pay_type'] == 4) {
            Db::name('used_goods_item_top')->insert(['add_time'=>time(),'usl_id' => $res,'ugi_id'=>$data['lost_id'],'user_id'=> $data['uid'],'top_day'=>$data['top_day'],'pay_type'=>2,'pay_price'=>$money, 'much_id' => $data['much_id']]);
        }
        if ($data['pay_type'] == 5) {
            Db::name('employment_item_top')->insert(['add_time'=>time(),'usl_id' => $res,'ei_id'=>$data['lost_id'],'user_id'=> $data['uid'],'top_day'=>$data['top_day'],'pay_type'=>2,'pay_price'=>$money, 'much_id' => $data['much_id']]);
        }
        return $this->json_rewrite($order);
    }
}