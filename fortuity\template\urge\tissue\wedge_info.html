<!DOCTYPE html>
<html lang="zh-cn">

<head>
    <meta charset="UTF-8">
    <title>小纸条详情</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="never">
    <link rel="stylesheet" href="assets/css/amazeui.min.css" />
    <style>body{background-color:#f5f5f5;padding:20px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji"}.details-container{background:#fff;border-radius:4px;border:1px solid #e8e8e8;padding:25px 30px;margin:0 auto;max-width:900px}.details-header{padding-bottom:15px;margin-bottom:20px;border-bottom:1px solid #f0f0f0}.details-title{font-size:20px;font-weight:600;color:#2c3e50;margin:0}.details-list{margin:0;padding:0}.details-item{display:flex;padding:12px 0;font-size:14px;border-bottom:1px solid #f5f5f5;align-items:flex-start}.details-item:last-child{border-bottom:none}.item-label{flex:0 0 140px;color:#555;font-weight:500}.item-content{flex:1;color:#333}.item-content a{color:#3498db;text-decoration:none}.item-content a:hover{text-decoration:underline}.content-block{margin-top:25px;flex-wrap:wrap}.content-block .item-label{margin-bottom:10px;flex-basis:100%}.content-block .item-content{flex-basis:100%;background-color:#f9f9f9;padding:20px;border-radius:4px;border:1px solid #eee;white-space:pre-wrap;word-wrap:break-word;line-height:1.7;color:#2c3e50}.item-content img{max-width:100%;height:auto;border-radius:4px}dt+dd{margin-top:0}</style>
</head>
<body>

    <div class="details-container">
        <header class="details-header">
            <h1 class="details-title">小纸条详情</h1>
        </header>

        <dl class="details-list">
            <div class="details-item">
                <dt class="item-label">用户昵称</dt>
                <dd class="item-content">
                    {if $list.user.uvirtual == 0}
                    <a href="{:url('user/index')}&openid={$list.user.user_wechat_open_id}&page=1"
                        title="{$list.user.user_nick_name|emoji_decode}" target="_blank">
                        {$list.user.user_nick_name|emoji_decode}
                    </a>
                    {else}
                    <a href="{:url('user/theoretic')}&hazy_name={$list.user.user_nick_name|filter_emoji}&page=1"
                        target="_blank" title="{$list.user.user_nick_name|emoji_decode}">
                        {$list.user.user_nick_name|emoji_decode}
                    </a>
                    {/if}
                </dd>
            </div>
            <div class="details-item">
                <dt class="item-label">联系方式</dt>
                <dd class="item-content">{$list.contact_person|default='未提供'}</dd>
            </div>
            <div class="details-item">
                <dt class="item-label">用户年龄</dt>
                <dd class="item-content">{$list.age}</dd>
            </div>
            <div class="details-item">
                <dt class="item-label">用户性别</dt>
                <dd class="item-content">{if $list.gender == 0} 女 {else} 男 {/if}</dd>
            </div>
            <div class="details-item">
                <dt class="item-label">用户星座</dt>
                <dd class="item-content">
                    {switch $list.constellation}
                    {case 0}白羊座{/case}
                    {case 1}金牛座{/case}
                    {case 2}双子座{/case}
                    {case 3}巨蟹座{/case}
                    {case 4}狮子座{/case}
                    {case 5}处女座{/case}
                    {case 6}天秤座{/case}
                    {case 7}天蝎座{/case}
                    {case 8}射手座{/case}
                    {case 9}魔羯座{/case}
                    {case 10}水瓶座{/case}
                    {case 11}双鱼座{/case}
                    {default /}未知
                    {/switch}
                </dd>
            </div>
            <div class="details-item">
                <dt class="item-label">所在城市</dt>
                <dd class="item-content"><span id="remainCity">加载中...</span></dd>
            </div>
            <div class="details-item">
                <dt class="item-label">抽取城市限制</dt>
                <dd class="item-content"><span id="restrictCity">加载中...</span></dd>
            </div>
            <div class="details-item">
                <dt class="item-label">被抽取次数限制</dt>
                <dd class="item-content">{if $list.longevity==0} 不限制 {else} {$list.longevity}次 {/if}</dd>
            </div>
            <div class="details-item">
                <dt class="item-label">纸条被抽取次数</dt>
                <dd class="item-content">{$list.ufCount}次</dd>
            </div>
            <div class="details-item">
                <dt class="item-label">放入时间</dt>
                <dd class="item-content">{:date('Y-m-d H:i:s',$list.create_time)}</dd>
            </div>
            <div class="details-item">
                <dt class="item-label">审核状态</dt>
                <dd class="item-content">
                    {if $list.check_status == 0} <span class="am-text-warning">待审核</span>
                    {elseif $list.check_status == 1} <span class="am-text-success">已通过</span>
                    {elseif $list.check_status == 2} <span class="am-text-danger">已拒绝</span>
                    {/if}
                </dd>
            </div>
            <div class="details-item">
                <dt class="item-label">审核时间</dt>
                <dd class="item-content">{if $list.check_time}{:date('Y-m-d H:i:s',$list.check_time)}{else}无{/if}</dd>
            </div>
            <div class="details-item">
                <dt class="item-label">审核意见</dt>
                <dd class="item-content">{if $list.check_opinion}{$list.check_opinion}{else}无{/if}</dd>
            </div>
            <div class="details-item">
                <dt class="item-label">纸条内容</dt>
                <dd class="item-content">
                    {$list.hedge_content}
                </dd>
            </div>
        </dl>
    </div>

</body>
<script>
    !function () {
        var province_list = {
            110000: '北京市',
            120000: '天津市',
            130000: '河北省',
            140000: '山西省',
            150000: '内蒙古自治区',
            210000: '辽宁省',
            220000: '吉林省',
            230000: '黑龙江省',
            310000: '上海市',
            320000: '江苏省',
            330000: '浙江省',
            340000: '安徽省',
            350000: '福建省',
            360000: '江西省',
            370000: '山东省',
            410000: '河南省',
            420000: '湖北省',
            430000: '湖南省',
            440000: '广东省',
            450000: '广西壮族自治区',
            460000: '海南省',
            500000: '重庆市',
            510000: '四川省',
            520000: '贵州省',
            530000: '云南省',
            540000: '西藏自治区',
            610000: '陕西省',
            620000: '甘肃省',
            630000: '青海省',
            640000: '宁夏回族自治区',
            650000: '新疆维吾尔自治区',
            710000: '台湾省',
            810000: '香港特别行政区',
            820000: '澳门特别行政区',
            900000: '海外',
        }
        var remainCity = province_list[Number('{$list.remain_city}')];
        var restrictCity = province_list[Number('{$list.restrict_city}')];
        if (remainCity === undefined) {
            remainCity = '未知';
        }
        if (restrictCity === undefined) {
            restrictCity = '不限制';
        }
        document.querySelector('#remainCity').textContent = remainCity;
        document.querySelector('#restrictCity').textContent = restrictCity;
    }();
</script>

</html>