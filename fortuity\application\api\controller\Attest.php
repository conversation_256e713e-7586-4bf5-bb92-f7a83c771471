<?php

namespace app\api\controller;

use app\api\service\Util;
use app\common\RedisLock;
use think\Cache;
use think\Db;

class Attest extends Base
{
    public function check_attest()
    {
        $data = input('param.');
        $info = Db::name('user_attest')
            ->where('much_id', $data['much_id'])
            ->where('user_id', $this->user_info['id'])
            ->where('adopt_status', 1)
            ->field('ut_inject,at_id')
            ->find();
        return $this->json_rewrite(['code' => empty($info) ? 0 : 1]);
    }

    //激活卡密
    public function activation_card()
    {
        $data = input('param.');
        $plug = new Plugunit();
        $renzheng = $plug->check_plug('0e5d1b87-c743-7b40-96d0-c5b6b43b48c4',$data['much_id']);
        if (!$renzheng) {
            return $this->json_rewrite(['code' => 0, 'msg' => '未开通此功能插件！']);
        }
        // 启动事务
        $redis = new RedisLock();
        if ($redis->_redis != null) {
            $lockStatus = $redis->lock('card_lock' . $data['much_id'], 3);
            if ($lockStatus) {
                //  解锁
                $res = $this->activation_card_do($data);
                $redis->unlock('card_lock' . $data['much_id']);
                return $res;

            } else {
                return $this->json_rewrite(['code' => 1, 'msg' => '点的太快了，请稍后重试！']);
            }
        } else {
            $randomLock = md5(uniqid(mt_rand(), true));
            $getLocked = Cache::remember('card_lock' . $data['much_id'], $randomLock, 3);
            if ($randomLock == $getLocked) {

                $res = $this->activation_card_do($data);
                Cache::rm('card_lock' . $data['much_id']);
                return $res;
            } else {
                return $this->json_rewrite(['code' => 1, 'msg' => '点的太快了，请稍后重试！']);
            }
        }
    }

    public function activation_card_do($data)
    {
        $util = new Util();
        //去除空格
        $card = str_replace(" ", '', $data['card_code']);
        //检测是否已经使用
        $info = Db::name('mucilage')->where('card_code', $card)->where('much_id', $data['much_id'])->find();
        if ($info['is_use'] == 1 || $info['status'] == 0 || $info['is_del'] == 1) {//已使用
            return $this->json_rewrite(['code' => 0, 'msg' => '卡密状态异常！']);
        }
        if ($info['financial_type'] == 0) {
            Db::rollback();
            return $this->json_rewrite(['code' => 1, 'msg' => '目前仅支持系统内部卡密兑换！']);
        }
        $ip = \request()->ip();
        Db::startTrans();
        try {
            //使用当前卡密
            $up_card = Db::name('mucilage')->where('id', $info['id'])->update(['is_use' => 1]);
            if (!$up_card) {
                Db::rollback();
                return $this->json_rewrite(['code' => 1, 'msg' => '兑换失败1！']);
            }
            //记录当前用户使用情况
            $use_card = Db::name('mucilage_use_annaly')->insert(['mu_id' => $info['id'], 'user_id' => $this->user_info['id'], 'use_ip' => $ip, 'use_time' => time(), 'much_id' => $data['much_id']]);
            if (!$use_card) {
                Db::rollback();
                return $this->json_rewrite(['code' => 1, 'msg' => '兑换失败2！']);
            }
            $user_info = $util->get_openId_user($data['openid'], $data['much_id']);
            //贝壳卡
            if ($info['financial_type'] == 1) {
                $money = bcadd($user_info['conch'], $info['face_value'], 2);
                $solution = '卡密兑换' . $info['face_value'] . $this->design['currency'];
                $msg = "成功兑换：" . $info['face_value'] . $this->design['currency'];
                $ins_amount = $util->user_amount($this->user_info['id'], 3, $info['face_value'], $user_info['fraction'], $user_info['fraction'], $user_info['conch'], $money, 0, $solution, $data['much_id']);
                if (!$ins_amount) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '兑换失败3！']);
                }
                $user_set = Db::name('user')->where('much_id', $data['much_id'])->where('id', $this->user_info['id'])->update(['conch' => $money]);
                if (!$user_set) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '兑换失败4！']);
                }
            } //积分卡
            if ($info['financial_type'] == 2) {
                $money = bcadd($user_info['fraction'], $info['face_value'], 2);
                $solution = '卡密兑换' . $info['face_value'] . $this->design['confer'];
                $msg = "成功兑换：" . $info['face_value'] . $this->design['confer'];
                $ins_amount = $util->user_amount($this->user_info['id'], 3, $info['face_value'], $user_info['fraction'], $money, $user_info['conch'], $user_info['conch'], 1, $solution, $data['much_id']);
                if (!$ins_amount) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '兑换失败5！']);
                }
                $user_set = Db::name('user')->where('much_id', $data['much_id'])->where('id', $this->user_info['id'])->update(['fraction' => $money]);
                if (!$user_set) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '兑换失败6！']);
                }
            }
            if ($info['financial_type'] == 3) { //会员卡
                $msg = "成功兑换：" . $info['face_value'] . '天会员';
                $money = bcmul($info['face_value'], 86400, 2);
                if ($user_info['vip_end_time'] < time()) {
                    //用户已经不是会员  当前时间+ n个月的秒
                    $vip_end_time = bcadd(time(), $money);
                } else {
                    //用户是会员  到期时间+ n天的秒
                    $vip_end_time = bcadd($user_info['vip_end_time'], $money);
                }
                $user_set = Db::name('user')->where('much_id', $data['much_id'])->where('id', $this->user_info['id'])->update(['vip_end_time' => $vip_end_time]);
                if (!$user_set) {
                    Db::rollback();
                    return $this->json_rewrite(['code' => 1, 'msg' => '兑换失败7！']);
                }
            }
            // 提交事务
            Db::commit();
            return $this->json_rewrite(['code' => 0, 'msg' => $msg]);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['code' => 1, 'msg' => '系统错误！', 'key' => $e->getMessage()]);
        }
    }

    /**
     * 激活记录
     */
    public function activation_log()
    {
        $data = input('param.');
        $page = $data['page'];
        $list = Db::name('mucilage_use_annaly')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->page($page, 15)
            ->field('id,mu_id,use_time,much_id,user_id')
            ->order('use_time desc')
            ->select();
        foreach ($list as $k => $v) {
            $mu = Db::name('mucilage')->where('id', $v['mu_id'])->where('much_id', $data['much_id'])->field('card_code,financial_type,face_value')->find();
            $list[$k]['mu_id'] = $mu['card_code'];
            if ($mu['financial_type'] == 1) {
                $msg = $mu['face_value'] . '' . $this->design['currency'];
            }
            if ($mu['financial_type'] == 2) {
                $msg = $mu['face_value'] . '' . $this->design['confer'];
            }
            if ($mu['financial_type'] == 3) {
                $msg = '会员' . $mu['face_value'] . '天';
            }
            $list[$k]['financial_type'] = $msg;
            $list[$k]['use_time'] = date('Y-m-d H:i', $v['use_time']);
        }
        return $this->json_rewrite($list);
    }
}