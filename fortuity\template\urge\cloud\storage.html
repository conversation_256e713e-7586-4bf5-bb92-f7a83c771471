{extend name="/base"/}
{block name="main"}
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 网盘存储
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-6 am-u-sm-offset-2">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group" style="margin-top: 40px;">
                        <label class="am-u-sm-3 am-form-label">文件存储地址</label>
                        <div class="am-u-sm-9 am-u-end">
                            <select v-model="type">
                                <option value="-1">跟随远程附件设置</option>
                                <option value="0">本地存储</option>
                                <option value="1">阿里云OSS</option>
                                <option value="2">七牛云存储</option>
                                <option value="3">腾讯云COS</option>
                                <option value="4">又拍云存储</option>
                                <option value="5">FTP存储</option>
                            </select>
                        </div>
                    </div>
                    <div v-if="type == 1">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">AccessKey ID</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.oss.oss_access_key_id" placeholder="{if $list.oss_follow.oss_access_key_id}{$list.oss_follow.oss_access_key_id|ciphertext}{else}请输入阿里云AccessKey ID{/if}">
                                <small>Access Key ID是您访问阿里云API的密钥，具有该账户完全的权限，请您妥善保管。</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Access Key Secret</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.oss.oss_access_key_secret" placeholder="{if $list.oss_follow.oss_access_key_secret}{$list.oss_follow.oss_access_key_secret|ciphertext}{else}请输入阿里云Access Key Secret{/if}">
                                <small>Access Key Secret是您访问阿里云API的密钥，具有该账户完全的权限，请您妥善保管。</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket</label>
                            <div class="am-u-sm-9">
                                <input type="text"  v-model="list.oss.oss_bucket" placeholder="请输入阿里云存储空间的名字">
                                <small>请保证Bucket为可公共读取的</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket所在区域</label>
                            <div class="am-u-sm-9">
                                <select v-model="list.oss.oss_endpoint">
                                    <option value="0">请选择区域</option>
                                    {volist name="ossEndpoint" id="vo"}
                                    <option value="{$vo.value}">{$vo.key}</option>
                                    {/volist}
                                </select>
                                <small>请选择Bucket对应的地域节点</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text"  v-model="list.oss.oss_url" placeholder="请输入阿里云绑定的外链域名或自定义外链域名">
                                <small>阿里云支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                    </div>
                    <div v-if="type == 2">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">AccessKey</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.qiniu.qiniu_access_key" placeholder="{if $list.qiniu_follow.qiniu_access_key}{$list.qiniu_follow.qiniu_access_key|ciphertext}{else}请输入七牛云用于签名的公钥{/if}">
                                <small>用于签名的公钥</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">SecretKey</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.qiniu.qiniu_secret_key" placeholder="{if $list.qiniu_follow.qiniu_secret_key}{$list.qiniu_follow.qiniu_secret_key|ciphertext}{else}请输入七牛云用于签名的私钥{/if}">
                                <small>用于签名的私钥</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.qiniu.qiniu_bucket" value="{$list.qiniu_follow.qiniu_bucket}" placeholder="请输入七牛云存储空间的名字">
                                <small>请保证Bucket为可公共读取的</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.qiniu.qiniu_url" placeholder="请输入七牛云绑定的外链域名或自定义外链域名">
                                <small>七牛云支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">水印代码</label>
                            <div class="am-u-sm-9">
                                <textarea v-model="list.qiniu.qiniu_watermark" style="height:150px;resize:none;"></textarea>
                            </div>
                        </div>
                    </div>
                    <div v-if="type == 3">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">APPID</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.cos.cos_app_id" placeholder="请输入腾讯云APPID">
                                <small>APPID 是您项目的唯一标识号</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">SecretId</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.cos.cos_secret_id" placeholder="{if $list.cos_follow.cos_secret_id}{$list.cos_follow.cos_secret_id|ciphertext}{else}请输入腾讯云SecretId{/if}">
                                <small>SecretID 是您项目的安全秘钥，具有该账户完全的权限，请妥善保管</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">SecretKey</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.cos.cos_secret_key" placeholder="{if $list.cos_follow.cos_secret_key}{$list.cos_follow.cos_secret_key|ciphertext}{else}请输入腾讯云SecretKey{/if}">
                                <small>SecretKey 是您项目的安全秘钥，具有该账户完全的权限，请妥善保管</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.cos.cos_bucket" placeholder="请输入腾讯云存储空间的名字">
                                <small>请保证Bucket为可公共读取的</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket所在区域</label>
                            <div class="am-u-sm-9">
                                <select v-model="list.cos.cos_region">
                                    <option value="0">请选择区域</option>
                                    {volist name="ossRegion" id="vo"}
                                    <option value="{$vo.value}">{$vo.key}</option>
                                    {/volist}
                                </select>
                                <small>请选择Bucket对应的区域</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.cos.cos_url" placeholder="请输入腾讯云绑定的外链域名或自定义外链域名">
                                <small>腾讯云支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                    </div>
                    <div v-if="type == 4">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">服务名</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.upyun.upyun_service_name" placeholder="请输入又拍云存储的服务名称">
                                <small>云存储服务名称</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">操作员名</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.upyun.upyun_operator_name" placeholder="{if $list.upyun_follow.upyun_operator_name}{$list.upyun_follow.upyun_operator_name|ciphertext}{else}请输入又拍云的操作员名{/if}">
                                <small>账户管理 -> 操作员 [不是登录用户名]</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">操作员密码</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.upyun.upyun_operator_password" placeholder="{if $list.upyun_follow.upyun_operator_password}{$list.upyun_follow.upyun_operator_password|ciphertext}{else}请输入又拍云的操作员密码{/if}">
                                <small>账户管理 -> 操作员密码 [不是登录密码]</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.upyun.upyun_url" placeholder="请输入又拍云绑定的外链域名或自定义外链域名">
                                <small>又拍云支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                    </div>
                    <div v-if="type == 5">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP服务器地址</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.ftp.ftp_host" placeholder="请输入FTP服务器地址">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP用户名</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.ftp.ftp_username" placeholder="{if $list.ftp_follow.ftp_username}{$list.ftp_follow.ftp_username|ciphertext}{else}请输入FTP用户名{/if}">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP密码</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.ftp.ftp_password" placeholder="{if $list.ftp_follow.ftp_password}{$list.ftp_follow.ftp_password|ciphertext}{else}请输入FTP密码{/if}">
                                <small>账户管理 -> 操作员密码 [不是登录密码]</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP被动模式</label>
                            <div class="am-u-sm-9">
                                <select v-model="list.ftp.ftp_pasv">
                                    <option value="1">开启</option>
                                    <option value="0">关闭</option>
                                </select>
                                <small>服务器安全组需开启 TCP 39000-40000 FTP被动模端口范围，关闭FTP被动模式可能会造成上传页面卡死或上传失败等情况。</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP端口号</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.ftp.ftp_port" placeholder="请输入又拍云绑定的外链域名或自定义外链域名">
                                <small>
                                    FTP端口号，默认21
                                </small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="list.ftp.ftp_url" placeholder="请输入FTP自定义访问域名">
                                <small>
                                    FTP自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group am-u-sm-offset-3" style="font-size: 14px;">
                        注意事项：<span style="color: red;">保存之前请检查您的配置是否正确，保存之后所更改的内容将会立即生效！</span>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-push-6">
                            <button type="button" class="am-btn am-btn-primary" @click="holdSave">
                                保存配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    new Vue({
        el: '#app',
        data() {
            return {
                type: Number('{$list.quicken_type}'),
                list: {
                    oss: {
                        'oss_access_key_id': '',
                        'oss_access_key_secret': '',
                        'oss_bucket': '{$list.oss_follow.oss_bucket}',
                        'oss_endpoint': '{if $list.oss_follow.oss_endpoint}{$list.oss_follow.oss_endpoint}{else}0{/if}',
                        'oss_url': '{$list.oss_follow.oss_url}',
                    }, qiniu: {
                        'qiniu_access_key': '',
                        'qiniu_secret_key': '',
                        'qiniu_bucket': '{$list.qiniu_follow.qiniu_bucket}',
                        'qiniu_url': '{$list.qiniu_follow.qiniu_url}',
                        'qiniu_watermark': '{$list.qiniu_follow.qiniu_watermark}',
                    }, cos: {
                        'cos_app_id': '{$list.cos_follow.cos_app_id}',
                        'cos_secret_id': '',
                        'cos_secret_key': '',
                        'cos_bucket': '{$list.cos_follow.cos_bucket}',
                        'cos_region': '{if $list.cos_follow.cos_region}{$list.cos_follow.cos_region}{else}0{/if}',
                        'cos_url': '{$list.cos_follow.cos_url}',
                    }, upyun: {
                        'upyun_service_name': '{$list.upyun_follow.upyun_service_name}',
                        'upyun_operator_name': '',
                        'upyun_operator_password': '',
                        'upyun_url': '{$list.upyun_follow.upyun_url}'
                    }, ftp: {
                        'ftp_host': '{$list.ftp_follow.ftp_host}',
                        'ftp_username': '{$list.ftp_follow.ftp_username}',
                        'ftp_password': '{$list.ftp_follow.ftp_password}',
                        'ftp_pasv': '{if $list.ftp_follow.ftp_pasv}1{else}0{/if}',
                        'ftp_port': '{$list.ftp_follow.ftp_port}',
                        'ftp_url': '{$list.ftp_follow.ftp_url}'
                    }
                }
            }
        }, methods: {
            holdSave() {
                var setData = {};
                switch (Number(this.type)) {
                    case 1:
                        setData = this.list.oss;
                        break;
                    case 2:
                        setData = this.list.qiniu;
                        break;
                    case 3:
                        setData = this.list.cos;
                        break;
                    case 4:
                        setData = this.list.upyun;
                        break;
                    case 5:
                        setData = this.list.ftp;
                        break;
                }
                setData['quicken_type'] = this.type;
                $.post("{:url('cloud/storage')}", setData, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
            }
        }
    })
</script>
{/block}