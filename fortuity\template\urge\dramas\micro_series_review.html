{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-comments {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .user-link {color: #495057;text-decoration: none;font-weight: 500;}
    .user-link:hover {color: #343a40;text-decoration: underline;}
    .drama-link {color: #23b7e5;text-decoration: none;font-weight: 500;}
    .drama-link:hover {color: #1a9bc0;text-decoration: underline;}
    .episode-number {font-weight: 600;color: #333;}
    .comment-content {max-width: 200px;word-wrap: break-word;line-height: 1.4;color: #333;text-align: center;margin: 0 auto;}
    .audit-status {display: inline-block;padding: 3px 8px;border-radius: 12px;font-size: 11px;font-weight: 500;}
    .audit-pending {background-color: #fff3cd;color: #856404;border: 1px solid #ffeaa7;}
    .audit-passed {background-color: #d4edda;color: #155724;border: 1px solid #c3e6cb;}
    .audit-rejected {background-color: #f8d7da;color: #721c24;border: 1px solid #f5c6cb;}
    .modern-dropdown {position: relative;display: inline-block;}
    .modern-dropdown-btn {background: linear-gradient(135deg, #23b7e5 0%, #1a9bc0 100%);border: none;color: white;padding: 8px 16px;border-radius: 6px;font-size: 13px;cursor: pointer;transition: all 0.3s ease;box-shadow: 0 2px 4px rgba(35,183,229,0.2);display: flex;align-items: center;gap: 6px;}
    .modern-dropdown-btn:hover {background: linear-gradient(135deg, #1a9bc0 0%, #1587a8 100%);box-shadow: 0 4px 8px rgba(35,183,229,0.3);transform: translateY(-1px);}
    .modern-dropdown .am-dropdown-content {border: none;border-radius: 6px;box-shadow: 0 4px 12px rgba(0,0,0,0.15);margin-top: 4px;overflow: hidden;}
    .modern-dropdown .am-dropdown-content li a {padding: 10px 16px;color: #333;transition: all 0.2s ease;display: flex;align-items: center;gap: 8px;}
    .modern-dropdown .am-dropdown-content li a:hover {background-color: #f8f9fa;color: #23b7e5;}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-comments"></span> 短剧视频评论列表
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="commonSearch();"></i>
                <input type="text" id="searchName" value="{$searchName}" placeholder="搜索评论内容...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="15%">用户信息</th>
                            <th width="15%">短剧名称</th>
                            <th width="10%">集数</th>
                            <th width="25%">评论内容</th>
                            <th width="10%">审核状态</th>
                            <th width="15%">评论时间</th>
                            <th width="10%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                {if $vo.user.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user.user_wechat_open_id}&page=1" title="{$vo.user.user_nick_name|emoji_decode}" target="_blank" class="user-link">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&searchName={$vo.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user.user_nick_name|emoji_decode}" class="user-link">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                <a href="{:url('dramas/micro_series_info')}&searchName={$vo.msi_title}" target="_self" class="drama-link">
                                    {$vo.msi_title}
                                </a>
                            </td>
                            <td class="am-text-middle">
                                <span class="episode-number">第{$vo.msc_episode_number}集</span>
                            </td>
                            <td class="am-text-middle">
                                <div class="comment-content">
                                    {$vo.comment|emoji_decode|$expressionHtml}
                                </div>
                            </td>
                            <td class="am-text-middle">
                                {switch $vo.status}
                                {case 0}
                                <span class="audit-status audit-pending">待审核</span>
                                {/case}
                                {case 1}
                                <span class="audit-status audit-passed">已通过</span>
                                {/case}
                                {case 2}
                                <span class="audit-status audit-rejected">已拒绝</span>
                                {/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.create_time)}
                            </td>
                            <td class="am-text-middle">
                                <div class="modern-dropdown am-dropdown" data-am-dropdown>
                                    <button class="modern-dropdown-btn am-dropdown-toggle" data-am-dropdown-toggle>
                                        <span>功能列表</span>
                                        <span class="am-icon-caret-down"></span>
                                    </button>
                                    <ul class="am-dropdown-content" style="min-width: 120px;">
                                        {if $vo.status==0}
                                        <li>
                                            <a href="javascript:void(0);" onclick="auditCorrect('{$vo.id}',1)">
                                                <span class="am-icon-check"></span> 审核通过
                                            </a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0);" onclick="auditCorrect('{$vo.id}',2)">
                                                <span class="am-icon-times"></span> 审核拒绝
                                            </a>
                                        </li>
                                        {/if}
                                        <li>
                                            <a href="javascript:void(0);" onclick="delCorrect('{$vo.id}')">
                                                <span class="am-icon-trash"></span> 删除评论
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>


    var auditCorrect = function (fid, process) {
        var twoCheck = function (fid, process, reaValue) {
            $.ajax({
                type: "post",
                url: "{:url('dramas/trial_micro_series_review')}",
                data: {'fid': fid, 'process': process, 'inject': reaValue},
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }
            });
        }
        switch (process) {
            case 1:
                layer.confirm('您确定要审核通过这条回复吗？', {
                    btn: ['确定', '取消'], title: '提示'
                }, function () {
                    twoCheck(fid, process);
                }, function (index) {
                    layer.close(index);
                });
                break;
            case 2:
                layer.prompt({
                    title: '请输入用户未通过审核的原因：',
                    formType: 2,
                    area: ['300px', '100px'],
                    btn: ['确定', '取消'],
                }, function (reaValue, index) {
                    if (reaValue.trim() === '') {
                        return false;
                    }
                    twoCheck(fid, process, reaValue);
                    layer.close(index);
                });
                break;
        }
    }

    var delCorrect = function (fid) {
        layer.confirm('您确定要删除这条数据吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('dramas/del_micro_series_review')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    var commonSearch = function () {
        var searchName = $.trim($('#searchName').val());
        if (searchName) {
            location.href = "{:url('dramas/micro_series_review')}&searchName=" + searchName + "&page={$page}";
        } else {
            location.href = "{:url('dramas/micro_series_review')}&page={$page}";
        }
    }
</script>
{/block}