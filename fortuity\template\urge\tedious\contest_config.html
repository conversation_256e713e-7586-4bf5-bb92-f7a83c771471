{extend name="/base"/}
{block name="main"}
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 抽奖配置
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-6 am-u-sm-push-3" style="margin-top: 50px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">自定义标题</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.customTitle" placeholder="请输入自定义标题">
                            <small>抽奖功能自定义标题</small>
                        </div>
                    </div>
                    <div class="am-form-group am-margin-top-xl">
                        <label class="am-u-sm-3 am-form-label">激励视频ID</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.AD1" placeholder="激励视频ID">
                            <small>小程序流量主激励式视频广告ID</small>
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;margin: 50px 0 30px 0;">
                        <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    new Vue({
        el: '#app',
        data() {
            return {
                item: {
                    customTitle: '{$list.custom_title}',
                    AD1: '{$list.ad_1}'
                }
            }
        }, methods: {
            holdSave() {
                var setData = {};
                setData['customTitle'] = $.trim(this.item.customTitle);
                setData['AD1'] = $.trim(this.item.AD1);
                $.post("{:url('tedious/contest_config')}", {'item': setData}, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
            }
        }
    });
</script>
{/block}