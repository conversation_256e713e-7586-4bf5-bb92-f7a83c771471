{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-cogs {margin-right: 5px;color: #23b7e5;}
    .am-form-horizontal {background: #fff;border-radius: 6px;padding: 20px;box-shadow: 0 1px 3px rgba(0,0,0,0.05);}
    .am-form-group {margin-bottom: 20px;padding: 15px 0;border-bottom: 1px solid #f5f5f5;}
    .am-form-group:last-child {border-bottom: none;}
    .am-form-label {font-weight: 500;color: #333;line-height: 1.6;}
    .am-form-group input[type="text"] {height: 36px;padding: 8px 12px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;font-size: 14px;}
    .am-form-group input[type="text"]:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .am-form-group select {height: 36px;padding: 8px 12px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;font-size: 14px;}
    .am-form-group select:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .am-form-group small {color: #666;font-size: 12px;margin-top: 5px;display: block;line-height: 1.4;}
    .config-section {background: #f8f9fa;border-radius: 6px;padding: 20px;margin-bottom: 20px;border-left: 4px solid #23b7e5;}
    .config-section-title {font-size: 16px;font-weight: 600;color: #23b7e5;margin-bottom: 15px;}
    .save-btn {background: linear-gradient(135deg, #23b7e5 0%, #1a9bc0 100%);border: none;color: white;padding: 12px 30px;border-radius: 6px;font-size: 14px;font-weight: 500;cursor: pointer;transition: all 0.3s ease;box-shadow: 0 2px 4px rgba(35,183,229,0.2);}
    .save-btn:hover {background: linear-gradient(135deg, #1a9bc0 0%, #1587a8 100%);box-shadow: 0 4px 8px rgba(35,183,229,0.3);transform: translateY(-1px);}
    .save-btn:active {transform: translateY(0);box-shadow: 0 2px 4px rgba(35,183,229,0.2);}
    .percentage-input {display: flex;align-items: center;gap: 10px;}
    .percentage-input input {flex: 1;}
    .percentage-symbol {color: #666;font-weight: 500;}
    .editor-style {margin-top: 10px;}
    .am-modal-hd .am-close {top: -5px;right: -3px;}
    .w-e-menu {font-size: 12px;}
    .w-e-text, .w-e-text-container {height: 500px !important;}
</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-cogs"></span> 短剧配置
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-8 am-u-sm-push-1" style="margin-top: 30px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">短剧模块标题名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.customTitle" placeholder="请输入标题名称">
                            <small>自定义标题名称</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">VIP免费观看付费内容</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.isVipFreeLook">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>开启后VIP用户无需付费即可免费观看付费内容</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">是否允许用户上传短剧</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.isAllowUserUpload">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>开启后普通用户可以上传短剧视频</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">短剧信息是否自动审核</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.isInfoAutoReview">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>开启后普通用户可以直接发布短剧信息、无需审核</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">短剧内容是否自动审核</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.isContentAutoReview">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>开启后普通用户可以直接上传短剧视频、无需审核</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">短剧评论是否自动审核</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.isCommentAutoReview">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>开启后普通用户可以评论短剧视频、无需审核</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">需要用户拥有版权或授权</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.isRequireUserCopyright">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>开启后普通用户发布短剧时必须有版权活授权</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">允许用户短剧收费</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.isAllowUserCharge">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>开启后普通用户发布短剧时可以设置付费视频并获得收益</small>
                        </div>
                    </div>

                    <template v-if="item.isAllowUserCharge == 1">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">最小收费金额</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="item.minAmountCharged" oninput="grender(this,2)">
                                <small>开启后普通用户发布短剧时可以设置付费视频的最小收费金额</small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">最大收费金额</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="item.maxAmountCharged" oninput="grender(this,2)">
                                <small>开启后普通用户发布短剧时可以设置付费视频的最大收费金额</small>
                            </div>
                        </div>
                    </template>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">每天免费看付费内容次数</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.everyDayFreeLookNum" oninput="grender(this,0)">
                            <small>开启后普通用户观看付费短剧时可以免费观看的次数</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">观看广告解锁付费内容</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.isEnabledLookAdsUnlockPaidContent">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>开启后普通用户可以观看广告解锁付费内容</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">观看广告解锁付费内容最大次数</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.lookAdsUnlockPaidContentMaxNum" oninput="grender(this,0)">
                            <small>开启后普通用户每日可以通过观看广告解锁付费内容最大次数 0.不限制次数</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">收费短剧平台抽成比例</label>
                        <div class="am-u-sm-9">
                            <div class="percentage-input">
                                <input type="text" v-model="item.chargedProfitRakeRatio" oninput="grender(this,0)">
                                <span class="percentage-symbol">%</span>
                            </div>
                            <small>用户上传收费短剧，其他用户付费观看短剧时获得的收益，平台的抽成比例 </small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">用户协议</label>
                        <div class="am-u-sm-9 am-u-end">
                            <div id="userAgreementDetail" class="editor-style" style="min-height:600px;">{$list.user_agreement|$richTextCompatible}</div>
                            <span class="customizeGallery" style="display:none;" onclick="cuonice(0);"></span>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">免责声明</label>
                        <div class="am-u-sm-9 am-u-end">
                            <div id="disclaimerWarrantiesDetail" class="editor-style" style="min-height:600px;">{$list.disclaimer_warranties|$richTextCompatible}</div>
                            <span class="customizeGallery" style="display:none;" onclick="cuonice(1);"></span>
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;margin: 30px 0;border-bottom: none;">
                        <button type="button" class="save-btn" @click="holdSave">
                            保存配置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js?time={:time()}"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css?time={:time()}">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js?time={:time()}"></script>
<script>

    var vm = new Vue({
        el: '#app',
        data() {
            return {
                item: {
                    customTitle: '',
                    isVipFreeLook: '',
                    isAllowUserUpload: '',
                    isInfoAutoReview: '',
                    isContentAutoReview: '',
                    isCommentAutoReview: '',
                    isRequireUserCopyright: '',
                    isAllowUserCharge: '',
                    minAmountCharged: '',
                    maxAmountCharged: '',
                    everyDayFreeLookNum: '',
                    isEnabledLookAdsUnlockPaidContent: '',
                    lookAdsUnlockPaidContentMaxNum: '',
                    chargedProfitRakeRatio: '',
                    userAgreement: '',
                    disclaimerWarranties: ''
                },
                E: [],
                editor: [],
                editor2: []
            }
        }, created() {
            this.item.customTitle = '{$list.custom_title}';
            this.item.isVipFreeLook = '{$list.is_vip_free_look}';
            this.item.isAllowUserUpload = '{$list.is_allow_user_upload}';
            this.item.isInfoAutoReview = '{$list.is_info_auto_review}';
            this.item.isContentAutoReview = '{$list.is_content_auto_review}';
            this.item.isCommentAutoReview = '{$list.is_comment_auto_review}';
            this.item.isRequireUserCopyright = '{$list.is_require_user_copyright}';
            this.item.isAllowUserCharge = '{$list.is_allow_user_charge}';
            this.item.minAmountCharged = '{$list.min_amount_charged}';
            this.item.maxAmountCharged = '{$list.max_amount_charged}';
            this.item.everyDayFreeLookNum = '{$list.every_day_free_look_num}';
            this.item.isEnabledLookAdsUnlockPaidContent = '{$list.is_enabled_look_ads_unlock_paid_content}';
            this.item.lookAdsUnlockPaidContentMaxNum = '{$list.look_ads_unlock_paid_content_max_num}';
            this.item.chargedProfitRakeRatio = '{$list.charged_profit_rake_ratio*100}';
        }, mounted: function () {
            this.E = window.wangEditor;

            this.editor = new this.E('#userAgreementDetail');
            this.editor.customConfig.uploadImgServer = true;
            this.editor.create();
            this.E.fullscreen.init('#disclaimerWarranties');

            this.editor2 = new this.E('#disclaimerWarrantiesDetail');
            this.editor2.customConfig.uploadImgServer = true;
            this.editor2.create();
            this.E.fullscreen.init('#disclaimerWarrantiesDetail');
        },
        methods: {
            holdSave() {
                this.item.userAgreement = $.trim(this.editor.txt.html());
                this.item.disclaimerWarranties = $.trim(this.editor2.txt.html());
                $.post("{:url('dramas/micro_series_config')}", {'item': this.item}, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
            }
        }
    });

    var grender = function (obj, limit) {
        if (limit === 2) {
            obj.value = obj.value.replace(/[^\d.]/g, "");
            obj.value = obj.value.replace(/^\./g, "");
            obj.value = obj.value.replace(/\.{2,}/g, ".");
            obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        } else {
            obj.value = Number((obj.value.match(/^\d+(?:\.\d{0})?/)));
        }
    }

    var cuonice = function (index) {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogimages')}&gclasid=0&pictureIndex=" + index, 'no']
        });
    }

    var sutake = function (eurl, type) {
        switch (type) {
            case 0:
                vm.editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
                break;
            case 1:
                vm.editor2.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
                break;
        }
        layer.closeAll();
    }
</script>
{/block}