{extend name="/base"/}
{block name="main"}
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;}.tpl-toolbar{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;}.am-input-group-sm .am-form-field,.am-input-group-sm .am-input-group-btn .am-btn{height:32px;}.action-btn{display:inline-block;padding:5px 10px;font-size:13px;font-weight:normal;line-height:1.4;text-align:center;white-space:nowrap;vertical-align:middle;cursor:pointer;border:1px solid #ccc;border-radius:4px;transition:all .3s;}.action-btn:hover{background-color:#f5f5f5;}.action-btn .am-icon-trash-o,.action-btn .am-icon-copy,.action-btn .am-icon-repeat,.action-btn .am-icon-adn{margin-right:4px;}.action-btn.btn-add{color:#fff;background-color:#23b7e5;border-color:#23b7e5;}.action-btn.btn-add:hover{background-color:#1a9fd4;}.action-btn.btn-delete{color:#dd514c;background-color:white;}.action-btn.btn-delete:hover{color:#fff;background-color:#dd514c;}.action-btn.btn-approve{color:#fff;background-color:#5eb95e;border-color:#5eb95e;}.action-btn.btn-approve:hover{background-color:#449d44;}.action-btn.btn-reject{color:#fff;background-color:#dd514c;border-color:#dd514c;}.action-btn.btn-reject:hover{background-color:#c9302c;}.action-btn.btn-return{color:#333;background-color:#fff;border-color:#ccc;}.action-btn.btn-return:hover{background-color:#e6e6e6;}.confirm-btn{color:#fff;background-color:#23b7e5;border-color:#23b7e5;padding:6px 15px;font-size:13px;border-radius:3px;cursor:pointer;}.confirm-btn:hover{background-color:#1a9fd4;}.filter-btn-group .am-btn{border-radius:3px;}.filter-btn-group .am-btn.active{background-color:#23b7e5;color:white;border-color:#23b7e5;}.filter-btn-group a{margin-right:5px;}.am-table{border:1px solid #f0f0f0;border-radius:4px;font-size:13px;}.am-table > thead:first-child > tr:first-child > th{background-color:#f9f9f9;text-align:center;padding:10px 8px;font-weight:500;}.am-table > tbody > tr > td{text-align:center;vertical-align:middle;}.tpl-form-input{display:block;width:100%;height:32px;padding:6px 12px;font-size:13px;border:1px solid #ccc;border-radius:4px;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;}.tpl-form-input:focus{border-color:#23b7e5;box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(35,183,229,.6);}textarea.tpl-form-input{height:auto;}.am-badge{display:inline-block;padding:.25em .6em;font-size:75%;font-weight:700;line-height:1;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25rem;}.am-badge-success{color:#fff;background-color:#5eb95e;}.am-badge-warning{color:#fff;background-color:#f39c12;}.am-badge-danger{color:#fff;background-color:#dd514c;}</style>
<div class="tpl-portlet-components">
    <div class="tpl-block">
        <div class="am-g tpl-toolbar">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <button type="button" class="action-btn btn-add" data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 400, height: 225}" onclick="brandNew();">
                            <span class="am-icon-adn"></span> 新增话题
                        </button>
                    </div>
                    <div class="am-btn-group am-btn-group-xs filter-btn-group" style="margin-left: 10px;margin-top: 2.5px;">
                        <a href="{:url('compass/theme')}&egon=0" class="am-btn am-btn-default {if $egon==0}active{/if}">正常显示</a>
                        <a href="{:url('compass/theme')}&egon=1" class="am-btn am-btn-default {if $egon==1}active{/if}">回收站</a>
                    </div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-3 am-u-md-offset-3">
                <div class="am-input-group am-input-group-sm">
                    <input type="text" id="fz_name" value="{$hazy_name}" class="am-form-field" placeholder="搜索话题...">
                    <span class="am-input-group-btn">
                        <button class="am-btn  am-btn-default am-btn-success tpl-am-btn-success am-icon-search" onclick="fuzzy();" type="button"></button>
                    </span>
                </div>
            </div>
        </div>

        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover table-main">
                        <thead>
                        <tr>
                            <th width="15%">排序</th>
                            <th width="15%">GNID</th>
                            <th width="17.5%">话题名称</th>
                            <th width="17.5%">添加时间</th>
                            <th width="17.5%">跳转链接</th>
                            <th width="17.5%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                <input type="text" class="tpl-form-input scfixed" id="sfixed-{$vo.id}" value="{$vo.scores}" data-score="{$vo.scores}" style="width: 60px;" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                            </td>
                            <td>
                               {$vo.id}
                            </td>
                            <td>
                                <a href="{:url('essay/index')}&egon=0&tgid={$vo.id}" target="_blank">
                                {$vo.gambit_name}
                                </a>
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.add_time)}</td>
                            <td>
                                <div class="am-btn-toolbar">
                                    <div class="am-btn-group am-btn-group-xs">
                                        <button type="button" class="action-btn" onclick="exerox(this);">
                                            <input type="hidden" value="/yl_welore/pages/gambit/index?id={$vo.id}">
                                            <span class="am-icon-copy"></span> 复制链接
                                        </button>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="am-btn-toolbar">
                                    <div class="am-btn-group am-btn-group-xs">
                                        {if $egon==0}
                                        <button type="button" class="action-btn btn-delete" onclick="themelintDel('{$vo.id}');">
                                            <span class="am-icon-trash-o"></span> 删除
                                        </button>
                                        {else}
                                        <button type="button" class="action-btn" onclick="themelintRepeat('{$vo.id}');">
                                            <span class="am-icon-repeat"></span> 恢复
                                        </button>
                                        {/if}
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
        <div class="am-modal-dialog" style="background:#fefffe;">
            <div class="am-modal-hd">
                <span style="font-size: 14px;position: absolute;left:12px;top:7px;">新增话题</span>
                <a id="euModalClose" href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd am-form tpl-form-line-form">
                <div class="am-form-group" style="margin-top:30px;">
                    <label class="am-u-sm-4 am-form-label">话题名称</label>
                    <div class="am-u-sm-8">
                        <input type="text" id="talk" class="tpl-form-input" placeholder="请输入话题名称 前后不要加 # 号">
                    </div>
                </div>
                <div class="am-form-group" style="margin-top:10px;">
                    <label class="am-u-sm-4 am-form-label">排序</label>
                    <div class="am-u-sm-8">
                        <input type="number" id="scores" class="tpl-form-input" oninput="digitalCheck(this);" value="0">
                    </div>
                </div>
                <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top:10px;">
                    <button type="button" class="confirm-btn" onclick="sendGifts();">保存</button>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    var brandNew = function () {
        $('#talk').val('');
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var supre = function (asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            var repair = exalter(asyId, dalue);
            if (repair.code > 0) {
                layer.msg(repair.msg, {icon: 1, time: 800});
                $(domId).attr('data-score', dalue);
            } else {
                layer.msg(repair.msg, {icon: 5, time: 1600});
            }
        }
    }

    var exerox = function (obj) {
        var linkUrl = $(obj).children('input').eq(0).val();
        var oInput = document.createElement('input');
        oInput.value = linkUrl;
        document.body.appendChild(oInput);
        oInput.select(); // 选择对象
        var carried = document.execCommand("Copy"); // 执行浏览器复制命令
        oInput.className = 'oInput';
        oInput.style.display = 'none';
        if (carried) {
            layer.alert('\u94fe\u63a5\u5730\u5740\u5df2\u6210\u529f\u590d\u5236\u5230\u526a\u8d34\u677f\uff0c\u8bf7\u4f7f\u7528\u9f20\u6807\u53f3\u952e\u6216\u952e\u76d8\u7684\u0020\u0043\u0074\u0072\u006c\u002b\u0056\u0020\u7ec4\u5408\u952e\u0020\u8fdb\u884c\u7c98\u8d34\uff01', {'title': '\u7cfb\u7edf\u63d0\u793a'});
        } else {
            layer.alert('\u590d\u5236\u94fe\u63a5\u5730\u5740\u5931\u8d25\uff0c\u8bf7\u6839\u636e\u8df3\u8f6c\u94fe\u63a5\u4e0b\u65b9\u63d0\u793a\u624b\u52a8\u66f4\u6539\uff01', {'title': '\u7cfb\u7edf\u63d0\u793a'});
        }
    }

    var exalter = function (asyId, dalue) {
        var straw = {};
        $.ajax({
            type: "post",
            url: "{:url('compass/themeSort')}",
            data: {
                asyId: asyId,
                dalue: dalue
            },
            async: false,
            success: function (data) {
                straw = data;
            }
        });
        return straw;
    }


    var sendGifts = function () {
        var talk = $.trim($('#talk').val());
        if (talk == '') {
            layer.msg('新增话题名称不能为空');
            return;
        }
        talk = '#' + talk + '#';
        var scores = $('#scores').val();
        $.ajaxSettings.async = false;
        $.post("{:url('compass/pressTheme')}", {'talk': talk, 'scores': scores}, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000});
            }
        }, 'json');
        $.ajaxSettings.async = true;
    }

    var themelintDel = function (tgid) {
        layer.confirm('您确定要删除此条话题吗？<br>( <span style="color:red;">删除后用户将无法在小程序中创建此话题</span> )', {
            btn: ['确定', '取消'], "title": "提示"
        }, function () {
            $.ajaxSettings.async = false;
            $.post("{:url('compass/themeRemove')}", {'tgid': tgid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    var themelintRepeat = function (tgid) {
        layer.confirm('您确定要恢复此条话题吗？', {
            btn: ['确定', '取消'], "title": "提示"
        }, function () {
            $.ajaxSettings.async = false;
            $.post("{:url('compass/themeRepeat')}", {'tgid': tgid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }


    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val()).replace('#', '');
        if (fz_name) {
            location.href = "{:url('compass/theme')}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('compass/theme')}&page={$page}";
        }
    }

</script>
{/block}