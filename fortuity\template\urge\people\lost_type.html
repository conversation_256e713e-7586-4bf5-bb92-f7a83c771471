{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.customize-span,.customize-span:hover{padding:3px 10px;color:black !important;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;}a.am-btn.am-text-secondary{color:#3bb4f2 !important;}a.am-btn.am-text-secondary:hover{background:#c2c2c2 !important;color:#444 !important;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 物品分类
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索分类名称...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <a href="{:url('people/new_lost_type')}" class="customize-span">
                            <span class="am-icon-adn"></span> 新增分类
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g" style="margin-top: 10px;">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th class="text-center" width="25%">排序</th>
                            <th class="text-center" width="25%">分类名称</th>
                            <th class="text-center" width="25%">状态</th>
                            <th class="text-center" width="25%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="text-center">
                                <div style="width: 100%;height: 100%;display: flex;justify-content: center;">
                                    <input type="text" class="scfixed" id="sfixed-{$vo.id}" value="{$vo.sort}" data-sort="{$vo.sort}" style="width: 50px;margin-top: 8px;" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                                </div>
                            </td>
                            <td class="text-center">{$vo.name}</td>
                            <td class="text-center">
                                {if $vo.status == 0}
                                <span style="color: red;">隐藏</span>
                                {else}
                                <span style="color: lightgreen;">正常</span>
                                {/if}
                            </td>
                            <td class="text-center">
                                <div class="am-btn-toolbar" style="display: flex;justify-content: center;align-items: center;">
                                    <div class="am-btn-group am-btn-group-xs">
                                        <a href="{:url('people/edit_lost_type')}&fid={$vo.id}" class="am-btn am-btn-default am-btn-xs am-text-secondary" style="background: #fff;">
                                            <span class="am-icon-pencil-square-o"></span> 编辑
                                        </a>
                                        <button type="button" class="am-btn am-btn-default am-btn-xs am-text-danger" onclick="lostTypeDel('{$vo.id}','{$vo.name}');">
                                            <span class="am-icon-trash-o"></span>
                                            删除
                                        </button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var supre = function (asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-sort');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue !== daioe) {
            var repair = exalter(asyId, dalue);
            if (repair.code > 0) {
                layer.msg(repair.msg, {icon: 1, time: 800});
                $(domId).attr('data-sort', dalue);
            } else {
                layer.msg(repair.msg, {icon: 5, time: 1600});
            }
        }
    }

    var exalter = function (asyId, dalue) {
        var straw = [];
        $.ajax({
            type: "post",
            url: "{:url('people/lost_type_type_sort')}",
            data: {
                asyId: asyId,
                dalue: dalue
            },
            async: false,
            success: function (data) {
                straw = data;
            }
        });
        return straw;
    }


    var lostTypeDel = function (fid, lname) {
        var shint = '您确定要删除 <span style="color: red;">' + lname + '</span> 这条数据吗？';
        layer.confirm(shint, {
            btn: ['确定', '取消'], 'title': '提示'
        }, function () {
            $.post("{:url('people/del_lost_type')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('people/lost_type')}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('people/lost_type')}&page={$page}";
        }
    }

</script>
{/block}