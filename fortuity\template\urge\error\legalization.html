<!doctype html>
<head>
    <meta charset="utf-8">
    <title>站点未授权</title>
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="stylesheet" href="./static/error/css/style.css">
    <link rel="stylesheet" href="./static/error/css/blue.css">
    <style>.style-1 {color: #FF0000}</style>
</head>
<body>

<div id="error-container">
    <div id="error">
        <div id="pacman"></div>
    </div>
    <div id="container">
        <div id="title">
            <h1>对不起, 您当前的站点暂未授权!</h1>
        </div>
        <div id="content">
            <div class="left">
                <p class="no-top">&nbsp;&nbsp;&nbsp;可能是如下原因引起了这个错误:</p>
                <ul>
                    <li>&nbsp;&nbsp;&nbsp;{$depressed}</li>
                    <li>&nbsp;&nbsp;&nbsp;购买<!--正版-->软件后未及时联系客服</li>
                    <li>&nbsp;&nbsp;&nbsp;其他原因...</li>
                </ul>
                <div class="clearfix"></div>
            </div>
            <div class="right">
                <p class="no-top">推荐您通过相关渠道联系客服进行授权：</p>
                <ul class="links">
                    <li><a href="javascript:void(0);" onclick="retakeCache();">» 清除缓存并重试</a></li>
                </ul>
                <div class="clearfix"></div>
            </div>
            <div class="clearfix"></div>
        </div>
        <div id="footer">
            <!--Copyright © 2023 YuLuoNetWork. All Rights Reserved.-->
        </div>
    </div>
</div>
<script src="./assets/js/jquery.min.js"></script>
<script src="./static/error/js/script.js"></script>
<script src="./static/layer/layer.js"></script>
<script>
    function retakeCache() {
        $.get("{:url('index/purgeCache')}", function () {
            layer.msg('缓存清理完成', {time: 2000, icon: 1}, function () {
                location.reload();
            });
        });
    }
</script>
</body>
</html>