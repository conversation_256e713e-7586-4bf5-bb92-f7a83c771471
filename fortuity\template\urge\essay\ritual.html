{extend name="/base"/}
{block name="main"}
<style>.am-modal-hd .am-close{top:-5px;right:-3px;}.w-e-menu{font-size:12px;}.w-e-text,.w-e-text-container{height:300px !important;}.tpl-portlet-components{background:#fff;padding:25px;border-radius:8px;box-shadow:0 2px 12px rgba(0,0,0,0.05);}.portlet-title{margin-bottom:20px;border-bottom:1px solid #eee;padding-bottom:12px;}.portlet-title .caption{font-size:18px;color:#333;}.portlet-title .am-icon-cog{color:#3bb4f2;}.am-form-group{position:relative;margin-bottom:20px;padding-bottom:20px;border-bottom:1px solid #f0f0f0;}.am-form-group:last-child{border-bottom:none;margin-bottom:0;padding-bottom:0;}.am-form-label{font-weight:500;color:#333;padding-top:6px;font-size:13px;}.am-form input[type="radio"]{margin-right:5px;}.am-form input[type="number"]{height:36px;border-radius:4px;border:1px solid #ddd;padding:0 10px;width:100%;}.am-form small{margin-top:4px;font-size:12px;color:#666;}.am-form small[style*="color:red"]{color:#ff5252 !important;}.am-form textarea{border:1px solid #ddd;border-radius:4px;padding:10px;width:100%;}.am-btn-primary{background:#3bb4f2;border-color:#3bb4f2;padding:8px 24px;font-size:14px;border-radius:4px;transition:all 0.3s;}.am-btn-primary:hover{background:#2798d8;border-color:#2798d8;}.radio-group{display:flex;gap:15px;margin-bottom:4px;height:36px;align-items:center;}.radio-group label{display:flex;align-items:center;cursor:pointer;font-weight:normal;color:#666;font-size:14px;line-height:36px;height:36px;}.radio-group input[type="radio"]{margin-top:0;margin-bottom:0;vertical-align:middle;margin-right:4px;}#detail{border:1px solid #ddd;border-radius:4px;margin-bottom:6px;}.number-input-group{position:relative;display:flex;align-items:center;margin-bottom:4px;}.number-input-group input{flex:1;}.number-input-group label{margin-left:10px;color:#999;font-weight:normal;font-size:16px;}.tax-description{margin-top:6px;padding:8px;background:#f8f8f8;border-radius:4px;line-height:1.6;}.tax-description span{display:block;margin-top:3px;}.w-e-toolbar{background:#fafafa !important;border-bottom:1px solid #eee !important;}.w-e-text-container{border-color:#eee !important;}@media screen and (max-width:768px){.content-wrapper{padding:10px;}.tpl-portlet-components{padding:15px;}.am-u-sm-3,.am-u-sm-7,.am-u-sm-9{width:100%;}}.form-textarea{height:400px !important;padding:15px;font-size:14px;line-height:1.6;border:1px solid #ddd;border-radius:4px;background:#fff;resize:none;transition:border-color 0.3s;margin-bottom:4px;}.form-textarea:focus{border-color:#3bb4f2;outline:none;box-shadow:0 0 0 2px rgba(59,180,242,0.1);}.am-form-group:has(.form-textarea){margin-bottom:30px;}.am-form-label{padding-top:8px;}.form-submit{display:flex;justify-content:center;padding:20px 0;}.form-submit .am-btn{min-width:120px;}.content-wrapper{width:100%;min-height:100vh;background-color:#f5f7fa;padding:20px;margin-bottom:20px;}.tpl-portlet-components{max-width:900px;width:100%;margin:0 auto;background:#fff;padding:25px;border-radius:8px;box-shadow:0 2px 12px rgba(0,0,0,0.05);}.am-form-horizontal{max-width:900px;margin:0 auto;}.am-u-sm-3{width:22%;}.am-u-sm-7{width:60%;}.am-u-sm-9{width:78%;}.number-input-group input{max-width:120px;}.editor-container,.form-textarea{background:#fafafa;border:1px solid #eee;}</style>
<div class="content-wrapper">
    <div class="tpl-portlet-components">
        <div class="portlet-title">
            <div class="caption font-green bold">
                <span class="am-icon-cog"></span> 帖子设置
            </div>
        </div>
        <div class="tpl-block">
            <div class="am-g tpl-amazeui-form">
                <div class="am-u-sm-12 am-u-md-12">
                    <div class="am-form am-form-horizontal">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">发帖自动审核</label>
                            <div class="am-u-sm-9">
                                <div class="radio-group">
                                    <label>
                                        <input type="radio" name="review" value="1" {if $list.auto_review==1}checked{/if}> 开启
                                    </label>
                                    <label>
                                        <input type="radio" name="review" value="0" {if $list.auto_review==0}checked{/if}> 关闭
                                    </label>
                                </div>
                                <small style="color:red;">开启后您无需再手动审核 系统将自动通过所有用户发布的帖子</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">发帖次数限制</label>
                            <div class="am-u-sm-9">
                                <input type="number" id="limit" value="{$list.number_limit}" oninput="grender(this);">
                                <small>每日发帖次数限制 0 为不限制</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">回帖自动审核</label>
                            <div class="am-u-sm-9">
                                <div class="radio-group">
                                    <label>
                                        <input type="radio" name="replyReview" value="1" {if $list.reply_auto_review==1}checked{/if}> 开启
                                    </label>
                                    <label>
                                        <input type="radio" name="replyReview" value="0" {if $list.reply_auto_review==0}checked{/if}> 关闭
                                    </label>
                                </div>
                                <small style="color:red;">开启后您无需再手动审核 系统将自动通过所有用户回复的帖子</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">回帖次数限制</label>
                            <div class="am-u-sm-9">
                                <input type="number" id="replyLimit" value="{$list.reply_number_limit}" oninput="grender(this);">
                                <small>每日单个帖子用户最多回复次数 0 为不限制</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">评论自动审核</label>
                            <div class="am-u-sm-9">
                                <div class="radio-group">
                                    <label>
                                        <input type="radio" name="discussReview" value="1" {if $list.discuss_auto_review==1}checked{/if}> 开启
                                    </label>
                                    <label>
                                        <input type="radio" name="discussReview" value="0" {if $list.discuss_auto_review==0}checked{/if}> 关闭
                                    </label>
                                </div>
                                <small style="color:red;">开启后您无需再手动审核 系统将自动通过所有用户评论的回复</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">评论次数限制</label>
                            <div class="am-u-sm-9">
                                <input type="number" id="discussLimit" value="{$list.discuss_number_limit}" oninput="grender(this);">
                                <small>每日单个帖子用户最多评论次数 0 为不限制</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">付费帖子自动审核</label>
                            <div class="am-u-sm-9">
                                <div class="radio-group">
                                    <label>
                                        <input type="radio" name="buyPaperReview" value="1" {if $list.buy_paper_auto_review==1}checked{/if}> 开启
                                    </label>
                                    <label>
                                        <input type="radio" name="buyPaperReview" value="0" {if $list.buy_paper_auto_review==0}checked{/if}> 关闭
                                    </label>
                                </div>
                                <small style="color:red;">开启后您无需再手动审核 系统将自动通过所有用户发布的付费帖子</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">发布付费帖次数限制</label>
                            <div class="am-u-sm-9">
                                <input type="number" id="buyPaperLimit" value="{$list.buy_paper_number_limit}" oninput="grender(this);">
                                <small>发布付费帖次数限制 0 为不限制</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">平台扣除付费帖子税率</label>
                            <div class="am-u-sm-9">
                                <div class="number-input-group">
                                    <input type="number" id="buyPaperTaxing" value="{$list.buy_paper_taxing*100}" oninput="grender(this);">
                                    <label>%</label>
                                </div>
                                <div class="tax-description">
                                    <small>
                                        用户获得付费帖子收益计算方式（ 例如当前税率为 50% ）
                                        <!--
                                        获得{$defaultNavigate.confer} ( 税前 ) = 礼物价格 ( {$defaultNavigate.currency} ) * 10
                                        -->
                                        <span>收益{$defaultNavigate.confer} ( 税后 ) = [ 收益{$defaultNavigate.confer} - 收益{$defaultNavigate.confer} * 50% ( 税率 ) ]</span>
                                        <span style="color: red;">用户最终所获得的收益积分将保留小数点后两位，其余四舍五入</span>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">帖子内容字体大小</label>
                            <div class="am-u-sm-9">
                                <div class="number-input-group">
                                    <input type="number" id="tractateFontSize" value="{$list.tractate_font_size}" oninput="grender(this);">
                                    <label>px</label>
                                </div>
                                <small>帖子内容字体大小 默认大小14px</small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">论坛声明显示开关</label>
                            <div class="am-u-sm-9">
                                <div class="radio-group">
                                    <label>
                                        <input type="radio" name="isShowForumDeclaration" value="1" {if $list.is_show_forum_declaration==1}checked{/if}> 开启
                                    </label>
                                    <label>
                                        <input type="radio" name="isShowForumDeclaration" value="0" {if $list.is_show_forum_declaration==0}checked{/if}> 关闭
                                    </label>
                                </div>
                                <small style="color:red;">开启后将显示论坛声明</small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">论坛声明</label>
                            <div class="am-u-sm-9">
                                <div id="detail" class="editor-container">{$list.forum_declaration}</div>
                                <small style="color: red;">请填写论坛声明 ( 帖子内展示 )</small>
                                <span id="customizeGallery" style="display:none;" onclick="cuonice();"></span>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label for="notice" class="am-u-sm-3 am-form-label">发帖须知</label>
                            <div class="am-u-sm-9">
                                <textarea id="notice" class="form-textarea">{$list.notice}</textarea>
                                <small style="color: red;">请填写发帖须知 ( 用户发帖需要遵守的规则 )</small>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <div class="form-submit">
                                <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js?time={:time()}"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css?time={:time()}">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js?time={:time()}"></script>
<script>

    var E = window.wangEditor;
    var editor = new E('#detail');
    editor.customConfig.uploadImgServer = true;
    editor.create();
    E.fullscreen.init('#detail');

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
    }


    var grender = function (obj) {
        obj.value = Number(obj.value.match(/^\d+(?:\.\d{0,0})?/));
    }

    var excheck = function (notice,buyPaperTaxing) {
        if (notice === '' || notice === 'undefined' || notice == null) {
            layer.msg('发帖须知不能为空');
            return false;
        }
        if (buyPaperTaxing >= 100) {
            layer.msg('平台扣除付费帖子税率不能大于等于100%');
            return false;
        }
        return true;
    }

    var holdSave = function () {

        var setData = {};

        setData['uplid'] = '{$list.id}';

        setData['review'] = $.trim($("[name='review']:checked").val());
        setData['limit'] = $('#limit').val();

        setData['replyReview'] = $.trim($("[name='replyReview']:checked").val());
        setData['replyLimit'] = $('#replyLimit').val();

        setData['discussReview'] = $.trim($("[name='discussReview']:checked").val());
        setData['discussLimit'] = $('#discussLimit').val();

        setData['buyPaperReview'] = $.trim($("[name='buyPaperReview']:checked").val());
        setData['buyPaperLimit'] = $.trim($('#buyPaperLimit').val());

        setData['buyPaperTaxing'] = Number($.trim($('#buyPaperTaxing').val()));

        setData['tractateFontSize'] = Number($.trim($('#tractateFontSize').val()));

        setData['isShowForumDeclaration'] = $.trim($("[name='isShowForumDeclaration']:checked").val());

        setData['forumDeclaration'] = $.trim(this.editor.txt.html());
        setData['notice'] = $.trim($('#notice').val());

        if (excheck(setData['notice'], setData['buyPaperTaxing'])) {
            $.post("{:url('essay/ritual')}", setData, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        }
    }
</script>
{/block}